<template>
    <q-header elevated class="main-header">
        <q-toolbar class="main-header-toolbar">
            <div class="full-width row justify-between items-center">
                <div class="col-4 justify-center items-start">
                    <div class="row justify-start items-center">
                        <q-btn
                            flat
                            @click="toggleDrawer"
                            round
                            dense
                            icon="menu"
                            :aria-label="$t('toggleDrawerDescription')"
                        />
                        <img class="main-logo" alt="main logo" :src="logoPath()" @click="gotoRoute('/')" />
                    </div>
                </div>
                <div class="col-4 justify-center items-center">
                    <div class="text-center main-title">
                        <router-link to="/">{{ $t('applicationTitle') }}</router-link>
                    </div>
                </div>
                <div class="col-4 justify-center items-end">
                    <main-menu />
                </div>
            </div>
        </q-toolbar>
    </q-header>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import config from '@/config';
import MainMenu from './MainMenu.vue';

export default defineComponent({
    name: 'main-header',
    components: {
        MainMenu
    },
    emits: ['toggleDrawer'],
    methods: {
        logoPath(): string {
            const culture: string = config.locale?.substring(0, 2) || 'el';
            return `assets/img/aade-logo-${culture}.svg`;
        },
        toggleDrawer() {
            this.$emit('toggleDrawer');
        },
        gotoRoute(path: string) {
            if (!path) return;
            if (this.$route?.path === path) return;
            this.$nextTick(() => this.$router.push({ path }));
        }
    }
});
</script>

<style>
.main-header {
    padding: 0 10px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
}

.main-header .main-title,
.main-header .main-title a,
.main-header .main-title a:visited,
.main-header .main-title a:focus {
    text-decoration: none;
    color: white;
    font-weight: bold;
}

.main-header .main-title {
    padding: 0 16px;
}
</style>
