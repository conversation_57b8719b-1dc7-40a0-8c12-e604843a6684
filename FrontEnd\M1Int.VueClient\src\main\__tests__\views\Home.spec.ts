import { shallowMount, VueWrapper } from '@vue/test-utils';
import HomeView from '@/main/views/Home.vue';
import { commonComponentMocks, commonComponentPlugins } from '@/mocks/commonComponentMocks';

describe('HomeView', () => {
    let wrapper: VueWrapper;

    beforeEach(() => {
        wrapper = shallowMount(HomeView, {
            global: {
                mocks: commonComponentMocks,
                plugins: commonComponentPlugins
            }
        });
    });

    afterEach(() => {});

    it('should mount', () => {
        expect(wrapper).toBeTruthy();
        expect(wrapper.vm).toBeTruthy();
        expect(wrapper.vm.$el).toBeTruthy();
    });

    it('element should be valid', () => {
        expect(wrapper.vm.$el.classList).toBeTruthy();
        expect(Array.from(wrapper.vm.$el.classList)).toContain('home-view');
    });

    it('should have valid $t property', () => {
        expect(wrapper.vm.$t).toBeTruthy();
        expect(typeof wrapper.vm.$t).toEqual('function');
    });

    it('$t should return valid result', () => {
        expect(wrapper.vm.$t('HomeView')).toEqual('HomeView');
    });

    it('should have valid $alert property', () => {
        expect((wrapper.vm as any).$alert).toBeTruthy();
    });
});
