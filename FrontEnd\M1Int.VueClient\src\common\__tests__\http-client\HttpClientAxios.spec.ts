import { HttpClientAxios } from '@/common/http-client/HttpClientAxios';

describe('HttpClientAxios', () => {
    let client: HttpClientAxios;

    beforeEach(() => {
        client = new HttpClientAxios();
    });

    it('client is valid', () => {
        expect(client).toBeTruthy();
    });

    it('client has baseurl property', () => {
        expect(client.baseUrl).not.toBeUndefined();
        expect(client.baseUrl).not.toBeNull();
    });
});
