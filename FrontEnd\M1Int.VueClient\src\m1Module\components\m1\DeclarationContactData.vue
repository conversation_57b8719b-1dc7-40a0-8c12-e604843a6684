<template>
    <div v-if="declaration" class="full-width q-px-xs row justify-center items-center view-declaration-data-content">
        <div v-if="!declaration.m1ResidenseAbroad" class="full-width row declaration-data-container">
            <!-- <div class="col-12 col-sm-12 col-md-3 q-px-md">
                                <field-display
                                    class="full-width"
                                    :modelObject="declaration"
                                    fieldId="m1ResidenseKind"
                                    :value="$t(`residenseKind${declaration.m1ResidenseKindText}`)"
                                    :fieldTitle="$t('residenseKind')"
                                    :dense="true"
                                />
                            </div>  -->
            <div class="col-12 col-sm-12 col-md-2 q-px-md">
                <field-display
                    class="full-width"
                    :modelObject="declaration"
                    fieldId="m1Street"
                    :fieldTitle="$t('street')"
                    :dense="true"
                />
            </div>
            <div class="col-12 col-sm-12 col-md-2 q-px-md">
                <field-display
                    class="full-width"
                    :modelObject="declaration"
                    fieldId="m1StreetNo"
                    :fieldTitle="$t('number')"
                    :dense="true"
                />
            </div>
            <div class="col-12 col-sm-12 col-md-2 q-px-md">
                <field-display
                    class="full-width"
                    :modelObject="declaration"
                    fieldId="postCode"
                    :fieldTitle="$t('zip')"
                    :dense="true"
                />
            </div>
            <div class="col-12 col-sm-12 col-md-2 q-px-md">
                <field-display
                    class="full-width"
                    :modelObject="declaration"
                    fieldId="m1PostCodeMunicipality"
                    :fieldTitle="$t('municipality')"
                    :dense="true"
                />
            </div>
            <div class="col-12 col-sm-12 col-md-4 q-px-md">
                <field-display
                    class="full-width"
                    :modelObject="declaration"
                    fieldId="m1PostCodePrefecture"
                    :fieldTitle="$t('prefecture')"
                    :dense="true"
                />
            </div>
        </div>
        <div v-if="declaration.m1ResidenseAbroad" class="full-width row declaration-data-container">
            <!-- <div class="col-12 col-sm-12 col-md-3 q-px-md">
                                <field-display
                                    class="full-width"
                                    :modelObject="declaration"
                                    fieldId="m1ResidenseKind"
                                    :value="$t(`residenseKind${declaration.m1ResidenseKindText}`)"
                                    :fieldTitle="$t('residenseKind')"
                                    :dense="true"
                                />
                            </div>  -->
            <div class="col-12 col-sm-12 col-md-2 q-px-md">
                <field-display
                    class="full-width"
                    :modelObject="declaration"
                    fieldId="m1ResidenseAbroadDescription"
                    :fieldTitle="$t('residenseAbroad')"
                    :dense="true"
                />
            </div>
            <div class="col-12 col-sm-12 col-md-4 q-px-md">
                <field-display
                    class="full-width"
                    :modelObject="declaration"
                    fieldId="m1AfmAbroad"
                    :fieldTitle="$t('afmAbroad')"
                    :dense="true"
                />
            </div>
            <div class="col-12 col-sm-12 col-md-2 q-px-md">
                <field-display
                    class="full-width"
                    :modelObject="declaration"
                    fieldId="m1Street"
                    :fieldTitle="$t('street')"
                    :dense="true"
                />
            </div>
            <div class="col-12 col-sm-12 col-md-2 q-px-md">
                <field-display
                    class="full-width"
                    :modelObject="declaration"
                    fieldId="m1StreetNo"
                    :fieldTitle="$t('number')"
                    :dense="true"
                />
            </div>
            <div class="col-12 col-sm-12 col-md-2 q-px-md">
                <field-display
                    class="full-width"
                    :modelObject="declaration"
                    fieldId="postCode"
                    :fieldTitle="$t('zip')"
                    :dense="true"
                />
            </div>
        </div>
        <div class="full-width row declaration-data-container">
            <div class="col-12 col-sm-12 col-md-4 q-px-md">
                <field-display
                    class="full-width"
                    :modelObject="declaration"
                    fieldId="m1FullDoyDescription"
                    :fieldTitle="$t('doy')"
                    :dense="true"
                />
            </div>
            <div class="col-12 col-sm-12 col-md-4 q-px-md">
                <field-display
                    class="full-width"
                    :modelObject="declaration"
                    fieldId="m1PhoneNumber"
                    :fieldTitle="$t('phone')"
                    :dense="true"
                />
            </div>
            <div class="col-12 col-sm-12 col-md-4 q-px-md">
                <field-display
                    class="full-width"
                    :modelObject="declaration"
                    fieldId="m1Email"
                    :fieldTitle="$t('email')"
                    :dense="true"
                />
            </div>
        </div>
        <div
            v-if="declaration.m1ResidenseAbroad && declaration.m1NotificationChoice"
            class="full-width row declaration-data-container"
        >
            <div class="col-12 col-sm-12 col-md-12 q-px-md">
                <field-display
                    class="full-width"
                    :modelObject="declaration"
                    fieldId="m1NotificationChoice"
                    :value="$t(`notificationChoice${declaration.m1NotificationChoiceText}`)"
                    :fieldTitle="$t('notificationChoice')"
                    :dense="true"
                />
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { Declaration } from '../../models/m1/Entities/Declaration';
import FieldDisplay from '@/common/components/form/FieldDisplay.vue';

export default defineComponent({
    name: 'declaration-contact-data',
    components: {
        FieldDisplay
    },
    props: {
        declaration: {
            type: Declaration,
            default: null
        }
    }
});
</script>

<style>
.view-declaration-data .field-group-header {
    padding: 6px 12px;
    background-color: var(--palette-primary-dark);
    color: white;
    border-radius: 6px;
}
.view-declaration-data .q-expansion-item .q-expansion-item__toggle-icon {
    color: white;
}
.view-declaration-data .declaration-link {
    color: var(--palette-primary-medium);
    font-weight: 600;
    cursor: pointer;
}
.view-declaration-data .success--text textarea {
    color: var(--palette-success) !important;
}
.view-declaration-data .warning--text textarea {
    color: var(--palette-warning) !important;
}
.view-declaration-data .error--text textarea {
    color: var(--palette-error) !important;
}
</style>
