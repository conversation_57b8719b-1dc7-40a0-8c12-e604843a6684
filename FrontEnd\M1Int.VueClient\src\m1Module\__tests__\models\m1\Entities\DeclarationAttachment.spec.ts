import { DeclarationAttachment } from '@/m1Module/models/m1/Entities/DeclarationAttachment';

describe('DeclarationAttachment', () => {
    let entity: DeclarationAttachment;

    beforeEach(() => {
        entity = new DeclarationAttachment();
    });

    it('should be valid', () => {
        expect(entity).toBeTruthy();
    });

    it('should have valid id', () => {
        expect(entity?.id).toBeUndefined();
    });

    it('should have valid declarationId', () => {
        expect(entity?.declarationId).toBeUndefined();
    });
});
