<template>
    <q-file
        :ref="elementId"
        :id="elementId"
        :for="elementId"
        :name="elementName"
        v-model="modelObject[fieldId]"
        :error-message="validationMessage"
        :error="isDirty && isInvalid"
        :noErrorIcon="true"
        :class="classResolved"
        :label="label"
        :placeholder="placeholder"
        :hint="hint"
        :hideHint="hideHint"
        :outlined="outlined"
        :filled="filled"
        :borderless="borderless"
        :rounded="rounded"
        :bottomSlots="bottomSlots"
        :hideBottomSpace="!bottomSlots"
        :dense="dense"
        :clearable="clearable && !disabled && !readonly"
        clear-icon="close"
        :disable="disabled"
        :readonly="readonly"
        :autofocus="autofocus && !disabled && !readonly"
        :accept="accept"
        :multiple="multiple"
        :append="append"
        :useChips="chips"
        :counter="counter"
        :maxFiles="multiple ? maxFiles : 1"
        :maxFileSize="maxFileSize * 1024"
        :maxTotalSize="maxTotalSize * 1024"
        @update:model-value="valueChanged"
        @rejected="rejectedFiles"
    >
        <template v-slot:selected="{ files }">
            <slot name="selected">
                <div class="files-container">
                    <div v-for="(file, index) in files" :key="index" class="file-container">
                        <q-chip
                            v-if="chips"
                            size="14px"
                            :clickable="true"
                            :removable="removable && !disabled && !readonly"
                            :v-model="file"
                            text-color="white"
                            class="chip bg-primary"
                            :label="file.name"
                            :title="file.name"
                            @click="clickFile($event, file, index)"
                            @remove="removeFile(file, index)"
                        />
                        <div v-else>
                            <span v-if="index > 0">,&nbsp;</span>
                            <span>{{ file.name }}</span>
                        </div>
                    </div>
                </div>
            </slot>
        </template>
        <template v-slot:label>
            <field-label :label="label" :hint="labelInfo" :isTooltip="isLabelInfoTooltip" />
        </template>
        <template v-slot:before v-if="beforeIcon">
            <q-icon :name="beforeIcon" @click="handleIconClick('click:before')" />
        </template>
        <template v-slot:after v-if="afterIcon">
            <q-icon :name="afterIcon" @click="handleIconClick('click:after')" />
        </template>
        <template v-slot:prepend v-if="prependIcon">
            <q-icon :name="prependIcon" @click="handleIconClick('click:prepend')" />
        </template>
        <template v-slot:append v-if="appendIcon">
            <q-icon :name="appendIcon" @click="handleIconClick('click:append')" />
        </template>
    </q-file>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import FieldEditTextCommon from './FieldEditTextCommon';
import { getLocaleNumberText, openNewUrl } from '@/common/utilities';
import { QRejectedEntry } from 'quasar';

export default defineComponent({
    name: 'field-file-picker',
    mixins: [FieldEditTextCommon],
    props: {
        accept: {
            type: String,
            default: '*'
        },
        multiple: {
            type: Boolean,
            default: true
        },
        removable: {
            type: Boolean,
            default: true
        },
        append: {
            type: Boolean,
            default: true
        },
        chips: {
            type: Boolean,
            default: true
        },
        counter: {
            type: Boolean,
            default: false
        },
        maxFiles: {
            type: Number,
            default: 10
        },
        maxFileSize: {
            type: Number,
            default: 4096
        },
        maxTotalSize: {
            type: Number,
            default: 8192
        },
        onClickFileOpen: {
            type: Boolean,
            default: true
        },
        beforeIconClickOpen: {
            type: Boolean,
            default: true
        },
        afterIconClickOpen: {
            type: Boolean,
            default: true
        },
        prependIconClickOpen: {
            type: Boolean,
            default: true
        },
        appendIconClickOpen: {
            type: Boolean,
            default: true
        }
    },
    /*
    computed: {
        files(): Array<File> {
            let model: any = this.modelObject;
            if (this.fieldId) model = this.modelObject[this.fieldId];
            if (!model) return [];
            if (this.multiple) return model;
            else return [model];
        }
    },
    */
    methods: {
        getRejectionMessage(entry: QRejectedEntry): string {
            if (!entry?.failedPropValidation) return this.$t('attachmentRejected');
            if (entry.failedPropValidation === 'accept')
                return this.$t('attachmentInvalidType', [entry.file.name, this.accept]);
            else if (entry.failedPropValidation === 'max-files')
                return this.$t('attachmentsMaxFilesNumberExceeded', [this.maxFiles]);
            else if (entry.failedPropValidation === 'max-file-size')
                return this.$t('attachmentMaxFileSizeExceeded', [
                    getLocaleNumberText(entry.file?.size / 1024, 0),
                    getLocaleNumberText(this.maxFileSize, 0)
                ]);
            else if (entry.failedPropValidation === 'max-total-size')
                return this.$t('attachmentsMaxTotalFilesSizeExceeded', [getLocaleNumberText(this.maxTotalSize, 0)]);
            else return this.$t('attachmentRejected');
        },
        rejectedFiles(rejectedEntries: Array<QRejectedEntry>) {
            let rejectionMessage: string = '';
            if (rejectedEntries.length) {
                rejectedEntries.forEach((entry: QRejectedEntry) => {
                    if (rejectionMessage) rejectionMessage += ', ';
                    rejectionMessage += this.getRejectionMessage(entry);
                });
            }
            if (!rejectionMessage) rejectionMessage = this.$t('attachmentRejected');
            this.$emit('rejectedFiles', rejectionMessage);
        },
        async clickFile(event: Event, file: File, index: number) {
            if (!file) return;
            if (this.onClickFileOpen) {
                if (event) {
                    event.stopPropagation();
                    event.preventDefault();
                }
                const buffer: ArrayBuffer = await file.arrayBuffer();
                const pdfBLOB = new Blob([buffer], { type: file.type });
                const pdfUrl = window.URL.createObjectURL(pdfBLOB);
                setTimeout(() => openNewUrl({ url: pdfUrl }), 200);
            }
            this.$emit('clickFile', file, index);
        },
        removeFile(_file: File, index: number) {
            let model: any = this.modelObject;
            if (this.fieldId) model = this.modelObject[this.fieldId];
            if (!model) return;
            if (this.$refs && this.$refs[this.elementId]) (this.$refs[this.elementId] as any)?.removeAtIndex(index);
            /*
            if (model.length) model.splice(index, 1);
            else model = null;
            */
            this.valueChanged();
        },
        handleIconClick(event: string) {
            const clickOpen: boolean =
                (event === 'click:before' && this.beforeIconClickOpen) ||
                (event === 'click:after' && this.afterIconClickOpen) ||
                (event === 'click:prepend' && this.prependIconClickOpen) ||
                (event === 'click:append' && this.appendIconClickOpen);
            if (clickOpen) {
                const element: any = this.$refs[this.elementId] as any;
                if (element?.$el?.click) element.$el.click();
            }
            this.emitEvent(event);
        }
    }
});
</script>

<style scoped>
.files-container {
    display: flex;
    flex-direction: row;
    justify-content: start;
    align-items: center;
    flex-wrap: wrap;
}
</style>
