import authService from '@/auth/services/AuthService';
import { IHttpClient } from '@/common/http-client/IHttpClient';
import { createHttpClient } from '@/common/http-client/HttpClientFactory';
import { GetDeclarationsOptionsRequest } from '../models/m1/Services/GetDeclarationsOptions/GetDeclarationsOptionsRequest';
import { GetDeclarationsOptionsResponse } from '../models/m1/Services/GetDeclarationsOptions/GetDeclarationsOptionsResponse';
import { GetSubmittedDeclarationsLiteRequest } from '../models/m1/Services/GetSubmittedDeclarationsLite/GetSubmittedDeclarationsLiteRequest';
import { GetSubmittedDeclarationsLiteResponse } from '../models/m1/Services/GetSubmittedDeclarationsLite/GetSubmittedDeclarationsLiteResponse';
import { GetDeclarationReportRequest } from '../models/m1/Services/GetDeclarationReport/GetDeclarationReportRequest';
import { GetDeclarationRequest } from '../models/m1/Services/GetDeclaration/GetDeclarationRequest';
import { GetDeclarationResponse } from '../models/m1/Services/GetDeclaration/GetDeclarationResponse';
import { GetDeclarationAttachmentRequest } from '../models/m1/Services/GetDeclarationAttachment/GetDeclarationAttachmentRequest';
import { ExportSubmittedDeclarationsToCsvRequest } from '../models/m1/Services/ExportSubmittedDeclarationsToCsv/ExportSubmittedDeclarationsToCsvRequest';
import { ExportSubmittedDeclarationsToCsvResponse } from '../models/m1/Services/ExportSubmittedDeclarationsToCsv/ExportSubmittedDeclarationsToCsvResponse';
import { ExportSubmittedDeclarationsToExcelRequest } from '../models/m1/Services/ExportSubmittedDeclarationsToExcel/ExportSubmittedDeclarationsToExcelRequest';
import { GetSubmittedDeclarationsRequest } from '../models/m1/Services/GetSubmittedDeclarations/GetSubmittedDeclarationsRequest';
import { GetSubmittedDeclarationsResponse } from '../models/m1/Services/GetSubmittedDeclarations/GetSubmittedDeclarationsResponse';

const httpClient: IHttpClient = createHttpClient();

export interface IM1Service {
    getDeclarationsOptions(request: GetDeclarationsOptionsRequest): Promise<GetDeclarationsOptionsResponse>;
    getDeclaration(request: GetDeclarationRequest): Promise<GetDeclarationResponse>;
    getSubmittedDeclarationsLite(
        request: GetSubmittedDeclarationsLiteRequest
    ): Promise<GetSubmittedDeclarationsLiteResponse>;
    getSubmittedDeclarations(
        request: GetSubmittedDeclarationsRequest
    ): Promise<GetSubmittedDeclarationsResponse>;
    exportSubmittedDeclarationsToCsv(
        request: ExportSubmittedDeclarationsToCsvRequest
    ): Promise<ExportSubmittedDeclarationsToCsvResponse>;
    exportSubmittedDeclarationsToExcel(request: ExportSubmittedDeclarationsToExcelRequest): Promise<any>;
    getDeclarationAttachment(request: GetDeclarationAttachmentRequest): Promise<any>;
    getDeclarationReport(request: GetDeclarationReportRequest): Promise<any>;
}

export class M1Service implements IM1Service {
    public async getDeclarationsOptions(
        request: GetDeclarationsOptionsRequest
    ): Promise<GetDeclarationsOptionsResponse> {
        try {
            const url = 'M1/GetDeclarationsOptions';
            const response: GetDeclarationsOptionsResponse = await httpClient.post<
                GetDeclarationsOptionsResponse,
                GetDeclarationsOptionsRequest
            >(url, request, {
                headers: {
                    Authorization: `Bearer ${authService.authData?.authToken || ''}`
                }
            });
            if (!response?.success || !response?.result) throw new Error('getDeclarationsOptions failed');
            return response;
        } catch (ex: any) {
            console.error(ex);
            throw ex;
        }
    }

    async getDeclaration(request: GetDeclarationRequest): Promise<GetDeclarationResponse> {
        if (!request?.id) throw new Error('getDeclaration => invalid request!');
        const url = 'M1/GetDeclaration';
        const response: GetDeclarationResponse = await httpClient.post<GetDeclarationResponse, GetDeclarationRequest>(
            url,
            request,
            {
                headers: {
                    Authorization: `Bearer ${authService.authData?.authToken || ''}`
                }
            }
        );
        if (!response?.success) throw new Error('getDeclaration failed');
        if (!response?.result) response.result = null;
        return response;
    }

    // async getSubmittedDeclarationsLite(
    //     request: GetSubmittedDeclarationsLiteRequest
    // ): Promise<GetSubmittedDeclarationsLiteResponse> {
    //     try {
    //         if (!(request?.applicantAfm && request?.declarationYear) && !request?.declarationId)
    //             throw new Error('getSubmittedDeclarationsLite => invalid request!');
    //         const url = 'M1/GetSubmittedDeclarationsLite';
    //         const response: GetSubmittedDeclarationsLiteResponse = await httpClient.post<
    //             GetSubmittedDeclarationsLiteResponse,
    //             GetSubmittedDeclarationsLiteRequest
    //         >(url, request, {
    //             headers: {
    //                 Authorization: `Bearer ${authService.authData?.authToken || ''}`
    //             }
    //         });
    //         if (!response?.success || !response?.result) throw new Error('getSubmittedDeclarationsLite failed');
    //         return response;
    //     } catch (ex: any) {
    //         console.error(ex);
    //         throw ex;
    //     }
    // }

    async getSubmittedDeclarationsLite(
        request: GetSubmittedDeclarationsLiteRequest
    ): Promise<GetSubmittedDeclarationsLiteResponse> {
        try {
            // if (!(request?.applicantAfm && request?.declarationYear) && !request?.declarationId)
            if (!request)
                throw new Error('getSubmittedDeclarationsLite => invalid request!');
            const url = 'M1/GetSubmittedDeclarationsLite';
            const response: GetSubmittedDeclarationsLiteResponse = await httpClient.post<
                GetSubmittedDeclarationsLiteResponse,
                GetSubmittedDeclarationsLiteRequest
            >(url, request, {
                headers: {
                    Authorization: `Bearer ${authService.authData?.authToken || ''}`
                }
            });
            if (!response?.success || !response?.result) throw new Error('getSubmittedDeclarationsLite failed');
            return response;
        } catch (ex: any) {
            console.error(ex);
            throw ex;
        }
    }

    async getSubmittedDeclarations(
        request: GetSubmittedDeclarationsRequest
    ): Promise<GetSubmittedDeclarationsResponse> {
        try {
            if (!request)
                throw new Error('getSubmittedDeclarations => invalid request!');
            const url = 'M1/GetSubmittedDeclarations';
            const response: GetSubmittedDeclarationsResponse = await httpClient.post<
                GetSubmittedDeclarationsResponse,
                GetSubmittedDeclarationsRequest
            >(url, request, {
                headers: {
                    Authorization: `Bearer ${authService.authData?.authToken || ''}`
                }
            });
            if (!response?.success || !response?.result) throw new Error('getSubmittedDeclarations failed');
            return response;
        } catch (ex: any) {
            console.error(ex);
            throw ex;
        }
    }

    public async exportSubmittedDeclarationsToCsv(
        request: ExportSubmittedDeclarationsToCsvRequest
    ): Promise<ExportSubmittedDeclarationsToCsvResponse> {
        console.log('exportSubmittedDeclarationsToCsv');
        throw new Error('exportSubmittedDeclarationsToCsv => invalid request!');
        // try {
        //     if (!request?.applicantAfm) throw new Error('exportSubmittedDeclarationsToCsv => invalid request!');
        //     const url = 'M1/ExportSubmittedDeclarationsToCsv';
        //     const response: any = await httpClient.post<any, GetSubmittedDeclarationsLiteRequest>(url, request, {
        //         headers: {
        //             Authorization: `Bearer ${authService.authData?.authToken || ''}`
        //         }
        //     });
        //     if (!response) throw new Error('exportSubmittedDeclarationsToCsv failed');
        //     return response;
        // } catch (ex: any) {
        //     console.error(ex);
        //     throw ex;
        // }
    }

    public async exportSubmittedDeclarationsToExcel(request: ExportSubmittedDeclarationsToExcelRequest): Promise<any> {
        try {
            const url = 'M1/ExportSubmittedDeclarationsToExcel';
            const response: any = await httpClient.post<any, GetSubmittedDeclarationsRequest>(url, request, {
                responseType: 'arraybuffer',
                headers: {
                    Authorization: `Bearer ${authService.authData?.authToken || ''}`
                }
            });
            if (!response) throw new Error('exportSubmittedDeclarationsToExcel failed');
            return response;
        } catch (ex: any) {
            console.error(ex);
            throw ex;
        }
    }

    public async getDeclarationAttachment(request: GetDeclarationAttachmentRequest): Promise<any> {
        try {
            // if (!request?.applicantAfm || !request.year || !request.declarationId || !request.declarationAttachmentId)
            if (!request.declarationId || !request.declarationAttachmentId)
                throw new Error('Invalid getDeclarationAttachment request!');
            const url = 'M1/GetDeclarationAttachment';
            const response: any = await httpClient.post<any, GetDeclarationAttachmentRequest>(url, request, {
                responseType: 'arraybuffer',
                headers: {
                    Authorization: `Bearer ${authService.authData?.authToken || ''}`
                }
            });
            if (!response) throw new Error('getDeclarationAttachment failed');
            return response;
        } catch (ex: any) {
            console.error(ex);
            throw ex;
        }
    }

    public async getDeclarationReport(request: GetDeclarationReportRequest): Promise<any> {
        try {
            if (!request?.id) throw new Error('Invalid getDeclarationReport request!');
            const url = 'reports/GetDeclarationReport';
            const response: any = await httpClient.post<any, GetDeclarationReportRequest>(url, request, {
                responseType: 'arraybuffer',
                headers: {
                    Authorization: `Bearer ${authService.authData?.authToken || ''}`
                }
            });
            if (!response) throw new Error('getDeclarationReport failed');
            return response;
        } catch (ex: any) {
            console.error(ex);
            throw ex;
        }
    }
}

const aadeWAService: IM1Service = new M1Service();
export default aadeWAService;
