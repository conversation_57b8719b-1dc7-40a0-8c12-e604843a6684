
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for auth/services</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> auth/services</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">27.69% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>18/65</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">8.82% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>3/34</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">22.22% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>4/18</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">29.5% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>18/61</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input oninput="onInput()" type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="AuthService.ts"><a href="AuthService.ts.html">AuthService.ts</a></td>
	<td data-value="22" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 22%"></div><div class="cover-empty" style="width: 78%"></div></div>
	</td>
	<td data-value="22" class="pct low">22%</td>
	<td data-value="50" class="abs low">11/50</td>
	<td data-value="6.66" class="pct low">6.66%</td>
	<td data-value="30" class="abs low">2/30</td>
	<td data-value="15.38" class="pct low">15.38%</td>
	<td data-value="13" class="abs low">2/13</td>
	<td data-value="23.91" class="pct low">23.91%</td>
	<td data-value="46" class="abs low">11/46</td>
	</tr>

<tr>
	<td class="file low" data-value="AuthStore.ts"><a href="AuthStore.ts.html">AuthStore.ts</a></td>
	<td data-value="46.66" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 46%"></div><div class="cover-empty" style="width: 54%"></div></div>
	</td>
	<td data-value="46.66" class="pct low">46.66%</td>
	<td data-value="15" class="abs low">7/15</td>
	<td data-value="25" class="pct low">25%</td>
	<td data-value="4" class="abs low">1/4</td>
	<td data-value="40" class="pct low">40%</td>
	<td data-value="5" class="abs low">2/5</td>
	<td data-value="46.66" class="pct low">46.66%</td>
	<td data-value="15" class="abs low">7/15</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2022-12-03T11:52:51.044Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    