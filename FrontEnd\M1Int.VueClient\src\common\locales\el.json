{"language": "Γλώσσα", "english": "Αγγλικ<PERSON>", "greek": "Ελληνικά", "aadeTitle": "Α.Α.Δ.Ε.", "applicationTitle": "Διαχείριση αιτήσεων απόδοσης ΑΦΜ & Κλειδάριθμου", "applicationDescription": "Διαχείριση αιτήσεων απόδοσης ΑΦΜ & Κλειδάριθμου", "version": "Έκδοση", "message": "Μήνυμα", "info": "Πληροφόρηση", "information": "Πληροφόρηση", "success": "Επιτυχία", "warning": "Προειδοποίηση", "danger": "Κίνδυνος", "error": "Σφάλμα", "question": "Ερώτηση", "confirmation": "Επιβεβαίωση", "prompt": "Προτροπή", "progress": "Πρ<PERSON><PERSON><PERSON>ος", "process": "Διεργασία", "pleaseWait": "Παρα<PERSON><PERSON><PERSON><PERSON> περιμένετε", "continue": "Συνέχεια", "close": "Κλείσιμο", "yes": "Ναι", "no": "Όχι", "ok": "Αποδοχή", "cancel": "Ακύρωση", "back": "Πίσω", "return": "Επιστροφή", "refresh": "Ανανέωση", "submit": "Υποβολή", "saveAndAdd": "Αποθήκευση και προσθήκη", "save": "Αποθήκευση", "add": "Προσθήκη", "edit": "Τροποποίηση", "display": "Προβολή", "delete": "Διαγραφή", "rename": "Μετονομασία", "search": "Αναζήτηση", "searchHint": "Πληκτρολογήστε όρο για αναζήτηση", "selection": "Επιλογή", "select": "Επιλογή", "selectAll": "Επιλογ<PERSON> όλων", "addFiles": "Προσθήκη αρχείων", "addFile": "Προσθήκη αρχείου", "selectFiles": "Επιλογή αρχείων", "selectFile": "Επιλογή αρχείου", "addAttachments": "Προσθήκη συνημμένων", "addAttachment": "Προσθήκη συνημμένου", "selectAttachments": "Επιλογή συνημμένων", "selectAttachment": "Επιλογή συνημμένου", "selectFileToUpload": "Επιλέξτε 1 αρχείο για επισύναψη", "selectFilesToUpload": "Επιλέξτε έως {0} αρχεία για επισύναψη", "errorTitle": "Σφάλμα", "errorMessage": "Παρου<PERSON><PERSON><PERSON><PERSON><PERSON>ηκε ένα σφάλμα.", "unauthorizedAccess": "Αποτυχημένη προσπάθεια μη εξουσιοδοτημένης πρόσβασης.", "forbiddenAccess": "Η λειτουργία επιτρέπεται μόνο σε εξουσιοδοτημένους χρήστες.", "formIsInvalid": "Υπάρχουν λάθη στις τιμές των πεδίων.", "fieldIsRequired": "Υποχρεωτικό πεδίο.", "fieldMinLength": "Το πεδίο \"{0}\" πρέπει να έχει μήκος τουλάχιστον {1}.", "fieldMaxLength": "Το πεδίο \"{0}\" δέχεται έως {1} χαρακτήρες.", "fieldMinValue": "Το πεδίο \"{0}\" πρέπει να έχει τιμή >= {1}.", "fieldMaxValue": "Το πεδίο \"{0}\" πρέπει να έχει τιμή <= {1}.", "fieldIsInvalid": "Το πεδίο \"{0}\" δεν έχει αποδεκτή τιμή.", "deleteConfirmation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> επιβεβαιώστε τη διαγραφή.", "discardChangesConfirmation": "Αν κλείσετε τη φόρμα, όλες οι τροποποιήσεις που έχετε κάνει θα χαθούν. Θέλετε σίγουρα να κλείσετε τη φόρμα;", "saveInvalidConfirmation": "Η φόρμα περιέχει πεδία με μη αποδεκτές τιμές. Θέλετε σίγουρα να προχωρήσετε στην αποθήκευση;", "home": "Αρχική", "about": "Σχετικά", "contact": "Επικοινωνία", "form": "Φόρμα", "homeLinkDescription": "Σύνδεσμος στην αρχική σελίδα", "aboutLinkDescription": "Σύνδεσμος στην σελίδα σχετικά", "contactLinkDescription": "Σύνδεσμος στην σελίδα επικοινωνία", "formLinkDescription": "Σύνδεσμος στην σελίδα φόρμα", "toggleDrawerDescription": "Εναλλαγή συρταριού επιλογών", "closeDrawerDescription": "Κλείσιμο συρταριού επιλογών", "authentication": "Ταυτοποίηση", "userAuthentication": "Ταυτοποίηση χρήστη", "registration": "Εγγραφή", "register": "Εγγραφή", "login": "Είσοδος", "logout": "Έξοδος", "user": "<PERSON>ρή<PERSON><PERSON><PERSON>ς", "userName": "Όνομα χρήστη", "password": "<PERSON>ω<PERSON><PERSON><PERSON><PERSON><PERSON> χρήστη", "userAuthenticationSuccess": "Η ταυτοποίηση του χρήστη ολοκληρώθηκε.", "userAuthenticationFailure": "Η ταυτοποίηση του χρήστη απέτυχε.", "userAuthorizationExpired": "Η εξουσιοδότηση του χρήστη {0} έχει λήξει.", "userRegistrationSuccess": "Η εγγραφή του χρήστη ολοκληρώθηκε.", "userRegistrationFailure": "Η εγγραφή του χρήστη απέτυχε.", "userLogoutSuccess": "Ο χρήστης αποσυνδέθηκε επιτυχώς.", "loginWithUsernameAndPassword": "Ε<PERSON><PERSON><PERSON><PERSON>ος με Όνομα Χρήστη και Κλειδί", "userSession": "Συνεδρία χρήστη", "userSessionExpirationMessage": "<div>Η συνεδρία σας θα λήξει σε {0}.</div><div>Θα πρέπει να αποθηκεύσετε όποια εργασία έχετε σε εξέλιξη πριν από τη λήξη της συνεδρίας.</div>", "userSessionHasExpired": "Η συνεδρία σας έχει λήξει.", "userSessionExpiredLoginRequired": "Η συνεδρία σας έχει λήξει. Για να συνεχίσετε να χρησιμοποιείτε την εφαρμογή θα πρέπει να συνδεθείτε ξανά.", "timerCountdown": "Αντίστροφη μέτρηση", "timerTimeLeftMessage": "Απομένουν {0} για τη λήξη.", "timerHasExpired": "Η αντίστροφη μέτρηση ολοκληρώθηκε.", "name": "Όνομα", "email": "Ηλεκτρονικό Ταχυδρομείο", "date": "Ημερομηνία", "time": "Ώρα", "now": "Τώρα", "dateFormat": "DD/MM/YYYY", "datePlaceholder": "ΗΗ/ΜΜ/ΕΕΕΕ", "dateTimeFormat": "DD/MM/YYYY HH:mm", "dateTimeSecondsFormat": "DD/MM/YYYY HH:mm:ss", "dateTimePlaceholder": "ΗΗ/ΜΜ/ΕΕΕΕ ΩΩ:ΛΛ", "dateTimeSecondsPlaceholder": "ΗΗ/ΜΜ/ΕΕΕΕ ΩΩ:ΛΛ:ΔΔ", "timeFormat": "HH:mm", "timeSecondsFormat": "HH:mm:ss", "timePlaceholder": "ΩΩ:ΛΛ", "timeSecondsPlaceholder": "ΩΩ:ΛΛ:ΔΔ", "thousandSeparator": ".", "decimalSeparator": ",", "list": "Λίστα", "table": "Π<PERSON>ν<PERSON><PERSON><PERSON>ς", "cards": "Κάρτ<PERSON>ς", "homeTitle": "Αρχική", "homeMessage": "Αυτή είναι η αρχική σελίδα", "welcomeMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON> ήλθατε στην υπηρεσία Διαχείριση αιτήσεων απόδοσης ΑΦΜ & Κλειδάριθμου", "aboutTitle": "Σχετικά", "aboutMessage": "Αυτή είναι η σχετική σελίδα", "contactTitle": "Επικοινωνία", "contactMessage": "Αυτή είναι η σελίδα της επικοινωνίας", "number1": "Αριθμός 1", "number2": "Αριθμός 2", "person": "Πρόσωπο", "persons": "Πρόσωπα", "file": "Αρχείο", "files": "Αρχεία", "availableSpace": "Δι<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> χώρος", "attachment": "Συνημμένο", "attachments": "Συνημμένα", "attachmentRejected": "Δεν επιτρέπεται η επισύναψη του αρχείου", "attachmentInvalidType": "Μη επιτρεπτός τύπος αρχείου {0}, οι επιτρεπόμενοι τύποι αρχείων είναι: {1}", "attachmentsMaxFilesNumberExceeded": "Η επισύναψη απέτυχε λόγω υπέρβασης του μέγιστου επιτρεπτού αριθμού αρχείων ({0})", "attachmentMaxFileSizeExceeded": "Η επισύναψη απέτυχε λόγω υπέρβασης του μέγιστου επιτρεπτού μεγέθους αρχείου ({0} KB / {1} KB)", "attachmentsMaxTotalFilesSizeExceeded": "Η επισύναψη απέτυχε λόγω υπέρβασης του μέγιστου επιτρεπτού μεγέθους αρχείων ({0} KB)", "welcomeMessage1": "<PERSON><PERSON><PERSON><PERSON><PERSON> ήλθατε στην υπηρεσία", "welcomeMessage2": "Διαχείριση αιτήσεων απόδοσης ΑΦΜ & Κλειδάριθμου", "welcomeMessage3": "της ΑΑΔΕ", "print": "Εκτύπωση", "registryData": "Στοιχεία μητρώου", "taxEntity": "Φορολογούμενος", "taxEntityData": "Στοιχεία φορολογούμενου", "taxRepresentative": "Φορολ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Ε<PERSON>πρόσωπος", "taxRepresentativeData": "Στοιχεία φορολογικού εκπρόσωπου", "loadingTaxEntityData": "Φόρτωμα στοιχείων φορολογούμενου", "taxEntityDataLoadFailure": "Το φόρτωμα των στοιχείων του φορολογούμενου απέτυχε", "taxEntityDataForUserNotFound": "Δεν βρέθηκαν στοιχεία φορολογούμενου για τον χρήστη '{0}'", "taxEntityNotAllowedToSubmitDeclaration": "Ο φορολογούμενος με ΑΦΜ {0} δεν επιτρέπεται να υποβάλει αίτηση.", "firmTaxEntityNotAllowedToSubmitDeclaration": "Η χρήση της εφαρμογής για λογαριασμό νομικού προσώπου/νομικής οντότητας μπορεί να γίνει είτε από εξουσιοδοτημένο εκπρόσωπο είτε από εξουσιοδοτημένο για την διαχείριση δηλώσεων παρακρατούμενων φόρων ή λοιπών δηλώσεων εισοδήματος λογιστή/λογιστικό γραφείο.", "taxEntityIsInactiveTitle": "Ο ΑΦΜ {0} ε<PERSON><PERSON><PERSON><PERSON> απενεργοποιημένος σύμφωνα με το μητρώο της ΑΑΔΕ.", "taxEntityIsInactive": "Δεν επιτρέπεται η χρήση της εφαρμογής για τον/την φορολογούμενο/η με ΑΦΜ {0} λόγω απενεργοποίησης στο Μητρώο της ΑΑΔΕ.", "taxEntityIsDeadTitle": "Ο ΑΦΜ {0} αν<PERSON><PERSON><PERSON><PERSON> σε θανόντα σύμφωνα με το μητρώο της ΑΑΔΕ.", "taxEntityIsDead": "Δεν επιτρέπεται η χρήση της εφαρμογής για τον/την φορολογούμενο/η με ΑΦΜ {0} λόγω δήλωσης θανάτου στο Μητρώο της ΑΑΔΕ.", "taxEntityIsStolenIdTitle": "Έχει δηλωθεί κλοπή ταυτότητας για τον ΑΦΜ {0} σύμφωνα με το μητρώο της ΑΑΔΕ.", "taxEntityIsStolenId": "Δεν επιτρέπεται η χρήση της εφαρμογής για τον/την φορολογούμενο/η με ΑΦΜ {0} λόγω δήλωσης κλοπής ταυτότητας στο Μητρώο της ΑΑΔΕ.", "taxEntityIsNotEpitid": "Η συγκεκριμένη ηλεκτρονική εφαρμογή μπορεί να χρησιμοποιηθεί μόνο από επιτηδευματίες.", "taxEntityViesCheck": "Δεν επιτρέπεται η υποβολή αίτησης από τον ΑΦΜ {0} λόγω αναστολής χρήσης για τη διενέργεια ενδοκοινοτικών συναλλαγών (ΠΟΛ.1200/2015).", "printTaxEntityData": "Εκτύπωση στοιχείων φορολογούμενου", "actAs": "Ενεργώ", "me": "Εγώ", "self": "για τον εαυτό μου", "for": "για", "attributes": "Γνωρίσματα", "isRegistryInvalid": "Υπάρχει πρόβλημα στα στοιχεία του μητρώου", "isRegistryValid": "Τα στοιχεία του μητρώου είναι σωστά", "individual": "Φυσικ<PERSON> πρόσωπο", "firm": "Νομικό πρόσωπο", "dead": "Πεθαμένος", "badAfm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ος <PERSON>", "invalidAfm": "Μη επιτρεπ<PERSON><PERSON>ς ΑΦΜ", "inactiveAfm": "Απενεργοποιημένος ΑΦΜ", "stolenId": "Κλεμμένη ταυτότητα", "epitid": "Επιτηδευματίας", "viesCheck": "Έλεγχος VIES", "accountant": "<PERSON><PERSON><PERSON><PERSON><PERSON>τ<PERSON>ς", "asAccountant": "ως λογιστής", "accountingOffice": "Λογιστικ<PERSON> γραφείο", "asAccountingOffice": "ως λογιστής λογιστικού γραφείου", "legalRepresentative": "Νό<PERSON>ι<PERSON>ος εκπρόσωπος", "asLegalRepresentative": "ως εκπρόσωπος νομικού προσώπου", "selectAuthorizer": "Επιλογή εκπροσωπούμενου", "searchAuthorizer": "Αναζήτηση εκπροσωπούμενου", "afm": "ΑΦΜ", "doy": "ΔΟΥ", "firmName": "Επωνυμία επιχείρησης", "firstName": "Όνομα", "lastName": "Επώνυμο", "fullName": "Ονοματεπώνυμο", "fatherName": "Πατρώνυμο", "idDocNumber": "Αριθμός ταυτοποιητικού εγγράφου", "phone": "Τηλέφωνο", "telephone": "Τηλέφωνο", "address": "Διεύθυνση", "declaration": "Αίτηση", "submission": "Υποβολή", "served": "Απόδοση ΑΦΜ", "idDocument": "Ταυτοποιητικό έγγραφο", "submissionMode": "Υποβολή από", "submissionSelf": "αιτούντα", "submissionLegalRepresentative": "νόμιμο εκπρόσωπο", "submissionAuthorizedRepresentative": "εξουσιοδοτημένο εκπρόσωπο", "submissionUndefined": "απροσδιόριστο", "submitDeclaration": "Υποβολή αίτησης", "submitDeclarationConfirmationTitle": "Η αίτηση είναι εμπρόθεσμη", "submitDeclarationConfirmationOverdueTitle": "Η αίτηση είναι εκπρόθεσμη", "submitOverdueDeclarationConfirmationOverdueMessage": "Θα ενημερωθείτε από την αρμόδια Υπηρεσία σχετικά με το προβλεπόμενο πρόστιμο του άρθρου 53 του Κ.Φ.Δ.", "submitDeclarationConfirmationMessage": "Σημειώνετα<PERSON> ότι μετά την οριστική υποβολή δεν έχετε δυνατότητα μεταβολής των δεδομένων ούτε διαγραφής της οριστικά υποβληθείσας αίτησης.<br/>Επιλέξ<PERSON><PERSON> \"Επιστροφή\" για να αλλάξετε όποια δεδομένα επιθυμείτε ή \"Υποβολή\" για την οριστική υποβολή της.", "submittedDeclarations": "Αιτήσεις", "viewDeclaration": "Προβολή αίτησης", "viewAttachment": "Προβολή συνημμένου", "printDeclaration": "Ψηφιακό αρχείο αίτησης", "noSubmittedDeclarationsFound": "Δεν βρέθηκαν αιτήσεις", "inputAllRequiredDeclarationFieldsSubmit": "Παρακαλούμε συμπληρώστε τα αιτούμενα πεδία προκειμένου να υποβληθεί η αίτησή σας.", "deleteAllTransactionsConfirmation": "Θέλετε σίγουρα να διαγράψετε όλους τους αντισυμβαλλόμενους της αίτησης;", "deleteTransactionConfirmation": "Θέλετε σίγουρα να διαγράψετε τον αντισυμβαλλόμενο;", "declarationSubmitConfirmation": "Θέλετε σίγουρα να υποβάλετε οριστικά την αίτηση;", "declarationDeleteConfirmation": "Θέλετε σίγουρα να διαγράψετε την αίτηση;", "declarationDeleteSuccess": "Η αίτηση διαγράφηκε επιτυχώς", "declarationDeleteFailure": "Η διαγραφή της αίτησης απέτυχε", "declarationSaveSuccess": "Η αίτηση αποθηκεύτηκε επιτυχώς", "declarationSaveFailure": "Η αποθήκευση της αίτησης απέτυχε", "declarationSubmitSuccess": "Η υποβολή της αίτησης με αριθμό {0} ολοκληρώθηκε επιτυχώς", "declarationSubmitFailure": "Η υποβολή της αίτησης απέτυχε", "declarationGetSuccess": "Η αίτηση ανακτήθηκε επιτυχώς", "declarationGetFailure": "Η ανάκτηση της αίτησης απέτυχε", "declarationGetNotFound": "Δεν βρέθηκε η αίτηση με κωδικό {0}", "declarationRelated": "Συνδεδεμένη αίτηση", "status": "Κατάσταση", "declarationApprovalStatus": "Κατάσταση αποδοχής", "declarationApprovalStatusApproved": "Αποδοχή", "declarationApprovalStatusRejected": "Απόρριψη", "declarationApprovalStatusPending": "Εκκρεμεί αποδοχή", "declarationApprovalStatusExpired": "Λύση", "year": "Έτος", "searchTerm": "Όρος αναζήτησης", "declarationNumber": "Αριθμός αίτησης", "declarationDate": "Ημερομηνία υποβολής", "project": "Έργο", "projectDescription": "Σύντομη περιγραφή έργου / λοιπές παρατηρήσεις", "projectStart": "Ημερομηνία έναρξης εργασιών", "projectStartInvalid": "Μη έγκυρη ημερομηνία έναρξης εργασιών", "projectValue": "Αξία έργου (ευρώ)", "projectValueUndefined": "<PERSON><PERSON><PERSON><PERSON><PERSON> καθορισμένη αξία", "projectValueHint": "Συμπληρώνετα<PERSON> ποσό άνω των 6.000 ευρώ. Συμφωνητικά για έργα χαμηλότερης αξίας υποβάλλονται ηλεκτρονικά στην \"Κατάσταση Συμφωνητικών παραγράφου 16 άρθρου 8 ν.1882/1990\" εφόσον είστε επιτηδευματίας.", "projectMinValue": "Το πεδίο συμπληρώνεται με ποσά άνω των {0} ευρώ.", "projectMaxValue": "Το πεδίο μπορεί να έχει τιμή έως {0} ευρώ.", "projectAttachment": "Επισύναψη συμφωνητικού", "applicantAfm": "ΑΦΜ φορολογούμενου", "foreignAfm": "<PERSON><PERSON><PERSON><PERSON><PERSON> ΑΦΜ στην Ελλάδα", "foreignAfmRequired": "Παρακαλούμε συμπληρώστε τον ΑΦΜ αλλοδαπής", "assignor": "Αναθέτων", "assignorAFM": "ΑΦΜ αναθέτοντος", "transactors": "Αντισυμβαλλόμενοι", "transactorsAfms": "ΑΦΜ αντισυμβαλλόμενων", "transactor": "Αντισυμβαλλόμενος", "transactorData": "Στοιχεία εργολάβου/υπεργολάβου", "transactorAFM": "ΑΦΜ αντισυμβαλλόμενου", "addressData": "Στοιχεία διεύθυνσης", "street": "<PERSON><PERSON><PERSON><PERSON>", "number": "Αριθμός", "city": "Π<PERSON><PERSON><PERSON>", "zip": "Τ.Κ.", "country": "Χώ<PERSON><PERSON>", "firmTransactorNotAllowed": "Ο αντισυμβαλλόμενος είναι επιχείρηση. Η αίτηση Θα πρέπει να υποβληθεί από τον αντισυμβαλλόμενο.", "getAfmRegistryError": "Η ανάκτηση των στοιχείων μητρώου για τον ΑΦΜ {0} απέτυχε.", "isSameApplicantTransactorAfm": "Ο ΑΦΜ του αντισυμβαλλόμενου είναι ίδιος με του αναθέτοντος.", "sameApplicantTransactorAfm": "Ο ΑΦΜ του αντισυμβαλλόμενου δεν επιτρέπεται να είναι ίδιος με του αναθέτοντος.", "transactorAfmExists": "Έχετε ήδη καταχωρήσει αντισυμβαλλόμενο με ΑΦΜ {0}.", "outOfMarginSubmissionMessage": "<div class='text-left' style='font-size: 0.85rem;'>Η αίτηση είναι εκπρόθεσμη.</div>", "protocolNumber": "Αρ. πρωτοκόλλου αιτήματος σε εκκρεμότητα", "protocolNumberHint": "Συμπληρώνεται ο αριθμός πρωτοκόλλου τυχόν σχετικού αιτήματος που υποβλήθηκε μέσω της εφαρμογής \"Τα Αιτήματά μου\" και παρέμεινε σε εκκρεμότητα την 01/11/2024.", "protocolNumberInvalid": "Έχει συμπληρωθεί μη αποδεκτός αριθμός στο πεδίο \"Αρ. πρωτοκόλλου αιτήματος σε εκκρεμότητα\".", "relatedDeclaration": "Σύνδεση με προηγούμενη υποβολή", "relatedDeclarationHint": "Επιλέγεται από τη λίστα ή συμπληρώνεται ο αριθμός της προηγούμενης αίτησης σε περίπτωση τροποποίησης σύμβασης.", "relatedDeclarationInvalid": "Έχουν συμπληρωθεί μη αποδεκτοί χαρακτήρες στο πεδίο \"Σύνδεση με προηγούμενη υποβολή\".", "relatedDeclarationNotFound": "Δεν βρέθηκε η σχετιζόμενη αίτηση.", "relatedDeclarationIdNotFound": "Δεν βρέθηκε η σχετιζόμενη αίτηση με id {0}.", "declarationNoItemsNotAllowed": "Για να υποβληθεί η αίτηση πρέπει να οριστεί τουλάχιστον ένας αντισυμβαλλόμενος.", "attachmentsMaxTotalFilesSize": "Το μέγεθος των αρχείων που έχετε επισυνάψει είναι {0} Kb. Το μέγιστο επιτρεπόμενο συνολικο μέγεθος συνημμένων είναι {1} Kb.", "approve": "Αποδοχή", "approval": "Αποδοχή", "approveTransactor": "Αποδοχή αίτησης", "approveTransactorDescription": "Αποδοχή αίτησης {0}", "approveTransactorWarning": "Η διαδικασία αποδοχής αίτησης είναι αμετάκλητη.", "approveTransactorConfirmation": "Η διαδικασία αποδοχής αίτησης είναι αμετάκλητη.<br/>Θέλετε σίγουρα να αποδεχτείτε την αίτηση {0};", "approveTransactorFailure": "Η αποδοχή της αίτησης {0} απέτυχε.", "approveTransactorSuccess": "Η αποδοχή της αίτησης {0} ολοκληρώθηκε επιτυχώς.", "reject": "Απόρριψη", "rejection": "Απόρριψη", "rejectedDeclaration": "Το συμφωνητικό έχει απορριφθεί από τουλάχιστον έναν αντισυμβαλλόμενο.", "rejectTransactor": "Απόρριψη αίτησης", "rejectTransactorDescription": "Απόρριψη αίτησης {0}", "rejectTransactorWarning": "Η διαδικασία απόρριψης αίτησης είναι αμετάκλητη.", "rejectTransactorConfirmation": "Η διαδικασία απόρριψης αίτησης είναι αμετάκλητη.<br/>Θέλετε σίγουρα να απορρίψετε την αίτηση {0};", "rejectTransactorFailure": "Η απόρριψη της αίτησης {0} απέτυχε.", "rejectTransactorSuccess": "Η απόρριψη της αίτησης {0} ολοκληρώθηκε επιτυχώς.", "expire": "Λύση", "expiration": "Λύση", "expireDeclaration": "Λύ<PERSON>η συμφωνητικού", "expiredDeclarationSubmitted": "Έχει δηλωθεί λύση του συμφωνητικού", "expireDeclarationDate": "Ημερομην<PERSON>α λύσης συμφωνητικού", "expireDeclarationDateAfterToday": "Η ημερομηνία λύσης συμφωνητικού δεν μπορεί να είναι μεταγενέστερη από τη σημερινή.", "expireDeclarationComments": "Αιτιολογ<PERSON><PERSON> λύσης συμφωνητικού", "expireDeclarationDescription": "Λύση συμφωνητικού {0}", "expireDeclarationWarning": "Η διαδικασία λύσης συμφωνητικού είναι αμετάκλητη.", "expireDeclarationConfirmation": "Η διαδικασία υποβολής αίτησης λύσης συμφωνητικού είναι αμετάκλητη.<br/>Θέλετε σίγουρα να δηλώσετε λύση του συμφωνητικού {0};", "expireDeclarationFailure": "Η υποβολή της αίτησης λύσης του συμφωνητικού {0} απέτυχε.", "expireDeclarationSuccess": "Η υποβολή της αίτησης λύσης του συμφωνητικού {0} ολοκληρώθηκε επιτυχώς.", "export": "Εξαγωγή", "exportToCsv": "Εξαγωγή σε CSV", "exportToExcel": "Εξαγωγή σε Excel", "exportToExcelCancel": "Ακύρωση διαδικασίας εξαγωγής σε Excel", "exportProcess": "Αρχεί<PERSON> {0} / {1}", "exportProcessCompleted": "Ολοκληρώθηκε η διαδικασία εξαγωγής. Πλήθος αρχείων {0}.", "exportProcessCanceled": "Η διαδικασία εξαγωγής ακυρώθηκε. Πλήθος αρχείων {0}.", "clickToCancelExport": "Πατήστε Ακύρωση για διακοπή της διαδικασίας", "declarationConsentTitle": "Προϋπόθεση", "declarationConsentText": "Με ατομική μου ευθύνη και γνωρ<PERSON>ζοντας τις κυρώσεις που προβλέπονται από τις διατάξεις της παρ.6 του άρθρου 22 του Ν.1599/1986, δηλώνω  ότι στα ειδικά διασκευασμένα τραπέζια της αίτησης απογραφής διεξάγονται μόνο ψυχαγωγικά τεχνικά παίγνια.", "declarationConsent": "Δηλώνω υπεύθυνα ότι διάβασα και αποδέχομαι την παραπάνω προϋπόθεση.", "declarationConsentRequired": "Η δήλωση αποδοχής είναι υποχρεωτική.", "representativeAfm": "ΑΦΜ εκπροσώπου", "representativeName": "Ονοματεπώνυμο εκπροσώπου", "declarationReplacedBy": "Η αίτηση έχει αντικατασταθε<PERSON> από την αίτηση με αριθμό {0}", "applicationData": "Στοιχεία αίτησης", "personalIdentificationData": "Προσωπ<PERSON>κ<PERSON> Στοιχεία και Στοιχεία Ταυτοποίησης", "personalData": "Προσωπικ<PERSON> Στοιχεία", "identificationData": "Στοιχεία Ταυτοποίησης", "contactData": "Στοιχεία Επικοινωνίας", "generalData": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Πληροφορίες", "relatedTinsData": "Σχετιζόμενοι ΑΦΜ", "relatedTinData": "Σχετιζόμενος", "relatedTin1": "ΑΦΜ 1", "relatedTin2": "ΑΦΜ 2", "relatedTin3": "ΑΦΜ 3", "relatedTin4": "ΑΦΜ 4", "relatedTin5": "ΑΦΜ 5", "applicantName": "Ονοματεπώνυμο", "applicantFatherName": "Ονοματεπώνυμο πατέρα", "applicantMotherName": "Ονοματεπώνυμο μητέρας", "sex": "Φύλο", "surNameA": "Επώνυμο Α΄", "surNameB": "Επώνυμο B΄", "fathersSurName": "Επώνυμο <PERSON>ατέρα", "fathersName": "Όνομα Πατέρα", "mothersSurName": "Επώνυμο <PERSON>ητέ<PERSON>ας", "mothersName": "Όνομα Μητέρας", "dateOfBirth": "Η<PERSON><PERSON>ς", "countryOfBirth": "Χώρα-<PERSON><PERSON><PERSON><PERSON> γέννησης", "placeOfBirthInGreece": "Τό<PERSON>ος γέννησης στην Ελλάδα", "genderMale": "Άνδρας", "genderFemale": "Γυνα<PERSON><PERSON><PERSON>", "idKind": "Είδος ταυτοποιητικού εγγράφου", "idNumber": "Αριθμός ταυτοποιητικού εγγράφου", "issueDate": "Ημ. Έκδοσης", "endDate": "Ημ. Λήξης", "authority": "Εκδούσα Αρχή", "permitKind": "Είδος Άδειας Διαμονής", "permitNumber": "Αριθμός Άδειας Διαμονής", "maritalStatus": "Οικογενειακ<PERSON> Κατάσταση", "judicialSupport": "Δικαστική Συμπαράσταση", "citizenShip": "Υπηκοότητα", "taxpayerStatusJudicialSupportYes": "Ναι", "taxpayerStatusJudicialSupportNo": "Όχι", "residenseKind": "Χώρ<PERSON> κατοικίας", "residenseKindGreek": "Ελλάδα", "residenseKindAbroad": "Εξωτερικό", "residenseAbroad": "Χώρα κατοικ<PERSON>ας στο Εξωτερικό", "afmAbroad": "TIN Number Εξωτερικού", "municipality": "<PERSON><PERSON><PERSON><PERSON>", "prefecture": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "region": "Περιφερειακή Ενότητα", "notificationChoice": "Ορισμός φορολογικού εκπροσώπου και κοινοποιήσεις Φορολογικής Διοίκησης", "notificationChoiceUndefined": "Δεν υπάρχει επιλογή κοινοποίησης", "notificationChoiceNone": "Δεν υπάρχει επιλογή κοινοποίησης", "notificationChoiceTaxRepresentativeOnly": "Επιθυμώ να ορίσω φορολογι<PERSON><PERSON> εκπρόσωπο στον οποίο θα γίνεται η κοινοποίηση των κάθε είδους πράξεων, εγ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> και ατομικών ειδοποιήσεων της Φορολογικής Διοίκησης προς εμένα.", "notificationChoiceTaxRepresentativePlusSelf": "Επιθυμώ να ορίσω φορολογ<PERSON><PERSON><PERSON> εκπρόσωπ<PERSON>, και δηλώνω ότι επιθυμώ η κοινοποίηση των κάθε είδους πράξεων, εγ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> και ατομικών ειδοποιήσεων της Φορολογικής Διοίκησης προς εμένα να γίνεται και στα στοιχεία επικοινωνίας που δηλώνω στη Φορολογική Διοίκηση εκτός από τον φορολογικό μου εκπρόσωπο.", "notificationChoiceSelfOnly": "Δεν επιθυμώ να ορίσω φορολο<PERSON><PERSON><PERSON><PERSON> εκπρόσωπο και αποδέχομαι η κοινοποίηση των κάθε είδους πράξεων, εγγ<PERSON><PERSON><PERSON><PERSON><PERSON> και ατομικών ειδοποιήσεων της Φορολογικής Διοίκησης προς εμένα να γίνεται στα στοιχεία επικοινωνίας που δηλώνω στη Φορολογική Διοίκηση.", "relStartDate": "Ημ. Έναρξης Σχέσης", "relKind": "<PERSON><PERSON><PERSON><PERSON> σχέσης", "relProofDoc": "Αποδεικτικό Έγγραφο", "relProofDocNo": "Αριθμός <PERSON>άφου", "relProofDocDate": "Ημ. Εγγράφου", "relProofDocAuthor": "Εκδούσα Αρχή Αποδεικτικού Εγγράφου", "m1Created": "Ημερομηνία δημιουργίας της αίτησης", "m1UpdatedEmpl": "Ημερομηνία απόδοσης ΑΦΜ", "m1Afm": "ΑΦΜ που αποδόθηκε", "representativeSendEmail": "Αποστολή email", "sentEmails": "Απεσταλμένα email", "emailTo": "Παρα<PERSON><PERSON><PERSON><PERSON>ης", "emailFrom": "Αποστολέας", "emailSentAt": "Ημερομηνία Αποστολής", "serviceChannelType": "Κανάλι εξυπηρέτησης", "serviceChannelTypeUndefined": "Δεν έχει ορισθεί κανάλι εξυπηρέτησης", "serviceChannelTypeNone": "Δεν έχει ορισθεί κανάλι εξυπηρέτησης", "serviceChannelTypeNavigateToFAABooking": "Άμεση Βιντεοκλήση", "serviceChannelTypeNavigateToMyAADEliveBooking": "Ραντεβού myAADElive", "serviceChannelTypeNavigateToDoyBooking": "Ραντεβού ΔΟΥ (φυσική παρουσία)", "serviceChannelTypeNavigateToTicketBooking": "Καταχώρηση αιτήματος", "serviceChannelTypeNavigateToMyAADEliveSpecialBooking": "Ραντεβού myAADElive (με μετάφραση ή στην Νοηματική γλώσσα)", "of": "από", "recordsPerPage:": "Εγγ<PERSON>α<PERSON><PERSON><PERSON> ανά σελίδα:"}