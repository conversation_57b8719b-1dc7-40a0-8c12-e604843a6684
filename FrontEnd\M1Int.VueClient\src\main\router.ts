import { createRouter, createWeb<PERSON>ashH<PERSON><PERSON>, RouteLocationNormalized, RouteRecordRaw } from 'vue-router';
import authService from '@/auth/services/AuthService';
import config from '@/config';
import HomeView from './views/Home.vue';
import AboutView from './views/About.vue';
import AuthHandler from '@/auth/views/AuthHandler.vue';
import Login from '@/auth/views/Login.vue';
import {
    LOGIN,
    LOGIN_CALLBACK,
    LOGOUT,
    LOGOUT_CALLBACK,
    SIGNIN,
    SIGNIN_CALLBACK,
    SIGNOUT,
    SIGNOUT_CALLBACK
} from '@/auth/authDefs';
import ViewDeclarationView from '@/m1Module/views/m1/ViewDeclaration.vue';
import SubmittedDeclarationsView from '@/m1Module/views/m1/SubmittedDeclarations.vue';

const routes: Array<RouteRecordRaw> = [
    {
        path: '/',
        name: 'home',
        component: HomeView,
        props: getAllPropsFromQuery
    },
    {
        path: '/about',
        name: 'about',
        component: AboutView
    },
    {
        path: `/${LOGIN}`,
        name: LOGIN,
        component: Login
    },
    {
        path: `/${SIGNIN}`,
        name: SIGNIN,
        component: Login
    },
    {
        path: `/${LOGIN_CALLBACK}`,
        name: LOGIN_CALLBACK,
        component: AuthHandler,
        props: { action: LOGIN_CALLBACK }
    },
    {
        path: `/${SIGNIN_CALLBACK}`,
        name: SIGNIN_CALLBACK,
        component: AuthHandler,
        props: { action: SIGNIN_CALLBACK }
    },
    {
        path: `/${LOGOUT}`,
        name: LOGOUT,
        component: AuthHandler,
        props: { action: LOGOUT }
    },
    {
        path: `/${SIGNOUT}`,
        name: SIGNOUT,
        component: AuthHandler,
        props: { action: SIGNOUT }
    },
    {
        path: `/${LOGOUT_CALLBACK}`,
        name: LOGOUT_CALLBACK,
        component: AuthHandler,
        props: { action: LOGOUT_CALLBACK }
    },
    {
        path: `/${SIGNOUT_CALLBACK}`,
        name: SIGNOUT_CALLBACK,
        component: AuthHandler,
        props: { action: SIGNOUT_CALLBACK }
    },
    {
        path: '/viewDeclaration/:declarationId',
        name: 'ViewDeclaration',
        component: ViewDeclarationView,
        props: true
    },
    {
        path: '/submittedDeclarations',
        name: 'SubmittedDeclarations',
        component: SubmittedDeclarationsView
    }
];

function getAllPropsFromQuery(route: RouteLocationNormalized): any {
    if (!route?.query) return null;
    const props: any = { ...route.query };
    return props;
}

const router = createRouter({
    history: createWebHashHistory(),
    routes
});

router.beforeEach(async (to, from, next) => {
    const fromPath: string = from?.path?.toLowerCase();
    const toPath: string = to?.path?.toLowerCase();
    // console.log(`routing: ${fromPath} => ${toPath}`);

    await authService.checkLoadAuthData();
    authService.checkFixAuthExpired();

    if (config.AUTHENTICATION_REQUIRED_ROUTER_PATHS.includes(toPath)) {
        // if (!authService.userAuthenticated) return next({ path: '/login' });
        if (!authService.userAuthenticated) return next({ path: '/' });
    } else {
        if (config.AUTHENTICATION_FORBIDDEN_ROUTER_PATHS.includes(toPath)) {
            if (authService.userAuthenticated) {
                if (config.AUTHENTICATION_FORBIDDEN_ROUTER_PATHS.includes(fromPath)) {
                    return next({ path: '/' });
                } else {
                    return next({ path: fromPath });
                }
            }
        }
    }

    return next();
});

export default router;
