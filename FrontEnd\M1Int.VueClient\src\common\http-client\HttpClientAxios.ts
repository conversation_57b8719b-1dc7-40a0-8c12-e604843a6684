import { HttpClientBase } from './HttpClientBase';
import axios from 'axios';
import config from '@/config';

export class HttpClientAxios extends HttpClientBase {
    constructor(public readonly baseUrl: string = config.BACKEND_API_URL) {
        super(baseUrl);
    }

    private getErrorMessage(response: any): string {
        let errorMessage = 'API Error';
        if (!response) return errorMessage;
        const error: any = response.error || response.data?.error;
        if (error?.errorMessage || error?.message) {
            errorMessage = error?.errorMessage || error?.message;
        } else if (error?.errorMessages || error?.messages) {
            errorMessage = (error?.errorMessages || error?.messages)?.join(', ');
        } else if (response.data?.title) {
            errorMessage = response.data?.title;
        } else if (response.data?.statusText) {
            errorMessage = response.data?.statusText;
        } else if (response.data?.status) {
            errorMessage = response.data?.status;
        } else if (response.data) {
            errorMessage = response.data.toString();
        } else if (response.statusText) {
            errorMessage = response.statusText;
        } else if (response.status) {
            errorMessage = response.status.toString();
        }
        return errorMessage;
    }

    private handleApiResponse(response: any): Promise<any> {
        if (!response) throw new Error('Invalid response object!');
        let responseContentPromise: Promise<any>;
        if (response.status >= 200 && response.status < 400) {
            responseContentPromise = Promise.resolve(response.data);
            return responseContentPromise;
        } else {
            return Promise.reject(this.getErrorMessage(response));
        }
    }

    private handleApiException(err: any): Promise<any> {
        if (!err) {
            throw new Error('API Error!');
        }
        return Promise.reject(this.getErrorMessage(err.response));
    }

    protected getOptions(options?: any): any {
        options = super.getOptions(options);
        if (options.withCredentials === undefined || options.withCredentials === null) {
            options.withCredentials = true;
        }
        return options;
    }

    public request<T>(options: any): Promise<T> {
        options = this.getUrlAndOptions(options?.url, options);
        return axios(options)
            .then((response) => this.handleApiResponse(response))
            .then((result) => <T>result)
            .catch((err) => this.handleApiException(err));
    }

    public get<T>(url: string, options?: any): Promise<T> {
        options = this.getUrlAndOptions(url, options);
        return axios
            .get(options.url, options)
            .then((response) => this.handleApiResponse(response))
            .then((result) => <T>result)
            .catch((err) => this.handleApiException(err));
    }

    public post<T, U>(url: string, data: U, options?: any): Promise<T> {
        options = this.getUrlAndOptions(url, options);
        const requestData = this.getDataFromModel(data, options);
        return axios
            .post(options.url, requestData, options)
            .then((response) => this.handleApiResponse(response))
            .then((result) => <T>result)
            .catch((err) => this.handleApiException(err));
    }

    public delete<T>(url: string, options?: any): Promise<T> {
        options = this.getUrlAndOptions(url, options);
        return axios
            .delete(options.url, options)
            .then((response) => this.handleApiResponse(response))
            .then((result) => <T>result)
            .catch((err) => this.handleApiException(err));
    }

    public put<T, U>(url: string, data: U, options?: any): Promise<T> {
        options = this.getUrlAndOptions(url, options);
        const requestData = this.getDataFromModel(data, options);
        return axios
            .put(options.url, requestData, options)
            .then((response) => this.handleApiResponse(response))
            .then((result) => <T>result)
            .catch((err) => this.handleApiException(err));
    }
}
