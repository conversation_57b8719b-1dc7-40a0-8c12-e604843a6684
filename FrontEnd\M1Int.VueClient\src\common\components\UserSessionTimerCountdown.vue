<template>
    <div v-if="timerVisible" class="q-mx-none q-px-none user-session-timer-countdown">
        <div class="row justify-center items-center user-session-timer-countdown-content">
            <q-knob
                :min="0"
                :max="initialSecondsLeft"
                v-model="timerTotalSecondsLeft"
                reverse
                show-value
                :size="timerKnobSize"
                :thickness="timerKnobThickness"
                :color="timerStatusColor"
                track-color="grey-4"
            >
                {{ timerDisplayText }}
            </q-knob>
        </div>
    </div>
</template>

<script lang="ts">
import { defineComponent, PropType } from 'vue';
import { ICommonStore, getCommonStore } from '../stores/Store';
import { UserAuthData } from '@/auth/models/Auth';
import TimerCountdown from './TimerCountdown.vue';

export default defineComponent({
    name: 'user-session-timer-countdown',
    mixins: [TimerCountdown],
    props: {
        store: {
            type: Object as PropType<ICommonStore>,
            default: () => getCommonStore()
        }
    },
    computed: {
        expirationTime(): number {
            return this.authData?.authTokenExpiresAt!.getTime() || 0;
        },
        timerVisible(): boolean {
            if (!this.timer) return false;
            if (!this.hasValidAuthExpiration) return false;
            return this.$q?.screen?.width >= 360;
        },
        authData(): UserAuthData | null {
            return this.store?.authData;
        },
        isUserAuthenticated(): boolean {
            return !!(this.authData && this.authData.userName);
        },
        hasValidAuthExpiration(): boolean {
            return !!(
                this.authData &&
                this.authData?.userName &&
                this.authData?.authToken &&
                this.authData.authTokenExpiresAt
            );
        }
    },
    watch: {
        userAuthData() {
            this.resumeTimer();
        }
    },
    methods: {
        doLogout(): boolean {
            this.$router?.push({ path: '/logout' });
            return true;
        },
        async timerExpired() {
            let loggedOut: boolean = false;
            this.$emit('timerExpired', this.timer);
            setTimeout(() => {
                if (!loggedOut) loggedOut = this.doLogout();
            }, 30000);
            await this.$alert?.messageBoxWarning({
                title: this.$t('userSession'),
                message: this.$t('userSessionExpiredLoginRequired')
            });
            if (!loggedOut) loggedOut = this.doLogout();
        },
        handleTimerNotifications() {
            if (!this.timer || !this.showNotifications) return;
            if (this.timerTotalSecondsLeft < this.timerSecondsLevel0) {
                if (!this.timerSecondsLevel0WarningDisplayed) {
                    this.timerSecondsLevel0WarningDisplayed = true;
                    this.$alert?.notifyWarning({
                        title: this.$t('userSession'),
                        message: this.$t('userSessionExpirationMessage', [this.timerDisplayText]),
                        duration: 10000
                    });
                }
            } else if (this.timerTotalSecondsLeft < this.timerSecondsLevel1) {
                if (!this.timerSecondsLevel1WarningDisplayed) {
                    this.timerSecondsLevel1WarningDisplayed = true;
                    this.$alert?.notifyInfo({
                        title: this.$t('userSession'),
                        message: this.$t('userSessionExpirationMessage', [this.timerDisplayText]),
                        duration: 10000
                    });
                }
            }
        }
    }
});
</script>
