export interface IHttpClient {
    baseUrl: string;
    getDataObjectFromModel(model: any): any;
    getFormDataFromModel(model: any, objectKey?: string): any;
    getDataFromModel(model: any, options: any): any;
    request<T>(options: any): Promise<T>;
    get<T>(url: string, options?: any): Promise<T>;
    post<T, U>(url: string, data: U, options?: any): Promise<T>;
    delete<T>(url: string, options?: any): Promise<T>;
    put<T, U>(url: string, data: U, options?: any): Promise<T>;
}
