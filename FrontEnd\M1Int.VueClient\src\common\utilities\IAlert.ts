export const DEFAULT_NOTIFICATION_DURATION = 3000;

export enum AlertResultType {
    None = 0,
    Ok = 1,
    Cancel = 2,
    Close = 3
}

export class AlertResult {
    public value: any;

    public constructor(public type: AlertResultType = AlertResultType.None) {}

    public get isOk(): boolean {
        return this.type === AlertResultType.Ok;
    }

    public get isCancel(): boolean {
        return this.type === AlertResultType.Cancel;
    }
}

export interface IAlert {
    close(): void;

    messageBox(options?: any): Promise<AlertResult>;
    messageBoxInfo(options?: any): Promise<AlertResult>;
    messageBoxQuestion(options?: any): Promise<AlertResult>;
    messageBoxSuccess(options?: any): Promise<AlertResult>;
    messageBoxWarning(options?: any): Promise<AlertResult>;
    messageBoxError(options?: any): Promise<AlertResult>;

    notify(options?: any): Promise<AlertResult>;
    notifyInfo(options?: any): Promise<AlertResult>;
    notifyQuestion(options?: any): Promise<AlertResult>;
    notifySuccess(options?: any): Promise<AlertResult>;
    notifyWarning(options?: any): Promise<AlertResult>;
    notifyError(options?: any): Promise<AlertResult>;

    confirm(options?: any): Promise<AlertResult>;
    confirmYesNo(options?: any): Promise<AlertResult>;

    prompt(options?: any): Promise<AlertResult>;

    selectFiles(options?: any): Promise<AlertResult>;

    progress(options?: any): Promise<AlertResult>;
}
