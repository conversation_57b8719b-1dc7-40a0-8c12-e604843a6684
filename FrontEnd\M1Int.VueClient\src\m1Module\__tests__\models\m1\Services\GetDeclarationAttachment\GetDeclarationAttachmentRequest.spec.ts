import { GetDeclarationAttachmentRequest } from '@/m1Module/models/m1/Services/GetDeclarationAttachment/GetDeclarationAttachmentRequest';

describe('GetDeclarationAttachmentRequest', () => {
    let request: GetDeclarationAttachmentRequest;

    beforeEach(() => {
        request = new GetDeclarationAttachmentRequest();
    });

    it('should be valid', () => {
        expect(request).toBeTruthy();
    });
});
