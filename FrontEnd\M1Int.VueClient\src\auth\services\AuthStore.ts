import config from '@/config';
import { IHttpClient } from '@/common/http-client/IHttpClient';
import { createHttpClient } from '@/common/http-client/HttpClientFactory';
import { UserAuthData } from '../models/Auth';

const httpClient: IHttpClient = createHttpClient();

export class AuthStore {
    public async loadAuthData(): Promise<UserAuthData | null> {
        let authData: UserAuthData | null = null;
        if (config.USE_SERVER_SESSION) {
            authData = await this.loadAuthDataFromServerSession();
        } else {
            authData = this.loadAuthDataFromStorage();
        }
        return authData;
    }

    public async storeAuthData(authData: UserAuthData | null) {
        if (config.USE_SERVER_SESSION) {
            await this.storeAuthDataToServerSession(authData);
        } else {
            this.storeAuthDataToStorage(authData);
        }
    }

    public async clearAuthData() {
        await this.storeAuthData(null);
    }

    private async loadAuthDataFromServerSession(): Promise<UserAuthData | null> {
        try {
            const url = 'session/GetAuthData';
            const response: any = await httpClient.post<any, any>(url, {});
            if (!response || !response?.userName) {
                await this.clearAuthData();
                return null;
            }
            const authData: UserAuthData = new UserAuthData(response);
            return authData;
        } catch (ex: any) {
            console.error(ex);
            await this.clearAuthData();
            throw ex;
        }
    }

    private async storeAuthDataToServerSession(authData: UserAuthData | null) {
        try {
            const url = 'session/SetAuthData';
            await httpClient.post<any, any>(url, {
                token: authData?.authToken,
                userName: authData?.userName
            });
        } catch (ex: any) {
            console.error(ex);
            // await this.clearAuthData();
            throw ex;
        }
    }

    private get storage(): Storage {
        // return localStorage;
        return sessionStorage;
    }

    private loadAuthDataFromStorage() {
        let authData: UserAuthData | null = null;
        const authDataStr: string | null = this.storage.getItem(config.LOCAL_STORAGE_AUTH_DATA_KEY);
        if (authDataStr) {
            const authDataJson: any = JSON.parse(authDataStr);
            authData = new UserAuthData(authDataJson);
        }
        return authData;
    }

    private storeAuthDataToStorage(authData: UserAuthData | null) {
        if (!authData) {
            this.storage.removeItem(config.LOCAL_STORAGE_AUTH_DATA_KEY);
        } else {
            const authDataStr: string | null = JSON.stringify(authData);
            this.storage.setItem(config.LOCAL_STORAGE_AUTH_DATA_KEY, authDataStr);
        }
    }
}

const authStore = new AuthStore();
export default authStore;
