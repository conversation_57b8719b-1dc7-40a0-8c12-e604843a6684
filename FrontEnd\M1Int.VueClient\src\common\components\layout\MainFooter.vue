<template>
    <q-footer elevated class="main-footer">
        <div class="main-footer-content">
            <div>
                <span v-if="$q?.screen?.width >= 800">{{ $t('applicationDescription') }}</span>
                <span v-else>{{ $t('applicationTitle') }}</span>
                &nbsp;&copy;&nbsp;
                <span>{{ yearStr }}</span>
            </div>
            <div>
                <span>{{ dateStr }}</span>
            </div>
        </div>
    </q-footer>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import type { PropType } from 'vue';
import { ICommonStore, getCommonStore } from '@/common/stores/Store';
import dayjs from '@/plugins/dayjs';

export default defineComponent({
    name: 'main-footer',
    props: {
        store: {
            type: Object as PropType<ICommonStore>,
            default: () => getCommonStore()
        }
    },
    computed: {
        currentDateTime(): Date {
            return this.store?.serverCurrentDateTime || dayjs().toDate();
        },
        yearStr(): string {
            return dayjs(this.currentDateTime).format('YYYY');
        },
        dateStr(): string {
            return dayjs(this.currentDateTime).format(this.$t('dateFormat'));
        }
    }
});
</script>

<style>
.main-footer {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: center;
    align-items: center;
}
.main-footer-content {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: space-between;
    align-items: center;
    padding: 0 16px;
}
</style>
