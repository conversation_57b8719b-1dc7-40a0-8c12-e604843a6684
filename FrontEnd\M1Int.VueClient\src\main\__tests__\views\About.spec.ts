import { shallowMount, VueWrapper } from '@vue/test-utils';
import AboutView from '@/main/views/About.vue';
import { commonComponentMocks } from '@/mocks/commonComponentMocks';

describe('AboutView', () => {
    let wrapper: VueWrapper;

    beforeEach(() => {
        wrapper = shallowMount(AboutView, {
            global: {
                mocks: commonComponentMocks
            }
        });
    });

    afterEach(() => {});

    it('should mount', () => {
        expect(wrapper).toBeTruthy();
        expect(wrapper.vm).toBeTruthy();
        expect(wrapper.vm.$el).toBeTruthy();
    });

    it('element should be valid', () => {
        expect(wrapper.vm.$el.classList).toBeTruthy();
        expect(Array.from(wrapper.vm.$el.classList)).toContain('about-view');
    });

    it('should have valid $t property', () => {
        expect(wrapper.vm.$t).toBeTruthy();
        expect(typeof wrapper.vm.$t).toEqual('function');
    });

    it('$t should return valid result', () => {
        expect(wrapper.vm.$t('AboutView')).toEqual('AboutView');
    });

    it('should have valid $alert property', () => {
        expect((wrapper.vm as any).$alert).toBeTruthy();
    });
});
