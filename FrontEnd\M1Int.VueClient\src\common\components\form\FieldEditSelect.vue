<template>
    <q-select
        :id="elementId"
        :for="elementId"
        :name="elementName"
        v-model="modelObject[fieldId]"
        :behavior="behaviorResolved"
        :error-message="validationMessage"
        :error="isDirty && isInvalid"
        :noErrorIcon="true"
        :class="classResolved"
        :label="label"
        :placeholder="placeholder"
        :hint="hint"
        :hideHint="hideHint"
        :outlined="outlined"
        :filled="filled"
        :borderless="borderless"
        :rounded="rounded"
        :bottomSlots="bottomSlots"
        :hideBottomSpace="!bottomSlots"
        :dense="dense"
        :clearable="clearable && !disabled && !readonly"
        clear-icon="close"
        :disable="disabled"
        :readonly="readonly"
        :autofocus="autofocus && !disabled && !readonly"
        :use-input="filterable"
        input-debounce="0"
        :options="filteredOptions"
        :optionLabel="optionLabel"
        :optionValue="optionValue"
        :optionDisable="optionDisable"
        :emitValue="!returnObject"
        :map-options="!returnObject"
        :multiple="multiple"
        :maxValues="maxValues"
        :useChips="chips"
        @filter="applyFilter"
        @update:model-value="updateModelValue"
    >
        <template v-slot:label>
            <field-label :label="label" :hint="labelInfo" :isTooltip="isLabelInfoTooltip" />
        </template>
        <template v-slot:before v-if="beforeIcon">
            <q-icon :name="beforeIcon" @click="emitEvent('click:before')">
                <q-tooltip v-if="beforeIconTooltip" anchor="top middle" self="center middle">
                    {{ beforeIconTooltip }}
                </q-tooltip>
            </q-icon>
        </template>
        <template v-slot:after v-if="afterIcon">
            <q-icon :name="afterIcon" @click="emitEvent('click:after')">
                <q-tooltip v-if="afterIconTooltip" anchor="top middle" self="center middle">
                    {{ afterIconTooltip }}
                </q-tooltip>
            </q-icon>
        </template>
        <template v-slot:prepend v-if="prependIcon">
            <q-icon :name="prependIcon" @click="emitEvent('click:prepend')">
                <q-tooltip v-if="prependIconTooltip" anchor="top middle" self="center middle">
                    {{ prependIconTooltip }}
                </q-tooltip>
            </q-icon>
        </template>
        <template v-slot:append v-if="appendIcon">
            <q-icon :name="appendIcon" @click="emitEvent('click:append')">
                <q-tooltip v-if="appendIconTooltip" anchor="top middle" self="center middle">
                    {{ appendIconTooltip }}
                </q-tooltip>
            </q-icon>
        </template>
    </q-select>
</template>

<script lang="ts">
import { defineComponent, PropType } from 'vue';
import FieldCommon from './FieldCommon';
import FieldLabel from './FieldLabel.vue';
import { getNoIntonation } from '@/common/utilities';

export default defineComponent({
    name: 'field-edit-select',
    mixins: [FieldCommon],
    components: {
        FieldLabel
    },
    props: {
        clearable: {
            type: Boolean,
            default: false
        },
        beforeIcon: {
            type: String,
            default: null
        },
        beforeIconTooltip: {
            type: String,
            default: null
        },
        afterIcon: {
            type: String,
            default: null
        },
        afterIconTooltip: {
            type: String,
            default: null
        },
        prependIcon: {
            type: String,
            default: null
        },
        prependIconTooltip: {
            type: String,
            default: null
        },
        appendIcon: {
            type: String,
            default: null
        },
        appendIconTooltip: {
            type: String,
            default: null
        },
        options: {
            type: Array<any>,
            default: () => []
        },
        optionLabel: {
            type: String,
            default: 'label'
        },
        optionValue: {
            type: String,
            default: 'value'
        },
        optionDisable: {
            type: [String, Function, undefined] as PropType<string | ((_: any) => boolean) | undefined>,
            default: null
        },
        returnObject: {
            type: Boolean,
            default: false
        },
        filterable: {
            type: Boolean,
            default: true
        },
        multiple: {
            type: Boolean,
            default: false
        },
        maxValues: {
            type: Number,
            default: 999999999
        },
        chips: {
            type: Boolean,
            default: false
        },
        behavior: {
            type: String,
            default: 'menu'
        }
    },
    data() {
        const dataObj: {
            appliedfilter: string | null;
        } = {
            appliedfilter: null
        };
        return dataObj;
    },
    computed: {
        behaviorResolved(): 'default' | 'menu' | 'dialog' | undefined {
            if (this.behavior) return undefined;
            if (this.behavior === 'default' || this.behavior === 'menu' || this.behavior === 'dialog') {
                return this.behavior;
            }
            return undefined;
        },
        filteredOptions(): Array<any> {
            if (!this.options?.length) return [];
            const filterUppered: string = getNoIntonation(this.appliedfilter?.trim()).toUpperCase() || '';
            if (!filterUppered) return this.options;
            const res = this.options.filter((x: any) => {
                if (getNoIntonation(x[this.optionLabel]?.trim()).toUpperCase().includes(filterUppered)) return true;
                return false;
            });
            return res;
        }
    },
    methods: {
        applyFilter(value: string, update: any) {
            let newFilterValue: string | null = null;
            if (value) {
                newFilterValue = value.toString().trim();
            } else {
                newFilterValue = null;
            }
            if (this.appliedfilter !== newFilterValue) {
                this.appliedfilter = newFilterValue;
            }
            update(() => {});
        },
        updateModelValue(value: any) {
            if (!value) {
                if (this.multiple) this.modelObject[this.fieldId] = [];
                else this.modelObject[this.fieldId] = null;
            }
            this.valueChanged();
        }
    }
});
</script>
