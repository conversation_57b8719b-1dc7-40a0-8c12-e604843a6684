<template>
    <div v-if="declaration" class="full-width q-pb-sm q-px-xs row justify-center items-center view-declaration-data-content">
        <div class="full-width q-ma-none q-px-xs related-tins-data-table">
            <q-table
                :columns="relTinsTableColumns"
                :rows="declaration.relatedTinsData"
                :pagination="{ rowsPerPage: 0 }"
                hide-pagination
                :grid="$q?.screen?.width < 900"
                :dense="false"
                style="width: 100%"
                table-header-class="bg-primary text-white"
                class="related-tins-data-list-table"
            >
                <template v-slot:body-cell-id="props">
                    <q-td :props="props">
                        <div class="q-mx-sm row no-wrap justify-start items-center">
                            {{ $t(`relatedTin${props.value}`) }}
                        </div>
                    </q-td>
                </template>
                <template v-slot:body-cell-relStartDateText="props">
                    <q-td :props="props">
                        <div class="q-mx-sm row no-wrap justify-start items-center">
                            {{ props.row.relStartDateText }}
                        </div>
                    </q-td>
                </template>
                <template v-slot:body-cell-relProofDocDateText="props">
                    <q-td :props="props">
                        <div class="q-mx-sm row no-wrap justify-start items-center">
                            {{ props.row.relProofDocDateText }}
                        </div>
                    </q-td>
                </template>
                <template v-slot:item="props">
                    <div
                        class="q-px-xs q-py-sm col-12 grid-style-transition"
                        style="min-width: 300px; display: flex; flex-grow: 1"
                    >
                        <q-card bordered flat style="width: 100%; background-color: rgb(250, 250, 250)">
                            <div
                                v-for="col in props.cols.filter((col: any) => col.name !== 'actions')"
                                :key="col.name"
                                class="q-px-md"
                            >
                                <field-display
                                    class="full-width"
                                    :modelObject="props.row"
                                    :fieldId="col.name"
                                    :fieldTitle="col.label"
                                    :value="col.value"
                                />
                            </div>
                        </q-card>
                    </div>
                </template>
            </q-table>
        </div>
    </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { Declaration } from '../../models/m1/Entities/Declaration';
import FieldDisplay from '@/common/components/form/FieldDisplay.vue';

export default defineComponent({
    name: 'declaration-related-tins-data',
    components: {
        FieldDisplay
    },
    props: {
        declaration: {
            type: Declaration,
            default: null
        }
    },
    computed: {
        relTinsTableColumns(): any[] {
            return [
                {
                    name: 'id',
                    label: this.$t('relatedTinData').toString(),
                    field: 'id',
                    align: 'left',
                    sortable: true,
                    style: 'white-space: break-spaces'
                },
                {
                    name: 'relAfm',
                    label: this.$t('afm').toString(),
                    field: 'relAfm',
                    align: 'left',
                    sortable: true,
                    style: 'white-space: break-spaces width: 80px;'
                },
                {
                    name: 'relNames',
                    label: this.$t('fullName').toString(),
                    field: 'relNames',
                    align: 'left',
                    sortable: true,
                    style: 'white-space: break-spaces'
                },
                {
                    name: 'relEmail',
                    label: this.$t('email').toString(),
                    field: 'relEmail',
                    align: 'center',
                    sortable: true,
                    style: 'width: 150px;'
                },
                {
                    name: 'relStartDateText',
                    label: this.$t('relStartDate').toString(),
                    field: 'relStartDateText',
                    align: 'left',
                    sortable: true,
                    style: 'white-space: break-spaces width: 80px;'
                },
                {
                    name: 'relKind',
                    label: this.$t('relKind').toString(),
                    field: 'relKindDescription',
                    align: 'center',
                    sortable: true,
                    style: 'width: 150px;'
                },
                {
                    name: 'relProofDoc',
                    label: this.$t('relProofDoc').toString(),
                    field: 'relProofDocDescription',
                    align: 'left',
                    sortable: false,
                    style: 'white-space: break-spaces'
                },
                {
                    name: 'relProofDocNo',
                    label: this.$t('relProofDocNo').toString(),
                    field: 'relProofDocNo',
                    align: 'left',
                    sortable: true,
                    style: 'white-space: break-spaces'
                },
                {
                    name: 'relProofDocDateText',
                    label: this.$t('relProofDocDate').toString(),
                    field: 'relProofDocDateText',
                    align: 'left',
                    sortable: true,
                    style: 'white-space: break-spaces width: 80px;'
                },
                {
                    name: 'relProofDocAuthor',
                    label: this.$t('relProofDocAuthor').toString(),
                    field: 'relProofDocAuthor',
                    align: 'left',
                    sortable: false,
                    style: 'white-space: break-spaces'
                }
            ];
        }
    }
});
</script>

<style>
.view-declaration-data .field-group-header {
    padding: 6px 12px;
    background-color: var(--palette-primary-dark);
    color: white;
    border-radius: 6px;
}
.view-declaration-data .q-expansion-item .q-expansion-item__toggle-icon {
    color: white;
}
.view-declaration-data .declaration-link {
    color: var(--palette-primary-medium);
    font-weight: 600;
    cursor: pointer;
}
.view-declaration-data .success--text textarea {
    color: var(--palette-success) !important;
}
.view-declaration-data .warning--text textarea {
    color: var(--palette-warning) !important;
}
.view-declaration-data .error--text textarea {
    color: var(--palette-error) !important;
}
/* .related-tins-data-list-table thead tr th {
    position: sticky;
    z-index: 1;
}
.related-tins-data-list-table thead tr:first-child th {
    top: 0;
}
.related-tins-data-list-table .q-table__bottom {
    display: none;
} */

/* .related-tins-data-list-table {
    Remove fixed height to allow natural sizing
} */

.related-tins-data-list-table thead tr th {
    position: sticky;
    z-index: 1;
}

.related-tins-data-list-table thead tr:first-child th {
    top: 0;
}

.related-tins-data-list-table .q-table__bottom {
    display: none;
}

/* Remove any max-height constraints */
.related-tins-data-list-table .q-table__middle {
    max-height: none !important;
}
</style>
