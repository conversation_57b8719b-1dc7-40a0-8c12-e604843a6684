<template>
    <div class="q-mt-sm q-mb-sm full-width row wrap justify-center items-center view-declaration-actions">
        <q-btn
            size="md"
            icon="article"
            :label="$q?.screen?.gt.sm ? $t('printDeclaration') : ''"
            class="q-ma-md primary text-white"
            @click="printDeclaration"
        >
            <q-tooltip anchor="top left" self="center middle" class="pa-sm primary-dark">{{
                $t('printDeclaration')
            }}</q-tooltip>
        </q-btn>
    </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import DeclarationHelper from './DeclarationHelper';
import { Declaration } from '../../models/m1/Entities/Declaration';

export default defineComponent({
    name: 'view-declaration-actions',
    mixins: [DeclarationHelper],
    props: {
        declaration: {
            type: Declaration,
            default: null
        }
    },
    emits: ['printDeclaration', 'expireDeclaration'],
    computed: {
        isNewDeclaration(): boolean {
            return this.declaration?.isNew;
        }
    },
    methods: {
        printDeclaration() {
            this.$emit('printDeclaration');
        }
    }
});
</script>

<style scoped>
.view-declaration-actions-container button,
.view-declaration-actions-container .q-btn {
    padding: 12px 18px;
    border-radius: 8px;
}
</style>
