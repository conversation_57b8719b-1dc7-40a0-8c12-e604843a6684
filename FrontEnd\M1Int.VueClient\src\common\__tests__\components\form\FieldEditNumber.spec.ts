import { shallowMount, VueWrapper } from '@vue/test-utils';
import mockAlert from '@/mocks/MockAlert';
import FieldEditNumber from '@/common/components/form/FieldEditNumber.vue';
import { Component } from 'vue';

describe('FieldEditNumber', () => {
    let wrapper: VueWrapper;
    let component: Component;

    const model: any = {
        id: '001',
        number1: 123,
        number2: 4567.89
    };

    beforeEach(() => {
        wrapper = shallowMount(FieldEditNumber, {
            global: {
                mocks: {
                    $t: (key: string) => {
                        return key;
                    },
                    $alert: mockAlert
                }
            },
            props: {
                modelObject: model,
                fieldId: 'id',
                fieldTitle: 'Id'
            }
        });
        component = wrapper.vm as Component;
    });

    afterEach(() => {});

    it('should mount', () => {
        expect(wrapper).toBeTruthy();
        expect(wrapper.vm).toBeTruthy();
        expect(wrapper.vm.$el).toBeTruthy();
    });

    it('component should be valid', () => {
        expect(component).toBeTruthy();
    });

    it('should have valid $t property', () => {
        expect(wrapper.vm.$t).toBeTruthy();
        expect(typeof wrapper.vm.$t).toEqual('function');
    });

    it('should have valid $alert property', () => {
        expect((wrapper.vm as any).$alert).toBeTruthy();
    });

    it('should have valid field id value', async () => {
        await wrapper.setProps({
            modelObject: model,
            fieldId: 'id'
        });
        expect((component as any).getValue()).toEqual(model.id);
    });

    it('should have valid field number1 value', async () => {
        await wrapper.setProps({
            modelObject: model,
            fieldId: 'number1',
            fieldTitle: 'Number1'
        });
        expect((component as any).getValue()).toEqual(model.number1);
    });

    it('should have valid field number1 label', async () => {
        await wrapper.setProps({
            modelObject: model,
            fieldId: 'number1',
            fieldTitle: 'Number1'
        });
        expect(wrapper.attributes('label')).toContain((component as any).fieldTitle);
    });

    it('should have valid field number2 value', async () => {
        await wrapper.setProps({
            modelObject: model,
            fieldId: 'number2',
            fieldTitle: 'Number2'
        });
        expect((component as any).getValue()).toEqual(model.number2);
    });

    it('should have valid field number2 label', async () => {
        await wrapper.setProps({
            modelObject: model,
            fieldId: 'number2',
            fieldTitle: 'Number2'
        });
        expect(wrapper.attributes('label')).toContain((component as any).fieldTitle);
    });

    it('should update field number1 value', async () => {
        await wrapper.setProps({
            modelObject: model,
            fieldId: 'number1'
        });
        const newValue: number = 456;
        expect((component as any).getValue()).toEqual(model.number1);
        model.number1 = newValue;
        expect((component as any).getValue()).toEqual(newValue);
    });

    it('should update field number2 value', async () => {
        await wrapper.setProps({
            modelObject: model,
            fieldId: 'number2'
        });
        const newValue: number = 1234.56;
        expect((component as any).getValue()).toEqual(model.number2);
        model.number2 = newValue;
        expect((component as any).getValue()).toEqual(newValue);
    });
});
