import { shallowMount, VueWrapper } from '@vue/test-utils';
import mockAlert from '@/mocks/MockAlert';
import FieldLabel from '@/common/components/form/FieldLabel.vue';
import { Component } from 'vue';

describe('FieldLabel', () => {
    let wrapper: VueWrapper;
    let component: Component;

    beforeEach(() => {
        wrapper = shallowMount(FieldLabel, {
            global: {
                mocks: {
                    $t: (key: string) => {
                        return key;
                    },
                    $alert: mockAlert
                }
            }
        });
        component = wrapper.vm as Component;
    });

    afterEach(() => {});

    it('should mount', () => {
        expect(wrapper).toBeTruthy();
        expect(wrapper.vm).toBeTruthy();
        expect(wrapper.vm.$el).toBeTruthy();
    });

    it('component should be valid', () => {
        expect(component).toBeTruthy();
    });

    it('should have valid $t property', () => {
        expect(wrapper.vm.$t).toBeTruthy();
        expect(typeof wrapper.vm.$t).toEqual('function');
    });

    it('should have valid $alert property', () => {
        expect((wrapper.vm as any).$alert).toBeTruthy();
    });
});
