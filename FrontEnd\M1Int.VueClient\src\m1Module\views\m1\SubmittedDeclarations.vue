<template>
    <div class="full-width column justify-start items-start submitted-declarations">
        <div
            class="full-width row justify-center items-center primary-dark submitted-declarations-header"
            style="padding: 18px 0"
        >
            <div class="q-px-sm text-h6 text-center" style="font-weight: 500">
                {{ title }}
            </div>
        </div>
        <div class="full-width column justify-center items-center submitted-declarations-content">
            <declarations-filter-view
                :exportEnabled="exportEnabled"
                :exportCancelEnabled="exportCancelEnabled"
                :exportProcessMessage="exportProcessMessage"
                @filterChanged="filterChanged"
                @filterInvalid="filterInvalid"
                @exportToExcel="exportToExcel"
                @exportToCsv="exportToCsv"
                @cancelExport="cancelExport"
            />
            <submitted-declarations-table
                v-if="hasSubmittedDeclarations"
                :filteredSubmittedDeclarations="submittedDeclarationsLite"
                @filterChanged="filterChanged"
            />
        </div>
        <div
            v-if="dataLoaded && !hasSubmittedDeclarations"
            class="full-width row wrap justify-center items-center"
            style="margin-top: 100px"
        >
            <div class="col"></div>
            <div class="col-10 col-sm-8 col-md-8 col-lg-6 justify-center items-center">
                <div class="full-width text-h6 error--text text-center">
                    {{ $t('noSubmittedDeclarationsFound') }}
                </div>
            </div>
            <div class="col"></div>
        </div>
    </div>
</template>
<script lang="ts">
import { defineComponent } from 'vue';
import DeclarationHelper from '../../components/m1/DeclarationHelper';
import DeclarationsFilterView from '@/m1Module/components/m1/DeclarationsFilter.vue';
import SubmittedDeclarationsTable from '../../components/m1/SubmittedDeclarationsTable.vue';
import { DeclarationsFilter } from '@/m1Module/models/m1/Entities/DeclarationsFilter';
import dayjs from '@/plugins/dayjs';
import { ExportSubmittedDeclarationsToCsvResponse } from '@/m1Module/models/m1/Services/ExportSubmittedDeclarationsToCsv/ExportSubmittedDeclarationsToCsvResponse';
import { ExportSubmittedDeclarationsToExcelResponse } from '@/m1Module/models/m1/Services/ExportSubmittedDeclarationsToExcel/ExportSubmittedDeclarationsToExcelResponse';
import { saveFile } from '@/common/utilities';

export default defineComponent({
    name: 'submitted-declarations',
    mixins: [DeclarationHelper],
    components: {
        DeclarationsFilterView,
        SubmittedDeclarationsTable
    },
    data() {
        const dataObj: {
            dataLoaded: boolean;
            dataExporting: boolean;
            dataExportingCancelled: boolean;
            exportRowsPerPage: number;
            exportCurrentPage: number;
            exportEstimatedPages: number | null;
            exportEnabled: boolean;
            exportCancelEnabled: boolean;
            exportProcessMessage: String | null;
        } = {
            dataLoaded: false,
            dataExporting: false,
            dataExportingCancelled: false,
            exportRowsPerPage: 1000,
            exportCurrentPage: 0,
            exportEstimatedPages: null,
            exportEnabled: true,
            exportCancelEnabled: false,
            exportProcessMessage: null
        };
        return dataObj;
    },
    computed: {
        title(): string {
            return this.$t('submittedDeclarations').toString();
        },
        exportProcessMessage(): string | null {
            return this.exportProcessMessage;
        }
    },
    methods: {
        async loadData() {
            await this.commonStore?.getServerCurrentDateTime();
            this.initializeFilterData();
            await this.loadSubmittedDeclarations();
        },
        clearSubmittedDeclarations() {
            this.store?.clearSubmittedDeclarations();
        },
        async loadSubmittedDeclarations() {
            await this.store?.getSubmittedDeclarationsLite(this.store?.pagedDeclarationsFilter);
        },
        async exportSubmittedDeclarationsToCsv() {
            await this.store?.getSubmittedDeclarations(this.store?.pagedDeclarationsFilter);
            const response: ExportSubmittedDeclarationsToCsvResponse =
                await this.store?.exportSubmittedDeclarationsToCsv(this.store?.pagedDeclarationsFilter);
            if (response?.success && response?.result) {
                const csvBlob = new Blob([response.result], { type: 'text/csv' });
                const csvUrl = window.URL.createObjectURL(csvBlob);
                // setTimeout(() => openNewUrl({ url: csvUrl }), 200);
                saveFile(csvUrl, `${this.$t('submittedDeclarations')} ${dayjs().format('YYYY-MM-DD_HH-mm-ss')}.csv`);
                setTimeout(() => {
                    window.URL.revokeObjectURL(csvUrl);
                }, 100);
            }
        },
        async exportSubmittedDeclarationsToExcel() {
            await this.loadSubmittedDeclarations();
            if (this.store?.pagedDeclarationsFilter?.pagingParams?.rowsNumber) {
                this.exportEstimatedPages = Math.ceil(
                    this.store?.pagedDeclarationsFilter?.pagingParams?.rowsNumber / this.exportRowsPerPage
                );
            } else {
                this.exportEstimatedPages = null;
            }

            this.dataExporting = true;
            this.dataExportingCancelled = false;
            this.exportEnabled = false;
            this.exportCancelEnabled = true;
            this.exportCurrentPage = 0;
            this.exportProcessMessage = null;

            let finished = false;

            const blobResults: BlobPart[] = [];
            while (this.dataExporting && !this.dataExportingCancelled && !finished) {
                this.exportCurrentPage++;
                const response: ExportSubmittedDeclarationsToExcelResponse =
                    await this.store?.exportSubmittedDeclarationsToExcel({
                        ...this.store?.pagedDeclarationsFilter?.declarationsFilter,
                        pagingParams: { page: this.exportCurrentPage, rowsPerPage: this.exportRowsPerPage }
                    });
                // console.log(this.exportCurrentPage, ' / ', this.exportEstimatedPages);
                // console.log(response);
                if (response?.success && response?.result && response?.result?.byteLength > 0) {
                    if (this.dataExporting)
                        this.exportProcessMessage = this.$t('exportProcess', [
                            this.exportCurrentPage,
                            this.exportEstimatedPages
                        ]);
                    // console.log(this.exportProcessMessage);
                    blobResults.push(response.result);
                    if (this.exportEstimatedPages && this.exportCurrentPage == this.exportEstimatedPages) {
                        finished = true;
                    }
                } else {
                    finished = true;
                }
            }
            if (!this.dataExportingCancelled && blobResults.length > 0) {
                this.exportProcessMessage = this.$t('exportProcessCompleted', [this.exportCurrentPage]);
                const excelBlob = new Blob(blobResults, {
                    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                });
                const excelUrl = window.URL.createObjectURL(excelBlob);
                // setTimeout(() => openNewUrl({ url: excelUrl }), 200);
                saveFile(excelUrl, `${this.$t('submittedDeclarations')} ${dayjs().format('YYYY-MM-DD_HH-mm-ss')}.xlsx`);
                setTimeout(() => {
                    window.URL.revokeObjectURL(excelUrl);
                }, 100);
            }
            // console.log(this.exportProcessMessage);
            this.dataExporting = false;
            this.exportEnabled = true;
            this.exportCancelEnabled = false;
            this.exportCurrentPage = 0;
            this.exportEstimatedPages = null;
        },
        cancelExport() {
            this.exportProcessMessage = this.$t('exportProcessCanceled', [this.exportCurrentPage]);
            // console.log(this.exportProcessMessage);
            this.dataExporting = false;
            this.dataExportingCancelled = true;
            this.exportEnabled = true;
            this.exportCancelEnabled = false;
            // this.exportCurrentPage = 0;
            // this.exportEstimatedPages = null;
        },
        // async exportSubmittedDeclarationsToExcelOLD() {
        //     let currentPage = 1;
        //     let finished = false;

        //     while (!finished) {
        //         const response: ExportSubmittedDeclarationsToExcelResponse =
        //             await this.store?.exportSubmittedDeclarationsToExcel({
        //                 ...this.store?.pagedDeclarationsFilter?.declarationsFilter,
        //                 pagingParams: { page: currentPage, rowsPerPage: 100 }
        //             });
        //         console.log(response);
        //         if (response?.success && response?.result && response?.result?.byteLength > 0) {
        //             const excelBlob = new Blob([response.result], {
        //                 type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        //             });
        //             const excelUrl = window.URL.createObjectURL(excelBlob);
        //             // setTimeout(() => openNewUrl({ url: excelUrl }), 200);
        //             saveFile(
        //                 excelUrl,
        //                 `${this.$t('submittedDeclarations')} ${dayjs().format('YYYY-MM-DD_HH-mm-ss')}.xlsx`
        //             );
        //             setTimeout(() => {
        //                 window.URL.revokeObjectURL(excelUrl);
        //             }, 100);
        //             currentPage++;
        //         } else {
        //             finished = true;
        //         }
        //     }
        // },
        initializeFilterData() {
            if (!this.store?.pagedDeclarationsFilter) return;
        },
        filterHasCriteria(filter: DeclarationsFilter | null): boolean {
            if (!filter) return false;
            return !!(
                filter.id ||
                filter.m1Afm ||
                filter.m1IdNumber ||
                filter.m1SurNameA ||
                filter.m1SurNameB ||
                filter.m1Name ||
                filter.m1RepresentativeAfm
            );
        },
        async filterChanged(filter: any) {
            this.store?.setDeclarationsFilter(filter);
            await this.loadSubmittedDeclarations();
        },
        filterInvalid() {
            this.clearSubmittedDeclarations();
        },
        async exportToCsv(filter: DeclarationsFilter) {
            if (!this.filterHasCriteria(filter)) {
                this.clearSubmittedDeclarations();
                return;
            }
            this.store?.setDeclarationsFilter(filter);
            await this.exportSubmittedDeclarationsToCsv();
        },
        async exportToExcel(_filter: DeclarationsFilter) {
            // console.log('exportToExcel =', filter);
            // if (!this.filterHasCriteria(filter)) {
            //     this.clearSubmittedDeclarations();
            //     return;
            // }
            // this.store?.setDeclarationsFilter(filter);
            // console.log('after setDeclarationsFilter =', this.store.pagedDeclarationsFilter);
            await this.exportSubmittedDeclarationsToExcel();
        }
    },
    async created() {
        this.dataLoaded = false;
        await this.loadData();
        this.dataLoaded = true;
    }
});
</script>

<style>
.submitted-declarations .submitted-declarations-content .list-header .list-header-content .content-section-center {
    max-width: 600px;
}

.submitted-declarations
    .submitted-declarations-content
    .list-header
    .list-header-content
    .content-section-center
    .search-input {
    min-width: 200px !important;
}
</style>
