<template>
    <q-input
        :id="elementId"
        :for="elementId"
        :name="elementName"
        v-model="inputValue"
        :error-message="validationMessage"
        :error="isDirty && isInvalid"
        :noErrorIcon="true"
        :type="typeResolved"
        :class="classResolved"
        :label="label"
        :placeholder="placeholder || datetimeFormat"
        :hint="hint"
        :hideHint="hideHint"
        :mask="mask"
        :fillMask="fillMask"
        :outlined="outlined"
        :filled="filled"
        :borderless="borderless"
        :rounded="rounded"
        :bottomSlots="bottomSlots"
        :hideBottomSpace="!bottomSlots"
        :dense="dense"
        :clearable="clearable && !disabled && !readonly"
        clear-icon="close"
        :disable="disabled"
        :readonly="readonly"
        :autofocus="autofocus && !disabled && !readonly"
        @keyup.enter="checkValueChanged"
        @clear="checkValueChanged"
        @blur="checkValueChanged"
    >
        <template v-slot:label>
            <field-label :label="label" :hint="labelInfo" :isTooltip="isLabelInfoTooltip" />
        </template>
        <template v-slot:append>
            <q-icon v-if="isDateTime || isDate" name="event" class="cursor-pointer">
                <q-popup-proxy cover transition-show="scale" transition-hide="scale">
                    <q-date
                        v-model="inputValue"
                        today-btn
                        :mask="datetimeFormat"
                        :minimal="minimal"
                        :no-unset="noUnset"
                        :options="dateOptions"
                        :navigation-min-year-month="navigationMinYearMonth"
                        :navigation-max-year-month="navigationMaxYearMonth"
                        @update:model-value="valueUpdated"
                    >
                        <div class="row items-center justify-end">
                            <q-btn v-close-popup :label="$t('close')" color="primary" flat />
                        </div>
                    </q-date>
                </q-popup-proxy>
            </q-icon>
            <q-icon v-if="isDateTime || isTime" name="access_time" class="cursor-pointer">
                <q-popup-proxy cover transition-show="scale" transition-hide="scale">
                    <q-time
                        v-model="inputValue"
                        now-btn
                        :mask="datetimeFormat"
                        format24h
                        :minimal="minimal"
                        :no-unset="noUnset"
                        @update:model-value="valueUpdated"
                    >
                        <div class="row items-center justify-end">
                            <q-btn v-close-popup :label="$t('close')" color="primary" flat />
                        </div>
                    </q-time>
                </q-popup-proxy>
            </q-icon>
        </template>
    </q-input>
</template>

<script lang="ts">
import { defineComponent, PropType } from 'vue';
import FieldEditTextCommon from './FieldEditTextCommon';
import dayjs from '@/plugins/dayjs';

export default defineComponent({
    name: 'field-edit-date-time',
    mixins: [FieldEditTextCommon],
    props: {
        minimal: {
            type: Boolean,
            default: false
        },
        noUnset: {
            type: Boolean,
            default: false
        },
        dateOptions: {
            type: [Array, Function, undefined] as PropType<any[] | ((_: string) => boolean) | undefined>,
            default: undefined
        },
        navigationMinYearMonth: {
            type: String,
            default: undefined
        },
        navigationMaxYearMonth: {
            type: String,
            default: undefined
        }
    },
    data() {
        const dataObj: {
            inputValue: string | null;
        } = {
            inputValue: null
        };
        return dataObj;
    },
    computed: {
        datetimeFormat(): string {
            if (this.isDate) {
                return this.$t('dateFormat');
            } else if (this.isDateTime) {
                return this.$t('dateTimeFormat');
            } else if (this.isTime) {
                return this.$t('timeFormat');
            } else {
                return '';
            }
        }
    },
    methods: {
        resumeInputValue() {
            this.inputValue = null;
            const fieldValue: any = this.getValue();
            if (fieldValue === undefined || fieldValue === null) return;
            const dtStrValue = this.isTime ? fieldValue : dayjs(fieldValue).format(this.datetimeFormat);
            this.inputValue = dtStrValue;
        },
        setFieldValue(value: Date | string | null, resumeInputValue: boolean = true) {
            if (this.modelObject[this.fieldId] !== value) {
                this.modelObject[this.fieldId] = value;
            }
            if (resumeInputValue) this.resumeInputValue();
        },
        getInputDateTimeValue(): Date | string | null {
            if (this.inputValue == undefined || this.inputValue === null || this.inputValue.length <= 0) {
                return null;
            }
            let dtValue: Date | string | null = null;
            if (this.isTime) {
                dtValue = this.inputValue;
            } else {
                const dayjsValue = dayjs(this.inputValue, this.datetimeFormat, true);
                if (dayjsValue.isValid()) {
                    dtValue = dayjsValue.toDate();
                } else {
                    dtValue = null;
                    this.inputValue = null;
                }
            }
            return dtValue;
        },
        valueUpdated() {
            this.checkValueChanged();
            this.$nextTick(this.fixTabItems);
        },
        checkValueChanged(): boolean {
            const curValue = this.getValue();
            const newValue: Date | string | null = this.getInputDateTimeValue();
            if (!dayjs(curValue).isSame(dayjs(newValue))) {
                this.processFieldValue();
                this.valueChanged();
                return true;
            }
            return false;
        },
        processFieldValue() {
            const dtValue: Date | string | null = this.getInputDateTimeValue();
            if (dtValue === null) {
                this.setFieldValue(null, false);
            } else {
                this.setFieldValue(dtValue);
            }
        }
    },
    created() {
        this.resumeInputValue();
    }
});
</script>
