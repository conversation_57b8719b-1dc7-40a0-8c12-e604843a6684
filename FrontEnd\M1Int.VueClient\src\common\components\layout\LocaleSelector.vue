<template>
    <q-btn flat round icon="language" class="main-locale-menu-button" :aria-label="$t('language')">
        <q-menu auto-close class="main-locale-menu">
            <q-list class="locales-list">
                <q-item
                    clickable
                    v-for="item in menuItems"
                    :key="item.id"
                    :class="item.class"
                    :aria-label="item.label"
                    @click="item.command"
                >
                    <div class="row justify-start items-center">
                        <q-icon name="done" class="q-px-xs locale-icon" />
                        <div class="q-px-xs locale-title">{{ item.label }}</div>
                    </div>
                </q-item>
            </q-list>
        </q-menu>
    </q-btn>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { Locale } from '@/common/models/Locale';
import config from '@/config';
import dayjs from '@/plugins/dayjs';
import qLangEn from 'quasar/lang/en-US';
import qLangEl from 'quasar/lang/el';

export default defineComponent({
    name: 'locale-selector',
    data() {
        const dataObj: {
            currentLocale: string;
        } = {
            currentLocale: config.locale
        };
        return dataObj;
    },
    computed: {
        locales(): Array<Locale> {
            return config.locales;
        },
        menuItems(): Array<any> {
            const res: Array<any> = this.locales.map((x: Locale) => {
                return {
                    id: x.id,
                    label: this.$t(x.title),
                    class: `locale-item ${x.id} ${this.isCurrentLocale(x.id) ? 'selected' : ''}`,
                    command: () => this.setLocale(x.id)
                };
            });
            return res;
        },
        currentLocaleTitle() {
            return this.currentLocale?.toUpperCase();
        }
    },
    methods: {
        isCurrentLocale(locale: string): boolean {
            return locale?.toLowerCase() === this.currentLocale?.toLowerCase();
        },
        setCurrentLocale() {
            config.locale = this.currentLocale;
            dayjs.locale(this.currentLocale);
            this.$q?.lang.set(this.currentLocale === 'el' ? qLangEl : qLangEn);
            this.$i18n.locale = this.currentLocale;
        },
        setLocale(locale: string) {
            if (!locale || this.currentLocale === locale) return;
            this.currentLocale = locale;
            this.setCurrentLocale();
        }
    }
});
</script>

<style>
.main-locale-menu .locale-item {
    width: 130px;
    min-height: 24px;
    padding: 8px 12px;
}
.main-locale-menu .locale-item .locale-icon {
    visibility: hidden !important;
}
.main-locale-menu .locale-item.selected .locale-icon {
    visibility: visible !important;
}
</style>
