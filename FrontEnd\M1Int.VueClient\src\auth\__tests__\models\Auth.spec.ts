import { LoginModel, RegistrationModel, UserAuthData } from '@/auth/models/Auth';

const testLoginData: any = {
    userName: 'test_user_name',
    password: 'test_user_password'
};

const testRegistrationData: any = {
    userName: 'test_user_name',
    password: 'test_user_password'
};

const testUserAuthData: any = {
    userId: 'test_user_id',
    userName: 'test_user_name',
    userEmail: '<EMAIL>'
};

describe('LoginModel', () => {
    let loginData: LoginModel;

    beforeEach(() => {
        loginData = new LoginModel(testLoginData);
    });

    it('should be valid', () => {
        expect(loginData).toBeTruthy();
    });

    it('should have valid data', () => {
        expect(loginData.userName).toEqual(testLoginData.userName);
        expect(loginData.password).toEqual(testLoginData.password);
    });
});

describe('RegistrationModel', () => {
    let registrationData: RegistrationModel;

    beforeEach(() => {
        registrationData = new LoginModel(testRegistrationData);
    });

    it('should be valid', () => {
        expect(registrationData).toBeTruthy();
    });

    it('should have valid data', () => {
        expect(registrationData.userName).toEqual(testRegistrationData.userName);
        expect(registrationData.password).toEqual(testRegistrationData.password);
    });
});

describe('UserAuthData', () => {
    let authData: UserAuthData;

    beforeEach(() => {
        authData = new UserAuthData(testUserAuthData);
    });

    it('should be valid', () => {
        expect(authData).toBeTruthy();
    });

    it('should have valid data', () => {
        expect(authData.userId).toEqual(testUserAuthData.userId);
        expect(authData.userName).toEqual(testUserAuthData.userName);
        expect(authData.userEmail).toEqual(testUserAuthData.userEmail);
    });
});
