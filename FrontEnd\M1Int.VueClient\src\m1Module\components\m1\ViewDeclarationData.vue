<template>
    <div class="full-width q-mt-sm view-declaration-data">
        <div
            v-if="declaration"
            :key="declarationKey"
            class="full-width q-px-xs row justify-center items-center view-declaration-data-content"
        >
            <div v-if="warningMessage" class="col-12 q-pb-sm">
                <q-banner inline-actions rounded class="bg-orange text-white text-weight-bold">
                    <div class="q-py-xs q-px-sm text-center" v-html="warningMessage"></div>
                </q-banner>
            </div>
            <div class="full-width row declaration-data-container">
                <div
                    v-if="declaration.m1ReplacedBy"
                    class="col-12 col-sm-12 col-md-12 q-py-sm q-px-md warning--text font-weight-bold"
                >
                    <span @click="displayReplacedByDeclaration" class="link-style">{{
                        $t('declarationReplacedBy', [declaration.m1ReplacedBy])
                    }}</span>
                </div>
                <q-expansion-item
                    v-model="personalDataExpanded"
                    icon="person_outline"
                    :label="$t('personalIdentificationData')"
                    class="full-width q-pt-xs q-px-sm"
                    header-class="field-group-header"
                >
                <declaration-personal-data :declaration="declaration" />
                </q-expansion-item>
                <q-expansion-item
                    v-model="addressDataExpanded"
                    icon="place"
                    :label="contactDataTitle"
                    class="full-width q-pt-xs q-px-sm"
                    header-class="field-group-header"
                >
                    <declaration-contact-data :declaration="declaration" />
                </q-expansion-item>
                <q-expansion-item
                    v-if="declaration?.relatedTinsData?.length > 0"
                    v-model="relatedTinsDataExpanded"
                    icon="people_outline"
                    :label="relatedTinsDataTitle"
                    class="full-width q-pt-xs q-px-sm"
                    header-class="field-group-header"
                >
                    <declaration-related-tins-data :declaration="declaration" />
                </q-expansion-item>
                <q-expansion-item
                    v-if="declaration?.attachments?.length > 0"
                    v-model="attachmentsExpanded"
                    icon="attach_file"
                    :label="attachmentsTitle"
                    class="full-width q-pt-xs q-px-sm"
                    header-class="field-group-header"
                >
                    <div class="full-width q-mt-xs row">
                        <div class="col-12 col-md-12 col-lg-12 q-px-md q-pb-xs">
                            <declaration-attachments :declaration="declaration" />
                        </div>
                    </div>
                </q-expansion-item>
                <q-expansion-item
                    v-if="declaration?.emails?.length > 0"
                    v-model="sentEmailsDataExpanded"
                    icon="mark_email_read"
                    :label="sentEmailsTitle"
                    class="full-width q-pt-xs q-px-sm"
                    header-class="field-group-header"
                >
                    <declaration-emails :declaration="declaration" />
                </q-expansion-item>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import { PropType, defineComponent } from 'vue';
import { Validation } from '@vuelidate/core';
import DeclarationHelper from './DeclarationHelper';
import { Declaration } from '../../models/m1/Entities/Declaration';
import DeclarationPersonalData from './DeclarationPersonalData.vue';
import DeclarationContactData from './DeclarationContactData.vue';
import DeclarationRelatedTinsData from './DeclarationRelatedTinsData.vue';
import DeclarationAttachments from './DeclarationAttachments.vue';
import DeclarationEmails from './DeclarationEmails.vue';

export default defineComponent({
    name: 'view-declaration-data',
    mixins: [DeclarationHelper],
    components: {
        DeclarationPersonalData,
        DeclarationContactData,
        DeclarationRelatedTinsData,
        DeclarationAttachments,
        DeclarationEmails
    },
    props: {
        declaration: {
            type: Declaration,
            default: null
        },
        relatedDeclaration: {
            type: Object as PropType<Declaration | null>,
            default: null
        },
        validationObject: {
            type: Object as PropType<Validation>,
            default: null
        }
    },
    data() {
        const dataObj: {
            warningMessage: string | null;
            personalDataExpanded: boolean;
            addressDataExpanded: boolean;
            relatedTinsDataExpanded: boolean;
            attachmentsExpanded: boolean;
            sentEmailsDataExpanded: boolean;
        } = {
            warningMessage: null,
            personalDataExpanded: true,
            addressDataExpanded: false,
            relatedTinsDataExpanded: false,
            attachmentsExpanded: false,
            sentEmailsDataExpanded: false
        };
        return dataObj;
    },
    computed: {
        declarationKey(): string {
            return `${this.declaration?.id || 'declaration'}`;
        },
        contactDataTitle(): string {
            const descr: string | null = this.declaration?.contactDescription || null;
            if (descr) return `${this.$t('contactData')}: ${descr}`;
            else return this.$t('contactData');
        },
        relatedTinsDataTitle(): string {
            const descr: string | null = this.declaration?.relatedTinsDescription || null;
            if (descr) return `${this.$t('relatedTinsData')}: ${descr}`;
            else return this.$t('relatedTinsData');
        },
        attachmentsTitle(): string {
            let res: string = this.$t('attachments');
            if (!this.declaration || !this.declaration.attachments.length) return res;
            res += ` (${this.declaration.attachments.length}`;
            res += ` ${this.declaration.attachments.length === 1 ? this.$t('file') : this.$t('files')})`;
            res += `: ${this.declaration.attachmentsDescription}`;
            return res;
        },
        sentEmailsTitle(): string {
            let res: string = this.$t('sentEmails');
            if (!this.declaration || !this.declaration.emails.length) return res;
            res += ` (${this.declaration.emails.length})`;
            res += `: ${this.declaration.emailsDescription}`;
            return res;
        }
    },
    watch: {
        declaration() {
            this.$nextTick(() => this.resumeWarningMessage());
        }
    },
    methods: {
        resumeWarningMessage() {
            this.warningMessage = null;
        },
        displayReplacedByDeclaration() {
            if (!this.declaration?.m1ReplacedBy) return;
            this.$router?.push(`/viewDeclaration/${this.declaration?.m1ReplacedBy}`);
        }
    },
    async mounted() {
        this.$nextTick(() => this.resumeWarningMessage());
    }
});
</script>

<style>
.view-declaration-data .field-group-header {
    padding: 6px 12px;
    background-color: var(--palette-primary-dark);
    color: white;
    border-radius: 6px;
}
.view-declaration-data .q-expansion-item .q-expansion-item__toggle-icon {
    color: white;
}
.view-declaration-data .declaration-link {
    color: var(--palette-primary-medium);
    font-weight: 600;
    cursor: pointer;
}
.view-declaration-data .success--text textarea {
    color: var(--palette-success) !important;
}
.view-declaration-data .warning--text textarea {
    color: var(--palette-warning) !important;
}
.view-declaration-data .error--text textarea {
    color: var(--palette-error) !important;
}

.link-style {
    text-decoration: underline;
    cursor: pointer;
}
</style>
