<template>
    <q-input
        :id="elementId"
        :for="elementId"
        :name="elementName"
        :type="typeResolved"
        :model-value="valueResolved"
        :error-message="validationMessage"
        :error="isDirty && isInvalid"
        :noErrorIcon="true"
        :class="classResolved"
        :label="label"
        :placeholder="placeholder"
        :hint="hint"
        :hideHint="hideHint"
        :autogrow="autogrow"
        :outlined="outlined"
        :filled="filled"
        :borderless="borderless"
        :rounded="rounded"
        :bottomSlots="bottomSlots"
        :hideBottomSpace="!bottomSlots"
        :dense="dense"
        :clearable="false"
        :disable="disabled"
        :readonly="true"
    >
        <template v-slot:label>
            <field-label :label="label" :hint="labelInfo" :isTooltip="isLabelInfoTooltip" />
        </template>
        <template v-slot:before v-if="beforeIcon">
            <q-icon :name="beforeIcon" @click="emitEvent('click:before')">
                <q-tooltip v-if="beforeIconTooltip" anchor="top middle" self="center middle">
                    {{ beforeIconTooltip }}
                </q-tooltip>
            </q-icon>
        </template>
        <template v-slot:after v-if="afterIcon">
            <q-icon :name="afterIcon" @click="emitEvent('click:after')">
                <q-tooltip v-if="afterIconTooltip" anchor="top middle" self="center middle">
                    {{ afterIconTooltip }}
                </q-tooltip>
            </q-icon>
        </template>
        <template v-slot:prepend v-if="prependIcon">
            <q-icon :name="prependIcon" @click="emitEvent('click:prepend')">
                <q-tooltip v-if="prependIconTooltip" anchor="top middle" self="center middle">
                    {{ prependIconTooltip }}
                </q-tooltip>
            </q-icon>
        </template>
        <template v-slot:append v-if="appendIcon">
            <q-icon :name="appendIcon" @click="emitEvent('click:append')">
                <q-tooltip v-if="appendIconTooltip" anchor="top middle" self="center middle">
                    {{ appendIconTooltip }}
                </q-tooltip>
            </q-icon>
        </template>
    </q-input>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import dayjs from '@/plugins/dayjs';
import FieldEditTextCommon from './FieldEditTextCommon';

export default defineComponent({
    name: 'field-display',
    mixins: [FieldEditTextCommon],
    props: {
        autogrow: {
            type: Boolean,
            default: true
        },
        value: {
            type: String,
            default: null
        },
        valueFieldId: {
            type: String,
            default: null
        },
        valueIfEmpty: {
            type: String,
            default: ' '
        }
    },
    computed: {
        valueResolved(): string | null {
            let resValue: any;
            if (this.value) {
                resValue = this.value;
            } else if (this.valueFieldId && this.modelObject) {
                resValue = this.modelObject[this.valueFieldId];
            } else {
                resValue = this.getValue();
            }
            if (resValue === undefined || resValue === null) return this.valueIfEmpty;
            let strValue: string = '';
            if (this.isDate) {
                strValue = dayjs(resValue).format(this.$t('dateFormat').toString());
            } else if (this.isDateTime) {
                strValue = dayjs(resValue).format(this.$t('dateTimeFormat').toString());
            } else if (this.isTime) {
                strValue = dayjs(resValue).format(this.$t('timeFormat').toString());
            } else {
                strValue = resValue.toString();
            }
            if (!strValue) strValue = this.valueIfEmpty;
            return strValue;
        }
    }
});
</script>
