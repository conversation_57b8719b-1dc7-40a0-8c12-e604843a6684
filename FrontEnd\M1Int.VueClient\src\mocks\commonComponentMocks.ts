import { IAlert } from '@/common/utilities/IAlert';
import mockAlert from '@/mocks/MockAlert';
import { createTestingPinia } from '@pinia/testing';

export const commonComponentMocks: {
    $t: (key: string) => string;
    $alert: IAlert;
    $q: any;
} = {
    $t: (key: string) => {
        return key;
    },
    $alert: mockAlert,
    $q: null
};

export const commonComponentPlugins: any[] = [createTestingPinia({ stubActions: false })];
