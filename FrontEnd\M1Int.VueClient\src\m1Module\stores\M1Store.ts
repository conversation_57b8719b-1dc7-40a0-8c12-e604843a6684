import { Store, defineStore } from 'pinia';
import authService from '@/auth/services/AuthService';
import { M1State } from './M1State';
import { IM1Service, M1Service } from '../services/M1Service';
import { DeclarationsOptions } from '../models/m1/Entities/DeclarationsOptions';
import { GetDeclarationsOptionsRequest } from '../models/m1/Services/GetDeclarationsOptions/GetDeclarationsOptionsRequest';
import { GetDeclarationsOptionsResponse } from '../models/m1/Services/GetDeclarationsOptions/GetDeclarationsOptionsResponse';
import { DeclarationLite } from '../models/m1/Entities/DeclarationLite';
import { GetSubmittedDeclarationsLiteResponse } from '../models/m1/Services/GetSubmittedDeclarationsLite/GetSubmittedDeclarationsLiteResponse';
import { GetSubmittedDeclarationsLiteRequest } from '../models/m1/Services/GetSubmittedDeclarationsLite/GetSubmittedDeclarationsLiteRequest';
import { GetDeclarationReportRequest } from '../models/m1/Services/GetDeclarationReport/GetDeclarationReportRequest';
import { GetDeclarationRequest } from '../models/m1/Services/GetDeclaration/GetDeclarationRequest';
import { GetDeclarationResponse } from '../models/m1/Services/GetDeclaration/GetDeclarationResponse';
import { GetDeclarationAttachmentRequest } from '../models/m1/Services/GetDeclarationAttachment/GetDeclarationAttachmentRequest';
import { PagedDeclarationsFilter } from '../models/m1/Entities/PagedDeclarationsFilter';
import { ExportSubmittedDeclarationsToCsvRequest } from '../models/m1/Services/ExportSubmittedDeclarationsToCsv/ExportSubmittedDeclarationsToCsvRequest';
import { ExportSubmittedDeclarationsToCsvResponse } from '../models/m1/Services/ExportSubmittedDeclarationsToCsv/ExportSubmittedDeclarationsToCsvResponse';
import { ExportSubmittedDeclarationsToExcelRequest } from '../models/m1/Services/ExportSubmittedDeclarationsToExcel/ExportSubmittedDeclarationsToExcelRequest';
import { ExportSubmittedDeclarationsToExcelResponse } from '../models/m1/Services/ExportSubmittedDeclarationsToExcel/ExportSubmittedDeclarationsToExcelResponse';
import { PagingParams } from '../models/m1/Entities/PagingParams';
import { PagedDeclarationsLite } from '../models/m1/Entities/PagedDeclarationsLite';
import { DeclarationsFilter } from '../models/m1/Entities/DeclarationsFilter';
import { GetSubmittedDeclarationsResponse } from '../models/m1/Services/GetSubmittedDeclarations/GetSubmittedDeclarationsResponse';
import { GetSubmittedDeclarationsRequest } from '../models/m1/Services/GetSubmittedDeclarations/GetSubmittedDeclarationsRequest';

type M1StoreType = Store<'m1Store', M1State>;

export interface IM1Store extends M1StoreType {
    processing: boolean;

    declarationsOptions: DeclarationsOptions;
    pagedDeclarationsFilter: PagedDeclarationsFilter | null;
    submittedDeclarationsLite: Array<DeclarationLite>;

    setProcessing(value: boolean): void;

    clearDeclarationsOptions(): void;
    setDeclarationsOptions(declarationsOptions: any): void;
    clearDeclarationsFilter(): void;
    setDeclarationsFilter(filter: any): void;
    clearSubmittedDeclarations(): void;
    setSubmittedDeclarations(data: PagedDeclarationsLite | null): void;

    getDeclarationsOptions(): Promise<DeclarationsOptions>;
    getDeclaration(options?: any): Promise<GetDeclarationResponse>;
    getSubmittedDeclarationsLite(options?: any): Promise<GetSubmittedDeclarationsLiteResponse>;
    getSubmittedDeclarations(options?: any): Promise<GetSubmittedDeclarationsResponse>;
    exportSubmittedDeclarationsToCsv(options?: any): Promise<ExportSubmittedDeclarationsToCsvResponse>;
    exportSubmittedDeclarationsToExcel(options?: any): Promise<ExportSubmittedDeclarationsToExcelResponse>;
    getDeclarationAttachment(request: GetDeclarationAttachmentRequest): Promise<any>;
    getDeclarationReport(request: GetDeclarationReportRequest): Promise<any>;
}

export function getM1Store(service?: IM1Service): IM1Store {
    service = service || new M1Service();
    const state: M1State = new M1State();
    const useStore = defineStore('m1Store', {
        state: () => ({ ...state }),
        getters: {
            processing(state: M1State) {
                return state._processing;
            },
            declarationsOptions(state: M1State): DeclarationsOptions {
                return state._declarationsOptions || new DeclarationsOptions();
            },
            pagedDeclarationsFilter(state: M1State): PagedDeclarationsFilter | null {
                return state._pagedDeclarationsFilter;
            },
            submittedDeclarationsLite(state: M1State): Array<DeclarationLite> | null {
                return state._submittedDeclarationsLite;
            }
        },
        actions: {
            setProcessing(value: boolean) {
                this.$patch({ _processing: value });
            },
            clearDeclarationsOptions(): void {
                this.$patch({ _declarationsOptions: new DeclarationsOptions() });
            },
            setDeclarationsOptions(declarationsOptions: any): void {
                if (declarationsOptions) {
                    if (declarationsOptions instanceof DeclarationsOptions)
                        this.$patch({ _declarationsOptions: declarationsOptions });
                    else this.$patch({ _declarationsOptions: new DeclarationsOptions(declarationsOptions) });
                } else {
                    this.clearDeclarationsOptions();
                }
            },
            clearDeclarationsFilter() {
                this.$patch({ _pagedDeclarationsFilter: new PagedDeclarationsFilter() });
            },
            setDeclarationsFilter(filter: any) {
                if (filter) {
                    if (filter instanceof PagedDeclarationsFilter) {
                        this.$patch({ _pagedDeclarationsFilter: filter });
                        return;
                    }

                    if (filter instanceof DeclarationsFilter) {
                        const newPagedFilter = new PagedDeclarationsFilter({
                            declarationsFilter: filter,
                            pagingParams: new PagingParams()
                        });
                        this.$patch({ _pagedDeclarationsFilter: newPagedFilter });
                        return;
                    }

                    if (filter instanceof PagingParams) {
                        const newPagedFilter = new PagedDeclarationsFilter({
                            declarationsFilter: this._pagedDeclarationsFilter?.declarationsFilter || new DeclarationsFilter(),
                            pagingParams: filter
                        });
                        this.$patch({ _pagedDeclarationsFilter: newPagedFilter });
                        return;
                    }

                    this.$patch({ _pagedDeclarationsFilter: new PagedDeclarationsFilter(filter) });
                } else {
                    this.clearDeclarationsFilter();
                }
            },
            clearSubmittedDeclarations() {
                this.$patch({ _submittedDeclarationsLite: null });
                if (this.pagedDeclarationsFilter?.pagingParams) {
                    this.pagedDeclarationsFilter.pagingParams.rowsNumber = 0;
                }

            },
            setSubmittedDeclarations(data: PagedDeclarationsLite | null) {
                if (data?.pagingParams) {
                    if (this.pagedDeclarationsFilter)
                        this.pagedDeclarationsFilter.pagingParams = data.pagingParams;
                }
                if (data?.declarations?.length) {
                    if (data.declarations[0] instanceof DeclarationLite) {
                        this.$patch({ _submittedDeclarationsLite: data.declarations });
                    } else {
                        this.$patch({ _submittedDeclarationsLite: data.declarations.map((x: any) => new DeclarationLite(x)) });
                    }
                } else {
                    this.clearSubmittedDeclarations();
                }
            },
            async getDeclarationsOptions(): Promise<DeclarationsOptions> {
                try {
                    if (!service) return new DeclarationsOptions();
                    if (this._declarationsOptions) {
                        return this.declarationsOptions;
                    }
                    this.setProcessing(true);
                    const request: GetDeclarationsOptionsRequest = new GetDeclarationsOptionsRequest();
                    const response: GetDeclarationsOptionsResponse = await service.getDeclarationsOptions(request);
                    if (response.success && response.result) {
                        this.setDeclarationsOptions(response.result);
                    }
                    this.setProcessing(false);
                    return this.declarationsOptions;
                } catch (ex: any) {
                    console.error(ex);
                    this.setProcessing(false);
                    throw ex;
                } finally {
                    this.setProcessing(false);
                }
            },
            async getDeclaration(options?: any): Promise<GetDeclarationResponse> {
                try {
                    if (!service || !authService?.authData?.userName) {
                        return new GetDeclarationResponse();
                    }
                    this.setProcessing(true);
                    const request: GetDeclarationRequest = new GetDeclarationRequest(options);
                    const response: GetDeclarationResponse = await service.getDeclaration(request);
                    this.setProcessing(false);
                    return response;
                } catch (ex: any) {
                    console.error(ex);
                    this.setProcessing(false);
                    throw ex;
                } finally {
                    this.setProcessing(false);
                }
            },
            async getSubmittedDeclarationsLite(options?: any): Promise<GetSubmittedDeclarationsLiteResponse> {
                try {
                    if (!service || !authService?.authData?.userName) {
                        this.clearSubmittedDeclarations();
                        return new GetSubmittedDeclarationsLiteResponse();
                    }
                    const id: number = options?.declarationsFilter?.id || null;
                    const m1Afm: string = options?.declarationsFilter?.m1Afm || null;
                    const m1IdNumber: number = options?.declarationsFilter?.m1IdNumber || null;
                    const m1SurNameA: string = options?.declarationsFilter?.m1SurNameA || null;
                    const m1SurNameB: string = options?.declarationsFilter?.m1SurNameA || null;
                    const m1Name: string = options?.declarationsFilter?.m1Name || null;
                    const m1RepresentativeAfm: string = options?.declarationsFilter?.m1RepresentativeAfm || null;
                    const pagingParams: PagingParams = options?.pagingParams || null;
                    const request: GetSubmittedDeclarationsLiteRequest = new GetSubmittedDeclarationsLiteRequest({
                        id,
                        m1Afm,
                        m1IdNumber,
                        m1SurNameA,
                        m1SurNameB,
                        m1Name,
                        m1RepresentativeAfm,
                        pagingParams
                    });
                    if (this._getSubmittedDeclarationsLiteRequest) {
                        if (JSON.stringify(request) === JSON.stringify(this._getSubmittedDeclarationsLiteRequest)) {
                            return new GetSubmittedDeclarationsLiteResponse({
                                success: true,
                                result: this.submittedDeclarationsLite
                            });
                        }
                    }
                    this.setProcessing(true);
                    const response: GetSubmittedDeclarationsLiteResponse =
                        await service.getSubmittedDeclarationsLite(request);
                    if (response.success && response.result?.declarations?.length) {
                        this.setSubmittedDeclarations(response.result);
                        this._getSubmittedDeclarationsLiteRequest = request;
                    } else {
                        this.clearSubmittedDeclarations();
                        this._getSubmittedDeclarationsLiteRequest = null;
                    }
                    this.setProcessing(false);
                    return response;
                } catch (ex: any) {
                    console.error(ex);
                    this.setProcessing(false);
                    throw ex;
                } finally {
                    this.setProcessing(false);
                }
            },
            async getSubmittedDeclarations(options?: any): Promise<GetSubmittedDeclarationsResponse> {
                try {
                    if (!service || !authService?.authData?.userName) {
                        return new GetSubmittedDeclarationsResponse();
                    }
                    const id: number = options?.id || null;
                    const m1Afm: string = options?.m1Afm || null;
                    const m1IdNumber: number = options?.m1IdNumber || null;
                    const m1SurNameA: string = options?.m1SurNameA || null;
                    const m1SurNameB: string = options?.m1SurNameA || null;
                    const m1Name: string = options?.m1Name || null;
                    const m1RepresentativeAfm: string = options?.m1RepresentativeAfm || null;
                    const request: GetSubmittedDeclarationsRequest = new GetSubmittedDeclarationsRequest({
                        id,
                        m1Afm,
                        m1IdNumber,
                        m1SurNameA,
                        m1SurNameB,
                        m1Name,
                        m1RepresentativeAfm
                    });
                    this.setProcessing(true);
                    const response: GetSubmittedDeclarationsResponse =
                        await service.getSubmittedDeclarations(request);
                    this.setProcessing(false);
                    return response;
                } catch (ex: any) {
                    console.error(ex);
                    this.setProcessing(false);
                    throw ex;
                } finally {
                    this.setProcessing(false);
                }
            },

            async exportSubmittedDeclarationsToCsv(options?: any): Promise<ExportSubmittedDeclarationsToCsvResponse> {
                try {
                    if (!service || !authService?.authData?.userName) {
                        this.clearSubmittedDeclarations();
                        return new ExportSubmittedDeclarationsToCsvResponse();
                    }
                    const applicantAfm: string = options?.applicantAfm || null;
                    const declarationYear: number = options?.declarationYear || null;
                    const declarationId: number | null = options?.declarationId || null;
                    const searchTerm: string | null = options?.searchTerm || null;
                    if (!(applicantAfm && declarationYear) && !declarationId) {
                        return new ExportSubmittedDeclarationsToCsvResponse();
                    }
                    this.setProcessing(true);
                    const request: ExportSubmittedDeclarationsToCsvRequest =
                        new ExportSubmittedDeclarationsToCsvRequest({
                            applicantAfm,
                            declarationYear,
                            declarationId,
                            searchTerm
                        });
                    const csvData: any = await service.exportSubmittedDeclarationsToCsv(request);
                    const response: ExportSubmittedDeclarationsToCsvResponse =
                        new ExportSubmittedDeclarationsToCsvResponse({
                            success: true,
                            result: csvData
                        });
                    this.setProcessing(false);
                    return response;
                } catch (ex: any) {
                    console.error(ex);
                    this.setProcessing(false);
                    throw ex;
                } finally {
                    this.setProcessing(false);
                }
            },
            async exportSubmittedDeclarationsToExcel(
                options?: any
            ): Promise<ExportSubmittedDeclarationsToExcelResponse> {
                try {
                    if (!service || !authService?.authData?.userName) {
                        // this.clearSubmittedDeclarations();
                        return new ExportSubmittedDeclarationsToExcelResponse();
                    }

                    const id: number = options?.id || null;
                    const m1Afm: string = options?.m1Afm || null;
                    const m1IdNumber: string = options?.m1IdNumber || null;
                    const m1SurNameA: string = options?.m1SurNameA || null;
                    const m1SurNameB: string = options?.m1SurNameA || null;
                    const m1Name: string = options?.m1Name || null;
                    const m1RepresentativeAfm: string = options?.m1RepresentativeAfm || null;
                    const pagingParams: PagingParams = options?.pagingParams || null;
                    this.setProcessing(true);
                    const request: ExportSubmittedDeclarationsToExcelRequest =
                        new ExportSubmittedDeclarationsToExcelRequest({
                            id,
                            m1Afm,
                            m1IdNumber,
                            m1SurNameA,
                            m1SurNameB,
                            m1Name,
                            m1RepresentativeAfm,
                            pagingParams
                        });
                    const csvData: any = await service.exportSubmittedDeclarationsToExcel(request);
                    const response: ExportSubmittedDeclarationsToExcelResponse =
                        new ExportSubmittedDeclarationsToExcelResponse({
                            success: true,
                            result: csvData
                        });
                    this.setProcessing(false);
                    return response;
                } catch (ex: any) {
                    console.error(ex);
                    this.setProcessing(false);
                    throw ex;
                } finally {
                    this.setProcessing(false);
                }
            },
            async getDeclarationAttachment(request: GetDeclarationAttachmentRequest): Promise<any> {
                try {
                    if (!service) return null;
                    this.setProcessing(true);
                    const response: any = await service.getDeclarationAttachment(request);
                    this.setProcessing(false);
                    return response;
                } catch (ex: any) {
                    console.error(ex);
                    this.setProcessing(false);
                    throw ex;
                } finally {
                    this.setProcessing(false);
                }
            },
            async getDeclarationReport(request: GetDeclarationReportRequest): Promise<any> {
                try {
                    if (!service) return null;
                    this.setProcessing(true);
                    const response: any = await service.getDeclarationReport(request);
                    this.setProcessing(false);
                    return response;
                } catch (ex: any) {
                    console.error(ex);
                    this.setProcessing(false);
                    throw ex;
                } finally {
                    this.setProcessing(false);
                }
            }
        }
    });
    const store = useStore();
    return store as IM1Store;
}
