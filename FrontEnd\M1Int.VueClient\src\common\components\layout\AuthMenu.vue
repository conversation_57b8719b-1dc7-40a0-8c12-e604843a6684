<template>
    <div v-if="autnMenuEnabled" class="main-app-auth-menu-container">
        <div v-if="isUserAuthenticated" class="row justify-start items-center" style="cursor: pointer">
            <q-btn
                flat
                round
                icon="account_circle"
                :label="authUserNameTitle"
                class="main-locale-menu-button"
                :aria-label="authUserNameTitle"
            >
                <q-menu auto-close>
                    <q-list>
                        <q-item role="" :aria-label="authUserName">
                            <div class="row justify-start items-center">
                                <q-icon name="person" class="q-px-xs" />
                                <div class="q-px-xs">{{ authUserName }}</div>
                            </div>
                        </q-item>
                        <q-item v-if="logoutEnabled()" clickable role="" :aria-label="$t('logout')" @click="logout">
                            <div class="row justify-start items-center">
                                <q-icon name="logout" class="q-px-xs" />
                                <div class="q-px-xs">{{ $t('logout') }}</div>
                            </div>
                        </q-item>
                    </q-list>
                </q-menu>
            </q-btn>
        </div>
        <div
            v-else-if="loginEnabled()"
            class="row justify-start items-center"
            style="cursor: pointer"
            :aria-label="$t('login')"
            @click="login"
        >
            <q-icon size="sm" name="login" class="q-px-xs" />
            <div class="gt-sm" style="font-size: 14px">{{ $t('login') }}</div>
        </div>
    </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import type { PropType } from 'vue';
import { getCommonStore, ICommonStore } from '@/common/stores/Store';
import { goToUrl, isNetworkOnLine } from '@/common/utilities';
import config from '@/config';

export default defineComponent({
    name: 'auth-menu',
    props: {
        store: {
            type: Object as PropType<ICommonStore>,
            default: () => getCommonStore()
        }
    },
    data() {
        const dataObj: {
            autnMenuEnabled: boolean;
        } = {
            autnMenuEnabled: true
        };
        return dataObj;
    },
    computed: {
        authUserName(): string | undefined {
            return this.store?.authData?.userName || undefined;
        },
        authUserNameTitle(): string | undefined {
            if (!this.store?.authData?.userName) return undefined;
            if (this.$q.screen.width >= 700) return this.store?.authData?.userName;
            else return this.store?.authData?.userName[0];
        },
        isUserAuthenticated(): boolean {
            return !!this.authUserName;
        }
    },
    methods: {
        loginEnabled(): boolean {
            if (config.auth?.externalProvider?.enabled) {
                if (!config.auth?.externalProvider?.loginEnabled) return false;
            }
            if (!isNetworkOnLine()) return false;
            if (this.isUserAuthenticated) return false;
            return true;
        },
        logoutEnabled(): boolean {
            if (!isNetworkOnLine()) return false;
            if (!this.isUserAuthenticated) return false;
            return true;
        },
        login() {
            if (
                config.auth?.externalProvider?.enabled === true &&
                config.auth?.externalProvider?.simulateExternalProviderUserIdHeader !== true
            ) {
                if (config.auth?.externalProvider?.loginUrl) {
                    goToUrl({ url: config.auth?.externalProvider?.loginUrl });
                    return;
                }
            }
            this.$router?.push({ path: '/login' });
        },
        logout() {
            this.$router?.push({ path: '/logout' });
        },
        refresh() {
            this.autnMenuEnabled = false;
            this.$nextTick(() => {
                this.autnMenuEnabled = true;
            });
        },
        networkOnLine() {
            this.refresh();
        },
        networkOffLine() {
            this.refresh();
        }
    },
    created() {
        window.addEventListener('online', this.networkOnLine);
        window.addEventListener('offline', this.networkOffLine);
    },
    beforeUnmount() {
        window.removeEventListener('online', this.networkOnLine);
        window.removeEventListener('offline', this.networkOffLine);
    }
});
</script>

<style>
.main-locale-menu-button .q-icon.on-left {
    margin-right: 4px !important;
}
</style>
