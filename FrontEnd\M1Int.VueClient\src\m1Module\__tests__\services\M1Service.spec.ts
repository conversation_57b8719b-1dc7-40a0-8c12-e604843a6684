import { IM1Service, M1Service } from '@/m1Module/services/M1Service';

describe('M1Service', () => {
    let service: IM1Service;

    beforeEach(() => {
        service = new M1Service();
    });

    it('should be valid', () => {
        expect(service).toBeTruthy();
    });

    it('should have getDeclarationsOptions method', () => {
        expect(service.getDeclarationsOptions).toBeTruthy();
        expect(typeof service.getDeclarationsOptions).toEqual('function');
    });

    it('should have getSubmittedDeclarationsLite method', () => {
        expect(service.getSubmittedDeclarationsLite).toBeTruthy();
        expect(typeof service.getSubmittedDeclarationsLite).toEqual('function');
    });

    it('should have exportSubmittedDeclarationsToCsv method', () => {
        expect(service.exportSubmittedDeclarationsToCsv).toBeTruthy();
        expect(typeof service.exportSubmittedDeclarationsToCsv).toEqual('function');
    });

    it('should have exportSubmittedDeclarationsToExcel method', () => {
        expect(service.exportSubmittedDeclarationsToExcel).toBeTruthy();
        expect(typeof service.exportSubmittedDeclarationsToExcel).toEqual('function');
    });
});
