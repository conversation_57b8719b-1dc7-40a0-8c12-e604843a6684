
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for common/utilities</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> common/utilities</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">6.11% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>20/327</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">3.41% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>12/351</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">14.75% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>9/61</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">6.4% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>18/281</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input oninput="onInput()" type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file medium" data-value="IAlert.ts"><a href="IAlert.ts.html">IAlert.ts</a></td>
	<td data-value="70" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 70%"></div><div class="cover-empty" style="width: 30%"></div></div>
	</td>
	<td data-value="70" class="pct medium">70%</td>
	<td data-value="10" class="abs medium">7/10</td>
	<td data-value="66.66" class="pct medium">66.66%</td>
	<td data-value="3" class="abs medium">2/3</td>
	<td data-value="25" class="pct low">25%</td>
	<td data-value="4" class="abs low">1/4</td>
	<td data-value="66.66" class="pct medium">66.66%</td>
	<td data-value="9" class="abs medium">6/9</td>
	</tr>

<tr>
	<td class="file low" data-value="SweetAlert.ts"><a href="SweetAlert.ts.html">SweetAlert.ts</a></td>
	<td data-value="0.42" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0.42" class="pct low">0.42%</td>
	<td data-value="233" class="abs low">1/233</td>
	<td data-value="0.37" class="pct low">0.37%</td>
	<td data-value="264" class="abs low">1/264</td>
	<td data-value="2.56" class="pct low">2.56%</td>
	<td data-value="39" class="abs low">1/39</td>
	<td data-value="0.47" class="pct low">0.47%</td>
	<td data-value="209" class="abs low">1/209</td>
	</tr>

<tr>
	<td class="file low" data-value="index.ts"><a href="index.ts.html">index.ts</a></td>
	<td data-value="14.28" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 14%"></div><div class="cover-empty" style="width: 86%"></div></div>
	</td>
	<td data-value="14.28" class="pct low">14.28%</td>
	<td data-value="84" class="abs low">12/84</td>
	<td data-value="10.71" class="pct low">10.71%</td>
	<td data-value="84" class="abs low">9/84</td>
	<td data-value="38.88" class="pct low">38.88%</td>
	<td data-value="18" class="abs low">7/18</td>
	<td data-value="17.46" class="pct low">17.46%</td>
	<td data-value="63" class="abs low">11/63</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2022-12-03T11:52:51.044Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    