import { shallowMount, VueWrapper } from '@vue/test-utils';
import mockAlert from '@/mocks/MockAlert';
import FieldEditRadio from '@/common/components/form/FieldEditRadio.vue';
import { Component } from 'vue';

describe('FieldEditRadio', () => {
    let wrapper: VueWrapper;
    let component: Component;

    const options: Array<any> = [
        { id: '001', name: 'Name 001' },
        { id: '002', name: 'Name 002' },
        { id: '003', name: 'Name 003' },
        { id: '004', name: 'Name 004' },
        { id: '005', name: 'Name 005' }
    ];

    const model: any = {
        id: '001',
        selectionSingle: null,
        selectionMulti: []
    };

    beforeEach(() => {
        wrapper = shallowMount(FieldEditRadio, {
            global: {
                mocks: {
                    $t: (key: string) => {
                        return key;
                    },
                    $alert: mockAlert
                }
            },
            props: {
                modelObject: model,
                fieldId: 'selectionSingle',
                fieldTitle: 'Single selection',
                options: options,
                optionLabel: 'name',
                optionValue: 'id'
            }
        });
        component = wrapper.vm as Component;
    });

    afterEach(() => {});

    it('should mount', () => {
        expect(wrapper).toBeTruthy();
        expect(wrapper.vm).toBeTruthy();
        expect(wrapper.vm.$el).toBeTruthy();
    });

    it('component should be valid', () => {
        expect(component).toBeTruthy();
    });

    it('should have valid $t property', () => {
        expect(wrapper.vm.$t).toBeTruthy();
        expect(typeof wrapper.vm.$t).toEqual('function');
    });

    it('should have valid $alert property', () => {
        expect((wrapper.vm as any).$alert).toBeTruthy();
    });

    describe('model field single selection value', () => {
        beforeEach(async () => {
            await wrapper.setProps({
                modelObject: model,
                fieldId: 'selectionSingle',
                fieldTitle: 'Single selection',
                options: options,
                optionLabel: 'name',
                optionValue: 'id',
                multiple: false
            });
            model.selectionSingle = null;
        });

        it('should have valid field selectionSingle value', () => {
            expect((component as any).getValue()).toBeNull();
        });

        it('should have valid field selectionSingle label', async () => {
            expect(wrapper.attributes('label')).toContain((component as any).fieldTitle);
        });

        it('should update field selectionSingle value (take 1)', () => {
            expect((component as any).getValue()).toBeNull();
            model.selectionSingle = '001';
            expect((component as any).getValue()).toEqual('001');
        });

        it('should update field selectionSingle value (take 2)', () => {
            expect((component as any).getValue()).toBeNull();
            model.selectionSingle = '002';
            expect((component as any).getValue()).toEqual('002');
        });
    });

    describe('model field multiple selection value', () => {
        beforeEach(async () => {
            await wrapper.setProps({
                modelObject: model,
                fieldId: 'selectionMulti',
                fieldTitle: 'Multiple selection',
                options: options,
                optionLabel: 'name',
                optionValue: 'id',
                multiple: true
            });
            model.selectionMulti = [];
        });

        it('should have valid field selectionMulti value', () => {
            expect((component as any).getValue()).toEqual([]);
        });

        it('should have valid field selectionMulti label', async () => {
            expect(wrapper.attributes('label')).toContain((component as any).fieldTitle);
        });

        it('should update field selectionMulti value (take 1)', () => {
            expect((component as any).getValue()).toEqual([]);
            model.selectionMulti.push('001');
            expect((component as any).getValue()).toEqual(['001']);
        });

        it('should update field selectionMulti value (take 2)', () => {
            expect((component as any).getValue()).toEqual([]);
            model.selectionMulti.push('001');
            model.selectionMulti.push('002');
            expect((component as any).getValue()).toEqual(['001', '002']);
        });

        it('should update field selectionMulti value (take 3)', () => {
            expect((component as any).getValue()).toEqual([]);
            model.selectionMulti.push('001');
            model.selectionMulti.push('002');
            model.selectionMulti.splice(0, 1);
            expect((component as any).getValue()).toEqual(['002']);
        });

        it('should update field selectionMulti value (take 4)', () => {
            expect((component as any).getValue()).toEqual([]);
            model.selectionMulti.push('001');
            model.selectionMulti.push('002');
            model.selectionMulti.splice(1, 1);
            expect((component as any).getValue()).toEqual(['001']);
        });
    });
});
