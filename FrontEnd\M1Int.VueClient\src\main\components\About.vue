<template>
    <div class="q-pa-sm q-pt-lg column justify-center items-center about-component">
        <div class="text-h5 q-pa-sm">{{ $t('applicationDescription') }}</div>
        <div v-if="applicationVersion" class="text-h5 q-pa-sm">{{ applicationVersionDescription }}</div>
        <div class="text-h5 q-pa-sm">
            &copy;&nbsp;
            <span>{{ $t('aadeTitle') }}</span
            >&nbsp;&nbsp;<span>{{ yearStr }}</span>
        </div>
    </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import type { PropType } from 'vue';
import { ICommonStore, getCommonStore } from '@/common/stores/Store';
import dayjs from '@/plugins/dayjs';

export default defineComponent({
    name: 'about-component',
    props: {
        store: {
            type: Object as PropType<ICommonStore>,
            default: () => getCommonStore()
        }
    },
    computed: {
        applicationVersion(): string {
            return this.store?.applicationVersion || '';
        },
        applicationVersionDescription(): string {
            const applicationVersion: string = this.applicationVersion;
            if (!applicationVersion) return '';
            return `${this.$t('version')} v${applicationVersion}`;
        },
        currentDateTime(): Date {
            return this.store?.serverCurrentDateTime || dayjs().toDate();
        },
        yearStr(): string {
            return dayjs(this.currentDateTime).format('YYYY');
        }
    },
    async created() {
        try {
            await this.store?.getServerCurrentDateTime();
        } catch (ex: any) {
            console.error(ex);
        }
    }
});
</script>
