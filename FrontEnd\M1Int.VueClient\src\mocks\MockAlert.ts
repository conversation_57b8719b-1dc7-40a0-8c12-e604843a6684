import { AlertResult, AlertResultType, IAlert } from '@/common/utilities/IAlert';

export class <PERSON>ck<PERSON><PERSON>t implements IAlert {
    close(): void {}

    messageBox(_options?: any): Promise<AlertResult> {
        return Promise.resolve(new AlertResult());
    }

    messageBoxInfo(_options?: any): Promise<AlertResult> {
        return Promise.resolve(new AlertResult(AlertResultType.Ok));
    }

    messageBoxQuestion(_options?: any): Promise<AlertResult> {
        return Promise.resolve(new AlertResult(AlertResultType.Ok));
    }

    messageBoxSuccess(_options?: any): Promise<AlertResult> {
        return Promise.resolve(new AlertResult(AlertResultType.Ok));
    }

    messageBoxWarning(_options?: any): Promise<AlertResult> {
        return Promise.resolve(new AlertResult(AlertResultType.Ok));
    }

    messageBoxError(_options?: any): Promise<AlertResult> {
        return Promise.resolve(new AlertResult(AlertResultType.Ok));
    }

    notify(_options?: any): Promise<AlertResult> {
        return Promise.resolve(new AlertResult());
    }

    notifyInfo(_options?: any): Promise<AlertResult> {
        return Promise.resolve(new AlertResult());
    }

    notifyQuestion(_options?: any): Promise<AlertResult> {
        return Promise.resolve(new AlertResult());
    }

    notifySuccess(_options?: any): Promise<AlertResult> {
        return Promise.resolve(new AlertResult());
    }

    notifyWarning(_options?: any): Promise<AlertResult> {
        return Promise.resolve(new AlertResult());
    }

    notifyError(_options?: any): Promise<AlertResult> {
        return Promise.resolve(new AlertResult());
    }

    confirm(_options?: any): Promise<AlertResult> {
        return Promise.resolve(new AlertResult());
    }

    confirmYesNo(_options?: any): Promise<AlertResult> {
        return Promise.resolve(new AlertResult());
    }

    prompt(_options?: any): Promise<AlertResult> {
        return Promise.resolve(new AlertResult());
    }

    selectFiles(_options?: any): Promise<AlertResult> {
        return Promise.resolve(new AlertResult());
    }

    progress(_options?: any): Promise<AlertResult> {
        return Promise.resolve(new AlertResult());
    }
}

const mockAlert: IAlert = new MockAlert();
export default mockAlert;
