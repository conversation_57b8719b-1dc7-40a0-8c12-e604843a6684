import { shallowMount, VueWrapper } from '@vue/test-utils';
import DeclarationTaxEntityData from '@/m1Module/components/m1/DeclarationTaxEntityData.vue';
import { commonComponentMocks, commonComponentPlugins } from '@/mocks/commonComponentMocks';
import { Component } from 'vue';
import { MockM1Service } from '@/mocks/MockM1Service';
import { IM1Store, getM1Store } from '@/m1Module/stores/M1Store';
import { setActivePinia, createPinia } from 'pinia';

describe('DeclarationTaxEntityData', () => {
    let wrapper: VueWrapper;
    let m1Store: IM1Store | null = null;

    beforeEach(() => {
        setActivePinia(createPinia());
        m1Store = getM1Store(new MockM1Service());
        wrapper = shallowMount(DeclarationTaxEntityData, {
            global: {
                mocks: commonComponentMocks,
                plugins: commonComponentPlugins
            },
            props: {
                store: m1Store
            }
        });
    });

    afterEach(() => {});

    it('should mount', () => {
        expect(wrapper).toBeTruthy();
        expect(wrapper.vm).toBeTruthy();
        expect(wrapper.vm.$el).toBeTruthy();
        expect(wrapper.vm as Component).toBeTruthy();
    });

    it('element should be valid', () => {
        expect(wrapper.vm.$el.classList).toBeTruthy();
        expect(Array.from(wrapper.vm.$el.classList)).toContain('declaration-tax-entity-data-view');
    });

    it('should have valid $t property', () => {
        expect(wrapper.vm.$t).toBeTruthy();
        expect(typeof wrapper.vm.$t).toEqual('function');
    });

    it('$t should return valid result', () => {
        expect(wrapper.vm.$t('DeclarationTaxEntityData')).toEqual('DeclarationTaxEntityData');
    });

    it('should have valid $alert property', () => {
        expect((wrapper.vm as any).$alert).toBeTruthy();
    });
});
