import { shallowMount, VueWrapper } from '@vue/test-utils';
import mockAlert from '@/mocks/MockAlert';
import FieldEditText from '@/common/components/form/FieldEditText.vue';
import { Component } from 'vue';

describe('FieldEditText', () => {
    let wrapper: VueWrapper;
    let component: Component;

    const model: any = {
        id: '001',
        title: 'Title 001',
        description: 'Description 001'
    };

    beforeEach(() => {
        wrapper = shallowMount(FieldEditText, {
            global: {
                mocks: {
                    $t: (key: string) => {
                        return key;
                    },
                    $alert: mockAlert
                }
            },
            props: {
                modelObject: model,
                fieldId: 'id',
                fieldTitle: 'Id'
            }
        });
        component = wrapper.vm as Component;
    });

    afterEach(() => {});

    it('should mount', () => {
        expect(wrapper).toBeTruthy();
        expect(wrapper.vm).toBeTruthy();
        expect(wrapper.vm.$el).toBeTruthy();
    });

    it('component should be valid', () => {
        expect(component).toBeTruthy();
    });

    it('should have valid $t property', () => {
        expect(wrapper.vm.$t).toBeTruthy();
        expect(typeof wrapper.vm.$t).toEqual('function');
    });

    it('should have valid $alert property', () => {
        expect((wrapper.vm as any).$alert).toBeTruthy();
    });

    it('should have valid field id value', async () => {
        await wrapper.setProps({
            modelObject: model,
            fieldId: 'id',
            fieldTitle: 'Id'
        });
        expect((component as any).getValue()).toEqual(model.id);
    });

    it('should have valid field id label', async () => {
        await wrapper.setProps({
            modelObject: model,
            fieldId: 'id',
            fieldTitle: 'Id'
        });
        expect(wrapper.attributes('label')).toContain((component as any).fieldTitle);
    });

    it('should have valid field title value', async () => {
        await wrapper.setProps({
            modelObject: model,
            fieldId: 'title',
            fieldTitle: 'Title'
        });
        expect((component as any).getValue()).toEqual(model.title);
    });

    it('should have valid field title label', async () => {
        await wrapper.setProps({
            modelObject: model,
            fieldId: 'title',
            fieldTitle: 'Title'
        });
        expect(wrapper.attributes('label')).toContain((component as any).fieldTitle);
    });

    it('should have valid field description value', async () => {
        await wrapper.setProps({
            modelObject: model,
            fieldId: 'description',
            fieldTitle: 'Description'
        });
        expect((component as any).getValue()).toEqual(model.description);
    });

    it('should have valid field description label', async () => {
        await wrapper.setProps({
            modelObject: model,
            fieldId: 'description',
            fieldTitle: 'Description'
        });
        expect(wrapper.attributes('label')).toContain((component as any).fieldTitle);
    });

    it('should update field title value', async () => {
        await wrapper.setProps({
            modelObject: model,
            fieldId: 'title',
            fieldTitle: 'Title'
        });
        const newTitle: string = 'Title 111';
        expect((component as any).getValue()).toEqual(model.title);
        model.title = newTitle;
        expect((component as any).getValue()).toEqual(newTitle);
    });

    it('should update field description value', async () => {
        await wrapper.setProps({
            modelObject: model,
            fieldId: 'description',
            fieldTitle: 'Description'
        });
        const newDescription: string = 'Description 001';
        expect((component as any).getValue()).toEqual(model.description);
        model.description = newDescription;
        expect((component as any).getValue()).toEqual(newDescription);
    });
});
