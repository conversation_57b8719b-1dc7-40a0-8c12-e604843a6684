<template>
    <div v-if="timerVisible" class="q-mx-none q-px-none timer-countdown">
        <div class="row justify-center items-center timer-countdown-content">
            <q-knob
                :min="0"
                :max="initialSecondsLeft"
                v-model="timerTotalSecondsLeft"
                reverse
                show-value
                :size="timerKnobSize"
                :thickness="timerKnobThickness"
                :color="timerStatusColor"
                track-color="grey-4"
            >
                {{ timerDisplayText }}
            </q-knob>
        </div>
    </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { useTimer } from 'vue-timer-hook';

export default defineComponent({
    name: 'timer-countdown-common',
    props: {
        expiresAt: {
            type: Date,
            default: null
        },
        expirationSeconds: {
            type: Number,
            default: 0
        },
        showNotifications: {
            type: Boolean,
            default: true
        }
    },
    emits: ['timerStarted', 'timerUpdated', 'timerExpired'],
    data() {
        const dataObj: {
            timer: any;
            initialSecondsLeft: number;
            timerSecondsLevel0: number;
            timerSecondsLevel1: number;
            timerSecondsLevel2: number;
            timerSecondsLevel0WarningDisplayed: boolean;
            timerSecondsLevel1WarningDisplayed: boolean;
        } = {
            timer: null,
            initialSecondsLeft: 0,
            timerSecondsLevel0: 0,
            timerSecondsLevel1: 0,
            timerSecondsLevel2: 0,
            timerSecondsLevel0WarningDisplayed: false,
            timerSecondsLevel1WarningDisplayed: false
        };
        return dataObj;
    },
    computed: {
        expirationTime(): number {
            let expiration: number = 0;
            if (this.expiresAt) {
                expiration += this.expiresAt.getTime();
            }
            if (this.expirationSeconds) {
                const time = new Date();
                time.setSeconds(time.getSeconds() + this.expirationSeconds);
                expiration += time.getTime();
            }
            return expiration;
        },
        timerVisible(): boolean {
            if (!this.timer) return false;
            return this.$q?.screen?.width >= 360;
        },
        timerHours(): string {
            return `${this.timer.hours < 10 ? '0' : ''}${this.timer.hours.toString()}`;
        },
        timerMinutes(): string {
            return `${this.timer.minutes < 10 ? '0' : ''}${this.timer.minutes.toString()}`;
        },
        timerSeconds(): string {
            return `${this.timer.seconds < 10 ? '0' : ''}${this.timer.seconds.toString()}`;
        },
        timerTotalSecondsLeft(): number {
            if (!this.timer) return 0;
            return this.timer.seconds + this.timer.minutes * 60 + this.timer.hours * 60 * 60;
        },
        timerDisplayText(): string {
            let res: string = `${this.timerMinutes}:${this.timerSeconds}`;
            if (this.timer.hours) res = `${this.timerHours}:${res}`;
            return res;
        },
        timerStatusColor(): string {
            if (!this.timer) return '';
            if (this.timerTotalSecondsLeft < this.timerSecondsLevel0) return 'red-6';
            else if (this.timerTotalSecondsLeft < this.timerSecondsLevel1) return 'amber-7';
            else if (this.timerTotalSecondsLeft < this.timerSecondsLevel2) return 'light-green-7';
            else return 'green-7';
        },
        timerKnobSize(): string {
            if (this.$q?.screen?.width <= 640) return '30px';
            else return '45px';
        },
        timerKnobThickness(): number {
            if (this.$q?.screen?.width <= 640) return 0.1;
            else if (this.$q?.screen?.width <= 800) return 0.12;
            else return 0.15;
        }
    },
    watch: {
        'timer.hours'() {
            this.timerUpdated();
        },
        'timer.minutes'() {
            this.timerUpdated();
        },
        'timer.seconds'() {
            this.timerUpdated();
        }
    },
    methods: {
        resumeTimer() {
            this.timer = useTimer(this.expirationTime, false);
            this.initialSecondsLeft = this.timerTotalSecondsLeft;
            this.timerSecondsLevel2 = this.initialSecondsLeft / 2;
            this.timerSecondsLevel1 = Math.min(this.timerSecondsLevel2 / 3, 5 * 60);
            this.timerSecondsLevel0 = Math.min(this.timerSecondsLevel1 / 2, 2 * 60);
            this.timer.start();
            this.$emit('timerStarted', this.timer);
        },
        async timerUpdated() {
            if (!this.timer) return;
            this.$emit('timerUpdated', this.timer);
            if (this.timer.isExpired) {
                await this.timerExpired();
            } else {
                this.handleTimerNotifications();
            }
        },
        async timerExpired() {
            this.$emit('timerExpired', this.timer);
            if (this.showNotifications) {
                this.$alert?.notifyWarning({
                    title: this.$t('timerCountdown'),
                    message: this.$t('timerHasExpired', [this.timerDisplayText]),
                    duration: 5000
                });
            }
        },
        handleTimerNotifications() {
            if (!this.timer || !this.showNotifications) return;
            if (this.timerTotalSecondsLeft < this.timerSecondsLevel0) {
                if (!this.timerSecondsLevel0WarningDisplayed) {
                    this.timerSecondsLevel0WarningDisplayed = true;
                    this.$alert?.notifyWarning({
                        title: this.$t('timerCountdown'),
                        message: this.$t('timerTimeLeftMessage', [this.timerDisplayText]),
                        duration: 5000
                    });
                }
            } else if (this.timerTotalSecondsLeft < this.timerSecondsLevel1) {
                if (!this.timerSecondsLevel1WarningDisplayed) {
                    this.timerSecondsLevel1WarningDisplayed = true;
                    this.$alert?.notifyInfo({
                        title: this.$t('timerCountdown'),
                        message: this.$t('timerTimeLeftMessage', [this.timerDisplayText]),
                        duration: 5000
                    });
                }
            }
        }
    },
    mounted() {
        this.resumeTimer();
    }
});
</script>
