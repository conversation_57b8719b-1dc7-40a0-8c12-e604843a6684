import { ExportSubmittedDeclarationsToCsvRequest } from '@/m1Module/models/m1/Services/ExportSubmittedDeclarationsToCsv/ExportSubmittedDeclarationsToCsvRequest';

describe('ExportSubmittedDeclarationsToCsvRequest', () => {
    let request: ExportSubmittedDeclarationsToCsvRequest;

    beforeEach(() => {
        request = new ExportSubmittedDeclarationsToCsvRequest();
    });

    it('should be valid', () => {
        expect(request).toBeTruthy();
    });
});
