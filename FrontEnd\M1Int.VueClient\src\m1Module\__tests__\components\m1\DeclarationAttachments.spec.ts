import { shallowMount, VueWrapper } from '@vue/test-utils';
import DeclarationAttachments from '@/m1Module/components/m1/DeclarationAttachments.vue';
import { commonComponentMocks, commonComponentPlugins } from '@/mocks/commonComponentMocks';
import { Component } from 'vue';

describe('DeclarationAttachments', () => {
    let wrapper: VueWrapper;

    beforeEach(() => {
        wrapper = shallowMount(DeclarationAttachments, {
            global: {
                mocks: commonComponentMocks,
                plugins: commonComponentPlugins
            }
        });
    });

    afterEach(() => {});

    it('should mount', () => {
        expect(wrapper).toBeTruthy();
        expect(wrapper.vm).toBeTruthy();
        expect(wrapper.vm.$el).toBeTruthy();
        expect(wrapper.vm as Component).toBeTruthy();
    });

    it('element should be valid', () => {
        expect(wrapper.vm.$el.classList).toBeTruthy();
        expect(Array.from(wrapper.vm.$el.classList)).toContain('declaration-attachments');
    });

    it('should have valid $t property', () => {
        expect(wrapper.vm.$t).toBeTruthy();
        expect(typeof wrapper.vm.$t).toEqual('function');
    });

    it('$t should return valid result', () => {
        expect(wrapper.vm.$t('DeclarationAttachments')).toEqual('DeclarationAttachments');
    });

    it('should have valid $alert property', () => {
        expect((wrapper.vm as any).$alert).toBeTruthy();
    });
});
