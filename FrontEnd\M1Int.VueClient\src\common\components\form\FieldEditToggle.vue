<template>
    <q-toggle
        :id="elementId"
        :for="elementId"
        :name="elementName"
        v-model="modelObject[fieldId]"
        :error-message="validationMessage"
        :error="isDirty && isInvalid"
        :noErrorIcon="true"
        :class="classResolved"
        :label="label"
        :placeholder="placeholder"
        :hint="hint"
        :hideHint="hideHint"
        :outlined="outlined"
        :filled="filled"
        :borderless="borderless"
        :rounded="rounded"
        :dense="dense"
        :disable="disabled"
        :readonly="readonly"
        :icon="icon"
        :color="color"
        :keepColor="!!color"
        :leftLabel="leftLabel"
        :toggleOrder="toggleOrder"
        @update:model-value="valueChanged"
    />
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import FieldCommon from './FieldCommon';

export default defineComponent({
    name: 'field-edit-toggle',
    mixins: [FieldCommon],
    props: {
        icon: {
            type: String,
            default: null
        },
        color: {
            type: String,
            default: null
        },
        leftLabel: {
            type: Boolean,
            default: false
        },
        toggleOrder: {
            type: String as () => 'tf' | 'ft',
            default: 'tf'
        }
    }
});
</script>
