import dayjs from '@/plugins/dayjs';

export class DeclarationEmail {
    public id?: number;
    public m1Id?: number;
    public emailTo: string | null = null;
    public emailFrom: string | null = null;
    public emailServer: string | null = null;
    public emailSentAt: Date | null = null;

    constructor(options?: any) {
        options = options || {};
        this.id = options.id || undefined;
        this.m1Id = options.m1Id || undefined;
        this.emailTo = options.emailTo || null;
        this.emailFrom = options.emailFrom || null;
        this.emailServer = options.emailServer || null;
        if (options.emailSentAt) this.emailSentAt = dayjs(options.emailSentAt).toDate();
    }

    public get emailSentAtText(): string {
        // if (!this.emailSentAt) return '';
        // return dayjs(this.emailSentAt).format('L');
        const dt: Date | null = this.emailSentAt;
        if (!dt) return '';
        const dtjs = dayjs(dt);
        return `${dtjs.format('L')} ${dtjs.format('HH:mm:ss')}`;
    }
}
