<template>
    <div class="auth-handler"></div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import type { PropType } from 'vue';
import authService, { authServiceDataLoaded } from '../services/AuthService';
import { getCommonStore, ICommonStore } from '@/common/stores/Store';
import { goToUrl } from '@/common/utilities';
import {
    LOGIN,
    LOGIN_CALLBACK,
    LOGOUT,
    LOGOUT_CALLBACK,
    SIGNIN,
    SIGNIN_CALLBACK,
    SIGNOUT,
    SIGNOUT_CALLBACK
} from '../authDefs';

export default defineComponent({
    name: 'auth-handler',
    props: {
        store: {
            type: Object as PropType<ICommonStore>,
            default: () => getCommonStore()
        },
        action: {
            type: String,
            default: null
        },
        returnToUrl: {
            type: String,
            default: '/'
        }
    },
    methods: {
        login() {
            this.$router?.push({ path: `/${LOGIN}` });
        },
        loginCallback() {
            this.store?.setAuthData(authService?.authData);
            // this.$messageBus.emit(LOGIN_CALLBACK);
            let url: string = this.returnToUrl;
            if (!url) url = '/#/';
            if (url === '/' || url === '/#' || url === '/#/') {
                this.$nextTick(() =>
                    this.$router?.push({
                        path: url,
                        query: {
                            notifyType: 'success',
                            notifyMessage: this.$t('userAuthenticationSuccess').toString()
                        }
                    })
                );
            } else {
                this.$nextTick(() => goToUrl({ url }));
            }
        },
        async logout() {
            const logoutUrl: string | null = await authService.logout();
            const url: string = logoutUrl || this.returnToUrl || '/';
            if (url === '/' || url === '/#' || url === '/#/') {
                await this.logoutCallback();
            } else {
                this.$nextTick(() => goToUrl({ url: logoutUrl }));
            }
        },
        async logoutCallback() {
            await authService.logout();
            await this.store?.clearAuthData();
            // this.$messageBus.emit(LOGOUT_CALLBACK);
            let url: string = this.returnToUrl;
            if (!url) url = '/#/';
            if (url === '/' || url === '/#' || url === '/#/') {
                this.$nextTick(() =>
                    this.$router.push({
                        path: url,
                        query: {
                            notifyType: 'success',
                            notifyMessage: this.$t('userLogoutSuccess').toString()
                        }
                    })
                );
            } else {
                this.$nextTick(() => goToUrl({ url }));
            }
        }
    },
    async created() {
        await authServiceDataLoaded;
        if (!this.action) {
            this.$nextTick(() => goToUrl({ url: this.returnToUrl }));
            return;
        }
        if (this.action === LOGIN || this.action === SIGNIN) {
            this.login();
        } else if (this.action === LOGIN_CALLBACK || this.action === SIGNIN_CALLBACK) {
            this.loginCallback();
        } else if (this.action === LOGOUT || this.action === SIGNOUT) {
            this.logout();
        } else if (this.action === LOGOUT_CALLBACK || this.action === SIGNOUT_CALLBACK) {
            this.logoutCallback();
        }
    }
});
</script>
