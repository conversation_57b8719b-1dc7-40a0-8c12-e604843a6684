/*
This script is imported and executed during application initialization.
Supply values to the global variables below, in order to alter application configuration.
*/

// environment variables
var environVars = {
    applicationRoot: '/Apps/M1Int',
    useServerSession: true
};

// config patch
var configPatch = {
    auth: {
        externalProvider: {
            enabled: true,
            loginUrl: '{appServerPathUrl}#/login',
            logoutUrl: '{appServerPathUrl}',
            userIdHeaderKey: 'OAM_REMOTE_USER',
            simulateExternalProviderUserIdHeader: true,
            loginEnabled: true
        }
    },
    application: {
        locales: {
            default: 'el',
            selectionEnabled: true
        }
    }
};
