import Swal, { <PERSON><PERSON><PERSON>tResult } from 'sweetalert2';
import { App } from 'vue';
import i18n from '../i18n';
import config from '@/config';
import { AlertResult, AlertResultType, DEFAULT_NOTIFICATION_DURATION, IAlert } from './IAlert';

export class SweetAlert implements IAlert {
    public get locale(): string {
        return config.locale || 'en';
    }

    private t(phrase: any) {
        if (!phrase) return phrase;
        if (!i18n?.global?.t) return phrase;
        const res = i18n.global.t(phrase) || phrase;
        return res;
    }

    private replaceProperties(targetObj: any, propertiesDict: any) {
        if (!targetObj || !propertiesDict) return;
        Object.keys(propertiesDict).forEach((p) => {
            if (targetObj.hasOwnProperty(p)) {
                targetObj[propertiesDict[p]] = targetObj[p];
                delete targetObj[p];
            }
        });
    }

    private getTypeOptions(type: any) {
        if (!type) return {};
        const typesDict: any = {
            info: {
                icon: 'info',
                title: this.t('information'),
                textClass: 'info--text',
                get fontIcon() {
                    return 'info_outline';
                }
            },
            success: {
                icon: 'success',
                title: this.t('success'),
                textClass: 'success--text',
                get fontIcon() {
                    return 'done';
                }
            },
            warning: {
                icon: 'warning',
                title: this.t('warning'),
                textClass: 'warning--text',
                get fontIcon() {
                    return 'warning_amber';
                }
            },
            error: {
                icon: 'error',
                title: this.t('error'),
                textClass: 'error--text',
                get fontIcon() {
                    return 'dangerous_outline';
                }
            },
            question: {
                icon: 'question',
                title: this.t('question'),
                textClass: 'warning--text',
                get fontIcon() {
                    return 'help_outline';
                }
            },
            confirm: {
                icon: 'warning',
                title: this.t('confirmation'),
                textClass: 'warning--text',
                get fontIcon() {
                    return 'help_outline';
                }
            },
            progress: {
                icon: '',
                title: this.t('progress'),
                textClass: 'info--text',
                get fontIcon() {
                    return 'autorenew';
                }
            }
        };
        const res = typesDict[type] || {};
        return res;
    }

    private getSwalOptionsFromOptions(options?: any): any {
        if (!options) return null;
        const res: any = {};
        Object.assign(res, options);
        ['type', 'duration', 'multiple'].forEach((p) => {
            if (res.hasOwnProperty(p)) {
                delete res[p];
            }
        });
        if (!res.showClass) {
            res.showClass = {
                popup: 'swal-popup-show'
            };
        }
        if (!res.hideClass) {
            res.hideClass = {
                popup: 'swal-popup-hide'
            };
        }
        if (options.type !== 'progress') {
            const cancelButtonType = 'secondary';
            let confirmButtonType = 'primary';
            switch (options.type) {
                case 'success':
                case 'warning':
                case 'error':
                    confirmButtonType = options.type;
                    break;
                case 'danger':
                    confirmButtonType = 'error';
                    break;
                case 'info':
                case 'prompt':
                    confirmButtonType = 'info';
                    break;
                case 'question':
                case 'confirm':
                    confirmButtonType = 'primary';
                    break;
            }
            res.buttonsStyling = true;
            if (!res.customClass) {
                res.customClass = {
                    title: options.title && options.html ? 'title-and-message' : '',
                    htmlContainer: options.title && options.html ? 'title-and-message' : '',
                    confirmButton: `${confirmButtonType} q-mx-md`,
                    cancelButton: `${cancelButtonType} q-mx-md`
                };
            }
        }
        return res;
    }

    private getAlertResultFromSwalResult(swalResult: SweetAlertResult) {
        const res: AlertResult = new AlertResult();
        if (swalResult) {
            res.value = swalResult.value;
            if (swalResult.isDenied) {
                res.type = AlertResultType.Cancel;
            } else if (swalResult.isDismissed) {
                res.type = AlertResultType.Close;
            } else {
                res.type = AlertResultType.Ok;
            }
        }
        return res;
    }

    public close(): void {
        Swal.close();
    }

    private fire(options?: any): Promise<AlertResult> {
        this.close();
        return Swal.fire(this.getSwalOptionsFromOptions(options))
            .then((swalResult) => {
                const result = this.getAlertResultFromSwalResult(swalResult);
                return result;
            })
            .catch((ex) => {
                console.error(ex || 'Swal.fire error!');
                return new AlertResult(AlertResultType.None);
            });
    }

    public async messageBox(options?: any): Promise<AlertResult> {
        this.replaceProperties(options, { message: 'html' });
        const typeOptions = this.getTypeOptions(options.type);
        options.title = options.title || typeOptions.title;
        options.icon = options.icon || typeOptions.icon;
        options.confirmButtonText = options.confirmButtonText || this.t('ok');
        if (options.allowOutsideClick === undefined || options.allowOutsideClick === null)
            options.allowOutsideClick = false;
        if (options.allowEscapeKey === undefined || options.allowEscapeKey === null) options.allowEscapeKey = false;
        if (options.backdrop === undefined || options.backdrop === null) options.backdrop = true;
        options.heightAuto = false;
        return await this.fire(options);
    }

    public async messageBoxInfo(options?: any): Promise<AlertResult> {
        options = options || {};
        options.type = 'info';
        return await this.messageBox(options);
    }

    public async messageBoxQuestion(options?: any): Promise<AlertResult> {
        options = options || {};
        options.type = 'question';
        return await this.messageBox(options);
    }

    public async messageBoxSuccess(options?: any): Promise<AlertResult> {
        options = options || {};
        options.type = 'success';
        return await this.messageBox(options);
    }

    public async messageBoxWarning(options?: any): Promise<AlertResult> {
        options = options || {};
        options.type = 'warning';
        return await this.messageBox(options);
    }

    public async messageBoxError(options?: any): Promise<AlertResult> {
        options = options || {};
        options.type = 'error';
        return await this.messageBox(options);
    }

    public async notify(options?: any): Promise<AlertResult> {
        this.replaceProperties(options, { message: 'html' });
        if (options.toast === undefined || options.toast === null) options.toast = true;
        const typeOptions = this.getTypeOptions(options.type);
        options.icon = options.icon || typeOptions.icon;
        options.position = options.position || 'top-end';
        const timer = options.timer || options.duration;
        options.timer = timer === undefined ? DEFAULT_NOTIFICATION_DURATION : timer;
        /*
        if (options.allowOutsideClick === undefined || options.allowOutsideClick === null)
            options.allowOutsideClick = false;
        */
        if (options.showConfirmButton === undefined || options.showConfirmButton === null)
            options.showConfirmButton = false;
        if (options.allowEscapeKey === undefined || options.allowEscapeKey === null) options.allowEscapeKey = false;
        return await this.fire(options);
    }

    public async notifyInfo(options?: any): Promise<AlertResult> {
        options = options || {};
        options.duration = options.duration === undefined ? DEFAULT_NOTIFICATION_DURATION : options.duration;
        options.type = options.type || 'info';
        return this.notify(options);
    }

    public async notifyQuestion(options?: any): Promise<AlertResult> {
        options = options || {};
        options.duration = options.duration === undefined ? DEFAULT_NOTIFICATION_DURATION : options.duration;
        options.type = options.type || 'question';
        return this.notify(options);
    }

    public async notifySuccess(options?: any): Promise<AlertResult> {
        options = options || {};
        options.duration = options.duration === undefined ? DEFAULT_NOTIFICATION_DURATION : options.duration;
        options.type = options.type || 'success';
        return await this.notify(options);
    }

    public async notifyWarning(options?: any): Promise<AlertResult> {
        options = options || {};
        options.duration = options.duration === undefined ? DEFAULT_NOTIFICATION_DURATION : options.duration;
        options.type = options.type || 'warning';
        return this.notify(options);
    }

    public async notifyError(options?: any): Promise<AlertResult> {
        options = options || {};
        options.duration = options.duration === undefined ? DEFAULT_NOTIFICATION_DURATION : options.duration;
        options.type = options.type || 'error';
        return this.notify(options);
    }

    public async confirm(options?: any): Promise<AlertResult> {
        this.replaceProperties(options, { message: 'html' });
        options.type = options.type || 'confirm';
        const typeOptions = this.getTypeOptions(options.type);
        options.title = options.title || typeOptions.title;
        options.icon = options.icon || typeOptions.icon;
        options.showCancelButton = true;
        if (options.reverseButtons === undefined || options.reverseButtons === null) options.reverseButtons = false;
        options.confirmButtonText = options.confirmButtonText || this.t('ok');
        options.cancelButtonText = options.cancelButtonText || this.t('cancel');
        if (options.allowOutsideClick === undefined || options.allowOutsideClick === null)
            options.allowOutsideClick = false;
        if (options.allowEscapeKey === undefined || options.allowEscapeKey === null) options.allowEscapeKey = false;
        if (options.backdrop === undefined || options.backdrop === null) options.backdrop = true;
        options.heightAuto = false;
        return await this.fire(options);
    }

    public async confirmYesNo(options?: any): Promise<AlertResult> {
        options = options || {};
        options.icon = options.icon || 'question';
        options.confirmButtonText = options.confirmButtonText || this.t('yes');
        options.cancelButtonText = options.cancelButtonText || this.t('no');
        return await this.confirm(options);
    }

    public async prompt(options?: any): Promise<AlertResult> {
        this.replaceProperties(options, { message: 'html' });
        const typeOptions = this.getTypeOptions(options.type);
        options.icon = options.icon || typeOptions.icon;
        options.showCancelButton = true;
        if (options.reverseButtons === undefined || options.reverseButtons === null) options.reverseButtons = false;
        options.confirmButtonText = options.confirmButtonText || this.t('ok');
        options.cancelButtonText = options.cancelButtonText || this.t('cancel');
        if (options.allowOutsideClick === undefined || options.allowOutsideClick === null)
            options.allowOutsideClick = false;
        if (options.allowEscapeKey === undefined || options.allowEscapeKey === null) options.allowEscapeKey = false;
        if (options.backdrop === undefined || options.backdrop === null) options.backdrop = true;
        options.heightAuto = false;
        options.input = 'text';
        options.inputValue = options.defaultValue || '';
        return await this.fire(options);
    }

    public async selectFiles(options?: any): Promise<AlertResult> {
        this.replaceProperties(options, { message: 'html' });
        const typeOptions = this.getTypeOptions(options.type);
        options.icon = options.icon || typeOptions.icon;
        options.showCancelButton = true;
        if (options.reverseButtons === undefined || options.reverseButtons === null) options.reverseButtons = false;
        options.confirmButtonText = options.confirmButtonText || this.t('ok');
        options.cancelButtonText = options.cancelButtonText || this.t('cancel');
        if (options.allowOutsideClick === undefined || options.allowOutsideClick === null)
            options.allowOutsideClick = false;
        if (options.allowEscapeKey === undefined || options.allowEscapeKey === null) options.allowEscapeKey = false;
        if (options.backdrop === undefined || options.backdrop === null) options.backdrop = true;
        options.heightAuto = false;
        const buttonSelectId = 'buttonSelect';
        const buttonClearId = 'buttonClear';
        const fileInputId = 'fileInput';
        const fileTextId = 'fileText';
        const htmlContent = `<div class="text-center ${typeOptions.textClass ? ' ' + typeOptions.textClass : ''}">
                ${typeOptions.fontIcon ? '<span class="' + typeOptions.fontIcon + '"></span>' : ''}
                <div>${options.html}</div>
                <div class="select-files-content">
                    <input id="${fileInputId}" type="file" ${options.multiple ? 'multiple' : ''} class="file-input"
                        onchange="var elementFilesInput = document.getElementById('${fileInputId}');
                            var elementFilesText = document.getElementById('${fileTextId}');
                            var files = Array.from(elementFilesInput.files);
                            if (!files || files.length <= 0)
                                elementFilesText.value = '';
                            else
                                elementFilesText.value = files.map(f => f.name).join(', ');"
                    />
                    <table class="full-width">
                        <tr>
                            <td class="column-button-select">
                                <button id="${buttonSelectId}"
                                    onclick="event.preventDefault(); event.stopPropagation();
                                        var elementFilesInput = document.getElementById('${fileInputId}');
                                        setTimeout(() => elementFilesInput.click(), 0);"
                                    class="q-btn success">
                                    <span class="material-icons icon">attach_file</span>
                                </button>
                            </td>
                            <td class="column-file-text">
                                <textarea id="${fileTextId}" rows="${options.multiple ? 3 : 1.25}" readonly>
                                </textarea>
                            </td>
                            <td class="column-button-clear">
                                <button id="${buttonClearId}"
                                    onclick="event.preventDefault(); event.stopPropagation();
                                    var elementFilesInput = document.getElementById('${fileInputId}');
                                    var elementFilesText = document.getElementById('${fileTextId}');
                                    elementFilesInput.value = ''; elementFilesText.value = '';"
                                    class="q-btn warning">
                                    <span class="material-icons icon">close</span>
                                </button>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>`;
        options.html = htmlContent;
        /*
        options.input = 'file'
        options.inputAttributes = { 'multiple': true };
        */
        options.preConfirm = (value: any) => {
            let res: any = null;
            if (value) {
                const elementFilesInput: any = document.getElementById(fileInputId);
                if (elementFilesInput) res = Array.from(elementFilesInput.files);
            }
            return res;
        };
        return await this.fire(options);
    }

    public async progress(options?: any): Promise<AlertResult> {
        this.replaceProperties(options, { message: 'html' });
        options.type = options.type || 'progress';
        if (options.toast === undefined || options.toast === null) {
            options.toast = false;
        }
        if (options.toast) {
            options.position = options.position || 'top-end';
        } else {
            options.position = options.position || 'center';
        }
        const title: string = options.title;
        options.title =
            '<div class="column justify-center items-center">' +
            '<div class="full-width text-center">' +
            '<img src="assets/img/loading/three-dots-orange.svg" alt="progress" width="64" />' +
            '</div>';
        if (title) {
            if (!options.toast) {
                options.title += `<div class="progress-title">${title}</div>`;
            } else {
                options.title += `<div class="progress-title toast">${title}</div>`;
            }
        }
        options.title += '</div>';
        if (!options.toast) {
            if (options.allowOutsideClick === undefined || options.allowOutsideClick === null)
                options.allowOutsideClick = false;
            if (options.allowEscapeKey === undefined || options.allowEscapeKey === null) options.allowEscapeKey = true;
        } else {
            if (options.allowEscapeKey === undefined || options.allowEscapeKey === null) options.allowEscapeKey = false;
        }
        if (options.showConfirmButton === undefined || options.showConfirmButton === null)
            options.showConfirmButton = false;
        return await this.fire(options);
    }

    public static install(app: App<Element>) {
        if (!app.config.globalProperties) return;
        if (app.config.globalProperties.$alert) return;
        const swal: SweetAlert = new SweetAlert();
        app.config.globalProperties.$alert = swal;
    }
}
