import { createI18n } from 'vue-i18n';
import en from './locales/en.json';
import el from './locales/el.json';
import config from '@/config';

type MessageSchema = typeof en;

export const messages: any = { en, el };

const i18n = createI18n<[MessageSchema], 'en' | 'el'>({
    legacy: false,
    globalInjection: true,
    locale: config.locale,
    fallbackLocale: config.fallbackLocale,
    messages
});

export default i18n;
