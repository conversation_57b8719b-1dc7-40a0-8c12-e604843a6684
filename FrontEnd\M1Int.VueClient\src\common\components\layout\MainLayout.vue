<template>
    <q-layout view="hHh lpR fFf" class="main-layout">
        <main-header @toggleDrawer="toggleDrawer" />
        <q-drawer
            ref="drawer"
            v-model="drawerOpen"
            behavior="default"
            :show-if-above="false"
            :width="260"
            :breakpoint="1279"
            :overlay="overlay"
            bordered
            class="main-drawer"
        >
            <main-drawer-content @closeDrawer="closeDrawer" />
        </q-drawer>
        <q-page-container class="main-content">
            <router-view />
        </q-page-container>
        <main-footer />
    </q-layout>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import type { PropType } from 'vue';
import authService, { authServiceDataLoaded } from '@/auth/services/AuthService';
import { getCommonStore, ICommonStore } from '@/common/stores/Store';
import MainHeader from './MainHeader.vue';
import MainDrawerContent from './MainDrawerContent.vue';
import MainFooter from './MainFooter.vue';

export default defineComponent({
    name: 'main-layout',
    components: {
        MainHeader,
        MainDrawerContent,
        MainFooter
    },
    props: {
        commonStore: {
            type: Object as PropType<ICommonStore>,
            default: () => getCommonStore()
        }
    },
    data() {
        const dataObj: {
            drawerOpen: boolean;
        } = {
            drawerOpen: true
        };
        return dataObj;
    },
    computed: {
        overlay(): boolean {
            // return !!this.$q?.lt?.md;
            return false;
        }
    },
    methods: {
        toggleDrawer() {
            this.drawerOpen = !this.drawerOpen;
        },
        closeDrawer(forceClose: boolean = false) {
            if (forceClose) {
                this.drawerOpen = false;
            } else {
                if (this.$q?.screen?.lt?.md || (this.$refs?.drawer as any)?.overlay) {
                    this.drawerOpen = false;
                }
            }
        }
    },
    async created() {
        if (this.$q?.screen.lt?.md) {
            this.drawerOpen = false;
        }
        await authServiceDataLoaded;
        this.commonStore?.setAuthData(authService?.authData);
    }
});
</script>
