<template>
    <div class="row no-wrap justify-start items-center all-pointer-events field-label">
        <div v-if="label" class="label">{{ label }}</div>
        <q-icon v-if="hint" name="info" medium class="q-mx-xs text-primary field-label-info-icon">
            <q-tooltip
                v-if="isTooltip"
                max-width="320px"
                class="field-label-info tooltip"
                anchor="top start"
                self="bottom start"
                :offset="[2, 2]"
            >
                <q-banner rounded class="banner">
                    <div class="field-info-content" v-html="hint"></div>
                </q-banner>
            </q-tooltip>
            <q-popup-proxy v-else max-width="320px" :breakpoint="300" class="field-label-info popup">
                <q-banner rounded class="banner">
                    <div class="field-info-content" v-html="hint"></div>
                </q-banner>
            </q-popup-proxy>
        </q-icon>
    </div>
</template>
<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
    name: 'field-label',
    props: {
        label: {
            type: String,
            default: null
        },
        hint: {
            type: String,
            default: null
        },
        isTooltip: {
            type: Boolean,
            default: false
        }
    }
});
</script>

<style>
.field-label label,
.field-label .label {
    color: rgba(0, 0, 0, 0.6);
}

.field-label-info-icon {
    cursor: pointer !important;
    font-size: 1.2rem !important;
    color: var(--palette-primary) !important;
    opacity: 0.5;
}

.field-label-info-icon:hover,
.field-label-info-icon:focus {
    opacity: 1;
}

.field-label-info.tooltip,
.field-label-info.popup {
    padding: 0;
}

.field-label-info.tooltip .banner,
.field-label-info.popup .banner {
    background-color: var(--palette-primary);
    color: white;
}

.field-label-info.tooltip .banner a,
.field-label-info.tooltip .banner a:hover,
.field-label-info.tooltip .banner a:visited,
.field-label-info.tooltip .banner a:focus,
.field-label-info.popup .banner a,
.field-label-info.popup .banner a:hover,
.field-label-info.popup .banner a:visited,
.field-label-info.popup .banner a:focus {
    color: white;
}
</style>
