import { shallowMount, VueWrapper } from '@vue/test-utils';
import mockAlert from '@/mocks/MockAlert';
import FieldDisplay from '@/common/components/form/FieldDisplay.vue';
import { Component } from 'vue';

describe('FieldDisplay', () => {
    let wrapper: VueWrapper;
    let component: Component;

    beforeEach(() => {
        wrapper = shallowMount(FieldDisplay, {
            global: {
                mocks: {
                    $t: (key: string) => {
                        return key;
                    },
                    $alert: mockAlert
                }
            }
        });
        component = wrapper.vm as Component;
    });

    afterEach(() => {});

    it('should mount', () => {
        expect(wrapper).toBeTruthy();
        expect(wrapper.vm).toBeTruthy();
        expect(wrapper.vm.$el).toBeTruthy();
    });

    it('component should be valid', () => {
        expect(component).toBeTruthy();
    });

    it('should have valid $t property', () => {
        expect(wrapper.vm.$t).toBeTruthy();
        expect(typeof wrapper.vm.$t).toEqual('function');
    });

    it('should have valid $alert property', () => {
        expect((wrapper.vm as any).$alert).toBeTruthy();
    });

    describe('should be valid when using model', () => {
        const model: any = {
            id: '001',
            title: 'Title 001',
            description: 'Description 001'
        };

        it('should have valid field id value', async () => {
            await wrapper.setProps({
                modelObject: model,
                fieldId: 'id',
                fieldValue: { id: 'Id' }
            });
            expect((component as any).valueResolved).toEqual(model.id);
        });

        it('should have valid field id label', async () => {
            await wrapper.setProps({
                modelObject: model,
                fieldId: 'id',
                fieldTitle: 'Id'
            });
            expect(wrapper.attributes('label')).toContain((component as any).fieldTitle);
        });

        it('should have valid field id modelValue', async () => {
            await wrapper.setProps({
                modelObject: model,
                fieldId: 'id',
                fieldTitle: 'Id'
            });
            expect(wrapper.attributes('modelvalue')).toEqual(model.id);
        });

        it('should have valid field title value', async () => {
            await wrapper.setProps({
                modelObject: model,
                fieldId: 'title',
                fieldTitle: 'Title'
            });
            expect((component as any).valueResolved).toEqual(model.title);
        });

        it('should have valid field title label', async () => {
            await wrapper.setProps({
                modelObject: model,
                fieldId: 'title',
                fieldTitle: 'Title'
            });
            expect(wrapper.attributes('label')).toContain((component as any).fieldTitle);
        });

        it('should have valid field title modelValue', async () => {
            await wrapper.setProps({
                modelObject: model,
                fieldId: 'title',
                fieldTitle: 'Title'
            });
            expect(wrapper.attributes('modelvalue')).toEqual(model.title);
        });

        it('should have valid field description value', async () => {
            await wrapper.setProps({
                modelObject: model,
                fieldId: 'description',
                fieldTitle: 'Description'
            });
            expect((component as any).valueResolved).toEqual(model.description);
        });

        it('should have valid field description label', async () => {
            await wrapper.setProps({
                modelObject: model,
                fieldId: 'description',
                fieldTitle: 'Description'
            });
            expect(wrapper.attributes('label')).toContain((component as any).fieldTitle);
        });

        it('should have valid field description modelValue', async () => {
            await wrapper.setProps({
                modelObject: model,
                fieldId: 'description',
                fieldTitle: 'Description'
            });
            expect(wrapper.attributes('modelvalue')).toEqual(model.description);
        });
    });

    describe('should be valid when using value', () => {
        it(`should have valid string value '001'`, async () => {
            await wrapper.setProps({
                value: '001'
            });
            expect((component as any).valueResolved).toEqual('001');
        });

        it(`should have valid number value 123`, async () => {
            await wrapper.setProps({
                value: 123
            });
            expect((component as any).valueResolved).toEqual('123');
        });
    });
});
