import { defineComponent } from 'vue';
import dayjs from '@/plugins/dayjs';
import M1Helper from '../M1Helper';
import { Declaration } from '@/m1Module/models/m1/Entities/Declaration';
import { openNewUrl } from '@/common/utilities';
import { GetDeclarationResponse } from '@/m1Module/models/m1/Services/GetDeclaration/GetDeclarationResponse';
import { GetDeclarationReportRequest } from '@/m1Module/models/m1/Services/GetDeclarationReport/GetDeclarationReportRequest';
import { DeclarationAttachment } from '@/m1Module/models/m1/Entities/DeclarationAttachment';
import { GetDeclarationAttachmentRequest } from '@/m1Module/models/m1/Services/GetDeclarationAttachment/GetDeclarationAttachmentRequest';
import { DeclarationLite } from '@/m1Module/models/m1/Entities/DeclarationLite';
import { DeclarationsOptions } from '@/m1Module/models/m1/Entities/DeclarationsOptions';

export default defineComponent({
    name: 'declaration-helper',
    mixins: [M1Helper],
    computed: {
        currentDateTime(): Date {
            return this.commonStore?.serverCurrentDateTime || dayjs().toDate();
        },
        declarationsOptions(): DeclarationsOptions {
            return this.store?.declarationsOptions || new DeclarationsOptions();
        },
        zeroAfmAllowed(): boolean {
            return this.store?.declarationsOptions?.zeroAfmAllowed || false;
        },
        declarationProtocolCodeActive(): boolean {
            return !!this.declarationsOptions?.declarationProtocolCodeActive;
        },
        submittedDeclarationsLite(): Array<DeclarationLite> {
            return this.store?.submittedDeclarationsLite || [];
        },
        hasSubmittedDeclarations(): boolean {
            return this.submittedDeclarationsLite.length > 0;
        },
        attachmentsAllowedExtensions(): string {
            return this.declarationsOptions.attachments.attachmentsAllowedExtensions.join(',');
        },
        attachmentsMaxFiles(): number {
            return this.declarationsOptions.attachments.attachmentsMaxFilesNumber;
        },
        multipleAttachmentsAllowed(): boolean {
            return this.attachmentsMaxFiles > 1;
        },
        attachmentsMaxFileSize(): number {
            return this.declarationsOptions.attachments.attachmentsMaxFileSize;
        },
        attachmentsMaxFileSizeInBytes(): number {
            return this.declarationsOptions.attachments.attachmentsMaxFileSize * 1024;
        },
        attachmentsMaxTotalFilesSize(): number {
            return this.declarationsOptions.attachments.attachmentsMaxTotalSize;
        },
        attachmentsMaxTotalFilesSizeInBytes(): number {
            return this.declarationsOptions.attachments.attachmentsMaxTotalSize * 1024;
        }
    },
    methods: {
        focusHtmlElement(elementId: string) {
            if (!elementId) return;
            this.$nextTick(() => {
                const elem: HTMLElement | null = document.getElementById(elementId);
                if (elem?.focus) elem.focus();
            });
        },
        focusComponentElement(componentRef: string) {
            if (!componentRef || !this.$refs) return;
            const component: any = this.$refs[componentRef];
            if (!component?.elementId) return;
            this.focusHtmlElement(component?.elementId);
        },
        getToday(): dayjs.Dayjs {
            return dayjs(dayjs(this.currentDateTime).format('YYYY-MM-DDT00:00:00Z'));
        },
        getDeclarationDescriptionText(declaration: Declaration | DeclarationLite): string {
            return declaration?.id?.toString() || '';
        },
        async getSubmittedDeclarationDetails(id: number): Promise<Declaration | null> {
            const response: GetDeclarationResponse = await this.store?.getDeclaration({
                id: id
                // declarationStatus: DeclarationStatus.Submitted
            });
            if (response?.success && response?.result) return new Declaration(response?.result);
            return null;
        },
        async viewDeclarationAttachment(declaration: Declaration, attachment: DeclarationAttachment) {
            if (!declaration?.id || !attachment?.id) return;
            try {
                this.$alert?.progress({
                    title: this.$t('viewAttachment'),
                    message: this.$t('pleaseWait').toString()
                });
                const request: GetDeclarationAttachmentRequest = new GetDeclarationAttachmentRequest({
                    declarationId: declaration.id,
                    declarationAttachmentId: attachment.id
                });
                const response: any = await this.store?.getDeclarationAttachment(request);
                this.$alert?.close();
                if (!response) return;
                const pdfBLOB = new Blob([response], { type: attachment.fileType || '' });
                const pdfUrl = window.URL.createObjectURL(pdfBLOB);
                setTimeout(() => openNewUrl({ url: pdfUrl }), 200);
            } catch (err: any) {
                await this.$alert.messageBoxWarning({
                    title: this.$t('viewAttachment'),
                    message: err ? err.toString() : this.$t('errorMessage')
                });
            } finally {
                this.$alert?.close();
            }
        },
        async printDeclaration(declaration: Declaration) {
            if (!declaration?.id) return;
            try {
                this.$alert?.progress({
                    title: this.$t('printDeclaration'),
                    message: this.$t('pleaseWait').toString()
                });
                const request: GetDeclarationReportRequest = new GetDeclarationReportRequest({
                    id: declaration.id
                });
                const response: any = await this.store?.getDeclarationReport(request);
                this.$alert?.close();
                if (!response) return;
                const pdfBLOB = new Blob([response], { type: 'application/pdf' });
                const pdfUrl = window.URL.createObjectURL(pdfBLOB);
                setTimeout(() => openNewUrl({ url: pdfUrl }), 200);
            } catch (err: any) {
                await this.$alert.messageBoxWarning({
                    title: this.$t('printDeclaration'),
                    message: err ? err.toString() : this.$t('errorMessage')
                });
            } finally {
                this.$alert?.close();
            }
        }
    }
});
