@import 'sweetalert2/dist/sweetalert2.css';

:root {
    --fonts-primary: '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', 'Liberation Sans', 'Open Sans', 'Arial', 'sans-serif';
    /* --fonts-primary: Arial, 'Helvetica Neue', Helvetica, sans-serif; */

    --palette-primary: #009fdf;
    --palette-primary-medium: #006ea1;
    --palette-primary-dark: #112d63;
    --palette-secondary: #607d8b;
    --palette-accent: #8c9eff;
    --palette-info: #2196f3;
    --palette-success: #4caf50;
    --palette-warning: #fb8c00;
    --palette-error: #c10015;
    --palette-dark: #1d1d1d;
}

body {
    font-family: var(--fonts-primary) !important;
    margin: 0;
    padding: 0;
    min-width: 360px;
}

#app {
    width: 100%;
}

.main-drawer-content {
    background-color: white !important;
    color: var(--palette-primary-dark) !important;
}

.main-header {
    width: 100%;
    height: 70px;
    min-height: 70px;
    max-height: 70px;
    margin: 0 !important;
    padding: 0 !important;
    background-color: white !important;
    color: var(--palette-primary-dark) !important;
}

.main-header .q-toolbar {
    margin: 0 !important;
    padding: 0 8px !important;
    justify-content: space-between;
}

.main-logo {
    height: 20px;
    margin-left: 4px;
    margin-right: 4px;
    cursor: pointer;
}

.main-title {
    font-size: 11px;
    font-weight: bold;
    line-height: 1.2em;
    text-align: center;
}

@media screen and (min-width: 425px) {
    .main-logo {
        height: 24px;
        margin-left: 6px;
        margin-right: 6px;
        cursor: pointer;
    }
    .main-title {
        font-size: 12px;
    }
}

@media screen and (min-width: 500px) {
    .main-logo {
        height: 30px;
    }
    .main-title {
        font-size: 13px;
    }
}

@media screen and (min-width: 600px) {
    .main-logo {
        height: 35px;
        margin-left: 10px;
        margin-right: 10px;
        cursor: pointer;
    }
    .main-title {
        font-size: 14px;
    }
}

@media screen and (min-width: 1024px) {
    .main-logo {
        height: 40px;
        margin-left: 8px;
        margin-right: 8px;
    }
    .main-title {
        font-size: 16px;
    }
}

@media screen and (min-width: 1440px) {
    .main-logo {
        height: 50px;
        margin-left: 12px;
        margin-right: 12px;
    }
    .main-title {
        font-size: 19px;
    }
}

@media screen and (min-width: 1920px) {
    .main-title {
        font-size: 22px;
    }
}

.main-header .q-icon,
.main-header button,
.main-header .q-btn,
.main-header a,
.mainheader .main-title {
    color: var(--palette-primary-dark) !important;
}

.q-drawer {
    position: fixed !important;
}
.main-drawer {
    background-color: white !important;
}

.main-footer {
    width: 100%;
    height: 40px;
    min-height: 40px;
    max-height: 40px;
    background-color: white !important;
    color: var(--palette-primary-dark) !important;
    font-size: 0.85rem;
}

.main-content {
    padding: 0;
    margin: 0;
    /* min-height: calc(100vh - 40px); */
    min-height: 100vh;
    background-color: transparent;
    word-break: break-word;
}

.w100,
.full-width,
.fill-width {
    width: 100% !important;
}

.h-100,
.full-height,
.fill-height {
    height: 100% !important;
}

.break-spaces {
    white-space: break-spaces;
}

.visible {
    visibility: visible;
}

.invisible {
    visibility: hidden;
}

.faded {
    opacity: 0.5;
}

.semi-faded {
    opacity: 0.75;
}

.full-faded {
    opacity: 0;
}

.faded:hover,
.faded:focus,
.semi-faded:hover,
.semi-faded:focus {
    opacity: 1;
}

.font-weight-bold {
    font-weight: bold;
}

.font-style-italic {
    font-style: italic;
}

.white {
    background-color: white !important;
    border-color: white !important;
    color: var(--palette-primary-dark) !important;
}
.white--text {
    color: white !important;
}

.primary {
    background-color: var(--palette-primary) !important;
    border-color: var(--palette-primary) !important;
    color: white !important;
}
.primary--text {
    color: var(--palette-primary) !important;
}

.primary-medium {
    background-color: var(--palette-primary-medium) !important;
    border-color: var(--palette-primary-medium) !important;
    color: white !important;
}
.primary-medium--text {
    color: var(--palette-primary-medium) !important;
}

.primary-dark {
    background-color: var(--palette-primary-dark) !important;
    border-color: var(--palette-primary-dark) !important;
    color: white !important;
}
.primary-dark--text {
    color: var(--palette-primary-dark) !important;
}

.secondary {
    background-color: var(--palette-secondary) !important;
    border-color: var(--palette-secondary) !important;
    color: white !important;
}
.secondary--text {
    color: var(--palette-secondary) !important;
}

.info {
    background-color: var(--palette-info) !important;
    border-color: var(--palette-info) !important;
    color: white !important;
}
.info--text {
    color: var(--palette-info) !important;
}

.success {
    background-color: var(--palette-success) !important;
    border-color: var(--palette-success) !important;
    color: white !important;
}
.success--text {
    color: var(--palette-success) !important;
}

.warning {
    background-color: var(--palette-warning) !important;
    border-color: var(--palette-warning) !important;
    color: white !important;
}
.warning--text {
    color: var(--palette-warning) !important;
}

.error,
.danger {
    background-color: var(--palette-error) !important;
    border-color: var(--palette-error) !important;
    color: white !important;
}
.error--text,
.danger--text {
    color: var(--palette-error) !important;
}

.q-field.invalid .q-field__label,
.invalid label,
.invalid .label {
    color: var(--palette-error) !important;
}

.invalid input,
.invalid textarea {
    border-color: var(--palette-error) !important;
    /* border-left-width: 12px !important; */
}

.q-field.valid.dirty .q-field__label,
.valid.dirty label,
.valid.dirty .label {
    color: var(--palette-success) !important;
}

.valid.dirty input,
.valid.dirty textarea {
    border-color: var(--palette-success) !important;
    /* border-left-width: 12px !important ;*/
}

.required-mark-container {
    width: 8px;
}

.required-mark {
    color: var(--palette-error) !important;
    padding: 0 !important;
    margin: 0 !important;
}

.q-field .q-icon {
    cursor: pointer;
    padding-top: 13px;
}

.q-field .q-field__label .q-icon {
    padding-top: 0;
}

.q-field .q-chip .q-icon {
    cursor: pointer;
    padding-top: 3px;
}

button,
.btn,
.q-btn {
    text-transform: none !important;
    cursor: pointer !important;
}

a {
    cursor: pointer;
}

.q-option-group .q-radio.inline,
.q-option-group .q-checkbox.inline {
    margin: 0 4px;
}

table.q-table thead tr th {
    white-space: normal;
    word-break: normal;
}

.q-menu {
    z-index: 7000 !important;
}

.q-dialog__inner--minimized {
    padding: 4px !important;
}

@media (min-width: 600px) {
    .q-dialog__inner--minimized {
        padding: 8px !important;
    }
}

@media (min-width: 900px) {
    .q-dialog__inner--minimized {
        padding: 12px !important;
    }
}

@media (min-width: 1024px) {
    .q-dialog__inner--minimized {
        padding: 16px !important;
    }
}

@media (min-width: 1280px) {
    .q-dialog__inner--minimized {
        padding: 20px !important;
    }
}

@media (min-width: 1400px) {
    .q-dialog__inner--minimized {
        padding: 24px !important;
    }
}

/* sweetalert2 */

@keyframes fade-in {
    0% {
        opacity: 0;
        transform: scale(0);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes fade-out {
    0% {
        opacity: 1;
        transform: scale(1);
    }
    100% {
        opacity: 0;
        transform: scale(0);
    }
}

body.swal2-shown:not(.swal2-toast-shown) {
    overflow: hidden !important;
}

.swal-popup-show {
    animation: fade-in 0.2s;
}

.swal-popup-hide {
    animation: fade-out 0.2s;
}

.swal2-popup.swal2-modal {
    padding: 1em;
    /*
    background-color: rgba(54, 70, 93, 0.99);
    color: white;
    */
}

.swal2-container {
    padding: 0 5px !important;
    z-index: 6600;
}

@media (min-width: 600px) {
    .swal2-container {
        padding: 0 !important;
    }
}

.swal2-container .swal2-icon.swal2-info {
    color: var(--palette-info) !important;
}

.swal2-container .swal2-icon.swal2-success {
    color: var(--palette-success) !important;
}

.swal2-container .swal2-icon.swal2-warning {
    color: var(--palette-warning) !important;
}

.swal2-container .swal2-icon.swal2-error {
    color: var(--palette-error) !important;
}

.swal2-popup h1,
.swal2-popup h2,
.swal2-popup h3,
.swal2-popup h4,
.swal2-popup h5,
.swal2-popup h6 {
    padding: 0;
    margin: 0;
    line-height: 1.5;
}

.swal2-popup.swal2-modal .swal2-content {
    padding: 0;
}

.swal2-popup.swal2-modal .swal2-icon.swal2-icon-show {
    margin: 0.2em auto 0.4em auto;
}

.swal2-container.swal2-top,
.swal2-container.swal2-top-start,
.swal2-container.swal2-top-end {
    top: 74px !important;
}

.swal2-container.swal2-top-start {
    left: 4px !important;
}

.swal2-container.swal2-top-end {
    right: 4px !important;
}

.swal2-container .swal2-popup.swal2-toast {
    padding: 0.4em 0.8em !important;
}

.swal2-title {
    width: 100%;
    font-size: 1.5em !important;
    padding: 0.25em 0 0 0 !important;
}

.swal2-html-container {
    width: 100%;
    font-size: 1em !important;
    margin: 0.75em auto !important;
    padding: 0 !important;
}

.swal2-container .swal2-popup.swal2-toast .swal2-close {
    grid-row: 1 !important;
}

.swal2-popup.swal2-toast .swal2-title {
    margin: 0.3em 0.2em !important;
}

.swal2-popup.swal2-toast .swal2-html-container {
    padding-top: 0.3em;
    margin: 1em 0.2em !important;
    font-size: 0.95em !important;
}

.swal2-popup.swal2-toast .swal2-title.title-and-message {
    margin: 0.1em 0.2em !important;
}

.swal2-popup.swal2-toast .swal2-html-container.title-and-message {
    padding-top: 0;
    margin: 0.1em 0.2em 0.1em 0.2em !important;
}

.swal2-container .swal2-actions {
    width: 100%;
    margin: 0.75em auto 0.25em auto;
}

.swal2-container .swal2-actions button {
    min-width: 100px !important;
}

.swal2-container .progress-title {
    margin: 12px auto 2px auto;
}

.swal2-container .progress-title.toast {
    margin: 6px;
}

.swal2-container .select-files-content {
    width: 100%;
}

.swal2-container .select-files-content .file-input {
    visibility: collapse;
    height: 0;
    width: 0;
    margin: 0;
    padding: 0;
}

.swal2-container .select-files-content .column-button-select,
.swal2-container .select-files-content .column-button-clear {
    height: 36px;
    width: 40px;
    text-align: center;
}

.swal2-container .select-files-content .column-button-select button,
.swal2-container .select-files-content .column-button-select .q-btn,
.swal2-container .select-files-content .column-button-clear button,
.swal2-container .select-files-content .column-button-clear .q-btn {
    height: 36px;
    width: 40px;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding: 0;
    cursor: pointer;
}

.swal2-container .select-files-content .column-button-select .icon,
.swal2-container .select-files-content .column-button-clear .icon {
    height: 32px;
}

.swal2-container .select-files-content .column-file-text textarea {
    width: 100%;
    border: 1px solid #ccc;
    margin-top: 4px;
    font-size: 0.95rem;
}

/* sweetalert2 */
