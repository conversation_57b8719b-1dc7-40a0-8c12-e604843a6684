import { mergeConfig } from 'vite';
import { defineConfig } from 'vitest/config';
import viteConfig from './vite.config.mts';

export default mergeConfig(
    viteConfig,
    defineConfig({
        define: {
            appServerRootUrl: {},
            appServerPathUrl: {},
            environVars: {},
            configPatch: {
                auth: {
                    externalProvider: {
                        enabled: false,
                        simulateExternalProviderUserIdHeader: false,
                        loginEnabled: false
                    }
                }
            }
        },
        test: {
            globals: true,
            coverage: {
                provider: 'istanbul'
            }
        }
    })
);
