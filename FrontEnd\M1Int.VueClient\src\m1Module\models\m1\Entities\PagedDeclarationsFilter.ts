import { DeclarationsFilter } from './DeclarationsFilter';
import { PagingParams } from './PagingParams';

export class PagedDeclarationsFilter {
    public declarationsFilter: DeclarationsFilter | null = new DeclarationsFilter();
    public pagingParams: PagingParams | null = new PagingParams();
    
    public constructor(options?: any) {
        options = options || {};

        if (options.declarationsFilter) {
            if (options.declarationsFilter instanceof DeclarationsFilter) this.declarationsFilter = options.declarationsFilter;
            else this.declarationsFilter = options.declarationsFilter.map((x: any) => new DeclarationsFilter(x));
        }

        if (options.pagingParams) {
            if (options.pagingParams instanceof PagingParams) this.pagingParams = options.pagingParams;
            else this.pagingParams = options.pagingParams.map((x: any) => new PagingParams(x));
        }
    }
}
