import { ResponseBase } from '@/common/models/ResponseBase';

describe('ResponseBase', () => {
    let response: ResponseBase<string>;

    beforeEach(() => {
        response = new ResponseBase<string>();
    });

    it('should be valid', () => {
        expect(response).toBeTruthy();
    });

    it('should have valid data', () => {
        expect(response.success).toEqual(false);
        expect(response.result).toBeFalsy();
        expect(response.message).toBeFalsy();
    });
});
