import { getNewUID } from '@/common/utilities';
import { PropType, defineComponent } from 'vue';
import FieldValidation from './FieldValidation';

export default defineComponent({
    name: 'field-common',
    mixins: [FieldValidation],
    props: {
        elementPrefixId: {
            type: String,
            default: null
        },
        elementSuffixId: {
            type: String,
            default: null
        },
        elementName: {
            type: String,
            default: null
        },
        type: {
            type: String,
            default: null
        },
        fieldValue: {
            type: Object,
            default: null
        },
        fieldClass: {
            type: String,
            default: null
        },
        modelObject: {
            type: Object as PropType<any>,
            default: null
        },
        autofocus: {
            type: Boolean,
            default: null
        },
        disabled: {
            type: Boolean,
            default: null
        },
        readonly: {
            type: Boolean,
            default: null
        },
        hideLabel: {
            type: Boolean,
            default: false
        },
        hideRequiredMark: {
            type: Boolean,
            default: false
        },
        placeholder: {
            type: String,
            default: null
        },
        hint: {
            type: String,
            default: null
        },
        hideHint: {
            type: Boolean,
            default: false
        },
        labelHint: {
            type: String,
            default: null
        },
        labelTooltip: {
            type: String,
            default: null
        },
        outlined: {
            type: Boolean,
            default: false
        },
        filled: {
            type: Boolean,
            default: false
        },
        borderless: {
            type: Boolean,
            default: false
        },
        rounded: {
            type: Boolean,
            default: false
        },
        bottomSlots: {
            type: Boolean,
            default: true
        },
        dense: {
            type: Boolean,
            default: false
        }
    },
    computed: {
        elementId(): string {
            let res: string = this.fieldId || `f_${getNewUID()}`;
            if (this.elementPrefixId) res = `${this.elementPrefixId}-${res}`;
            if (this.elementSuffixId) res = `${res}-${this.elementSuffixId}`;
            return res;
        },
        locale(): string {
            return this.$i18n?.locale || 'en';
        },
        requiredMark(): string {
            if (
                this.validationObject &&
                this.fieldId &&
                this.validationObject[this.fieldId] &&
                this.validationObject[this.fieldId].required !== null &&
                this.validationObject[this.fieldId].required !== undefined
            ) {
                return '*';
            } else {
                return '';
            }
        },
        label(): string | undefined {
            if (this.hideLabel) return undefined;
            let res: string | undefined = undefined;
            if (this.requiredMark && !this.hideRequiredMark) {
                res = this.fieldTitle ? `${this.fieldTitle} ${this.requiredMark}` : this.requiredMark;
            } else {
                res = this.fieldTitle || undefined;
            }
            return res;
        },
        labelInfo(): string {
            return this.labelTooltip || this.labelHint || '';
        },
        isLabelInfoTooltip(): boolean {
            return !!this.labelTooltip;
        },
        classResolved(): string {
            const res: string = `${this.fieldClass} ${this.validationClass}`;
            return res.trim();
        },
        isTextArea(): boolean {
            if (!this.type) return false;
            const typeLowered = this.type.toLowerCase();
            if (typeLowered === 'textarea' || typeLowered === 'text-area') return true;
            return false;
        },
        isNumeric(): boolean {
            if (!this.type) return false;
            const typeLowered = this.type.toLowerCase();
            if (typeLowered === 'number' || typeLowered === 'num' || typeLowered === 'numeric') return true;
            return false;
        },
        isDate(): boolean {
            if (!this.type) return false;
            const typeLowered = this.type.toLowerCase();
            if (
                typeLowered === 'date' ||
                typeLowered === 'dt-date' ||
                typeLowered === 'date-local' ||
                typeLowered === 'dateinput' ||
                typeLowered === 'date-input'
            )
                return true;
            return false;
        },
        isTime(): boolean {
            if (!this.type) return false;
            const typeLowered = this.type.toLowerCase();
            if (
                typeLowered === 'time' ||
                typeLowered === 'dt-time' ||
                typeLowered === 'time-local' ||
                typeLowered === 'timeinput' ||
                typeLowered === 'time-input'
            )
                return true;
            return false;
        },
        isDateTime(): boolean {
            if (!this.type) return false;
            const typeLowered = this.type.toLowerCase();
            if (
                typeLowered === 'datetime' ||
                typeLowered === 'date-time' ||
                typeLowered === 'dt-datetime' ||
                typeLowered === 'dt-date-time' ||
                typeLowered === 'datetime-local' ||
                typeLowered === 'date-time-local' ||
                typeLowered === 'datetimeinput' ||
                typeLowered === 'datetime-input' ||
                typeLowered === 'date-time-input'
            )
                return true;
            return false;
        },
        typeResolved():
            | 'number'
            | 'search'
            | 'text'
            | 'password'
            | 'textarea'
            | 'email'
            | 'tel'
            | 'file'
            | 'url'
            | 'time'
            | 'date' {
            if (!this.type) return 'text';
            if (
                this.type === 'number' ||
                this.type === 'search' ||
                this.type === 'text' ||
                this.type === 'password' ||
                this.type === 'textarea' ||
                this.type === 'email' ||
                this.type === 'tel' ||
                this.type === 'file' ||
                this.type === 'url' ||
                this.type === 'time' ||
                this.type === 'date'
            ) {
                return this.type;
            }
            return 'text';
        },
        hasValue(): boolean {
            const val: any = this.getValue();
            if (val === undefined || val === null) return false;
            return val.toString().length > 0;
        },
        canClear(): boolean {
            if (this.disabled || this.readonly) return false;
            return this.hasValue;
        }
    },
    methods: {
        getValue(): any {
            if (!this.modelObject || !this.fieldId) return this.modelObject;
            return this.modelObject[this.fieldId];
        },
        clearValue() {
            if (!this.modelObject || !this.fieldId) return;
            this.modelObject[this.fieldId] = null;
        },
        valueChanged() {
            if (this.disabled || this.readonly) return;
            this.processFieldValue();
            this.onFieldValidationChanged();
            this.$nextTick(this.fixTabItems);
            this.emitEvent('valueChanged');
        },
        processFieldValue() {
            if (!this.fieldId) return;
            const value: any = this.modelObject[this.fieldId];
            if (value === undefined || value === null || value?.toString().length <= 0) {
                if (this.modelObject[this.fieldId] !== null) {
                    if (Array.isArray(this.modelObject[this.fieldId])) {
                        this.modelObject[this.fieldId] = [];
                    } else {
                        this.modelObject[this.fieldId] = null;
                    }
                }
            }
        },
        fixTabItemsBySelector(selector: string) {
            if (!this.$el || !this.$el.querySelectorAll || !selector) return;
            const elems = this.$el.querySelectorAll(selector);
            if (!elems || elems.length <= 0) return;
            elems.forEach((x: any) => x.setAttribute('tabindex', '-1'));
        },
        fixTabItemsBySelectors(selectors: string[]) {
            if (!this.$el || !selectors || selectors.length <= 0) return;
            selectors.forEach((selector: string) => this.fixTabItemsBySelector(selector));
        },
        fixTabItems() {
            if (!this.$el) return;
            this.fixTabItemsBySelectors([
                '.q-field__before button',
                '.q-field__before q-icon',
                '.q-field__prepend button',
                '.q-field__prepend q-icon',
                '.q-field__append button',
                '.q-field__append q-icon'
            ]);
        },
        emitEvent(eventId: string, eventObject?: any) {
            this.$emit(eventId, eventObject);
        }
    },
    mounted() {
        this.fixTabItems();
    }
});
