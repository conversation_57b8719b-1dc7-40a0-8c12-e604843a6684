import { RequestBase } from '@/common/models/RequestBase';

export class ExportSubmittedDeclarationsToCsvRequest extends RequestBase {
    public applicantAfm: string | null = null;
    public declarationYear: number | null = null;
    public declarationId: number | null = null;
    public searchTerm: string | null = null;

    public constructor(options?: any) {
        super();
        options = options || {};
        this.applicantAfm = options.applicantAfm || null;
        this.declarationYear = options.declarationYear || null;
        this.declarationId = options.declarationId || null;
        this.searchTerm = options.searchTerm || null;
    }
}
