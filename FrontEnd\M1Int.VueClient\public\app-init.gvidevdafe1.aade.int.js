/*
This script is imported and executed during application initialization.
Supply values to the global variables below, in order to alter application configuration.
*/

// environment variables
var environVars = {
    applicationRoot: '/gvidevdafe1/M1Int/Apps/M1Int',
    useServerSession: true
};

// config patch
var configPatch = {
    auth: {
        externalProvider: {
            enabled: true,
            logoutUrl:
                'https://testlogin.ggps.gsis/oam/server/logout?end_url=https://test.ggps.gsis/gvidevdafe1/M1Int/Apps/M1Int',
            userIdHeaderKey: 'OAM_REMOTE_USER',
            simulateExternalProviderUserIdHeader: false,
            loginEnabled: false
        }
    },
    api: {
        serverUrl: 'https://test.ggps.gsis/gvidevdafe1',
        rootPath: 'M1Int/api'
    },
    application: {
        locales: {
            default: 'el',
            selectionEnabled: true
        }
    }
};
