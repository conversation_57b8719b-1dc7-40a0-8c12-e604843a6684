<template>
    <div class="view-declaration">
        <div
            class="full-width row justify-center items-center primary-dark view-declaration-header"
            style="padding: 18px 0"
        >
            <div class="q-px-sm text-h6 row justify-center items-center" style="font-weight: 500">
                <div>{{ title }}</div>
            </div>
        </div>
        <div
            v-if="declaration"
            class="full-width q-px-xs row wrap justify-center items-center view-declaration-content"
        >
            <div class="view-declaration-data-container">
                <declaration-tax-entity-data v-if="dataLoaded && declaration" :declaration="declaration" />
                <view-declaration-data
                    v-if="dataLoaded && declaration"
                    :declaration="declaration"
                    :relatedDeclaration="relatedDeclaration"
                />
            </div>
            <view-declaration-actions
                v-if="declaration"
                :declaration="declaration"
                @printDeclaration="printDeclaration(declaration)"
            />
        </div>
        <div v-if="dataLoaded && !declaration" class="full-width row q-px-xs q-py-lg declaration-not-found">
            <div class="col"></div>
            <div class="col-10 justify-center items-center">
                <div class="full-width text-h6 error--text column justify-center items-center">
                    <div class="full-width text-center">
                        {{ $t('declarationGetNotFound', [declarationId]) }}
                    </div>
                </div>
            </div>
            <div class="col"></div>
        </div>
    </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import DeclarationHelper from '../../components/m1/DeclarationHelper';
import { Declaration } from '../../models/m1/Entities/Declaration';
import DeclarationTaxEntityData from '../../components/m1/DeclarationTaxEntityData.vue';
import { GetDeclarationResponse } from '@/m1Module/models/m1/Services/GetDeclaration/GetDeclarationResponse';
import ViewDeclarationData from '../../components/m1/ViewDeclarationData.vue';
import ViewDeclarationActions from '../../components/m1/ViewDeclarationActions.vue';

export default defineComponent({
    name: 'view-declaration',
    mixins: [DeclarationHelper],
    components: {
        DeclarationTaxEntityData,
        ViewDeclarationData,
        ViewDeclarationActions
    },
    props: {
        declarationId: {
            type: String,
            default: ''
        }
    },
    data() {
        const dataObj: {
            dataLoaded: boolean;
            declaration: Declaration | null;
            relatedDeclaration: Declaration | null;
        } = {
            dataLoaded: false,
            declaration: null,
            relatedDeclaration: null
        };
        return dataObj;
    },
    computed: {
        title(): string {
            if (this.declaration) return `${this.$t('viewDeclaration')} ${this.declaration.id}`;
            else return this.$t('viewDeclaration');
        }
    },
    watch: {
        async declarationId() {
            this.dataLoaded = false;
            await this.loadData();
        }
    },
    methods: {
        goToHomePage() {
            this.$router?.push({ name: 'home' });
        },
        resumeValidDeclarationApplicant() {
            if (this.declaration) {
                this.goToHomePage();
            }
        },
        async loadData() {
            await this.commonStore?.getServerCurrentDateTime();
            this.declaration = null;
            this.relatedDeclaration = null;
            if (!this.declarationId) return;
            let response: GetDeclarationResponse = await this.store?.getDeclaration({
                id: this.declarationId
            });
            if (response?.success && response.result) {
                this.declaration = new Declaration(response.result);
                if (this.declaration.m1ReplacedBy) {
                    response = await this.store?.getDeclaration({
                        id: this.declaration.m1ReplacedBy
                    });
                    if (response?.success && response.result) {
                        this.relatedDeclaration = new Declaration(response.result);
                    }
                }
            }
            this.dataLoaded = true;
        }
    },
    async created() {
        await this.loadData();
    }
});
</script>

<style scoped>
.view-declaration-data-container {
    width: 100%;
    max-height: calc(100vh - 270px);
    overflow-y: auto;
}
.view-declaration-actions button,
.view-declaration-actions .q-btn {
    padding: 12px 18px;
    border-radius: 8px;
}
</style>
