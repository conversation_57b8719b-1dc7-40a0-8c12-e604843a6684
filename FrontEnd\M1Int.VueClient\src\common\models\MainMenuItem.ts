export interface IMainMenuItem {
    id: string;
    title: string;
    subtitle: string;
    icon: string;
    href: string | null;
    target: string | null;
    to: string | null;
    clickHandler: Function | null;
    class: string;
    titleClass: string;
    subtitleClass: string;
    disabled: boolean;
    ifAuthenticated: boolean;
    ifNotAuthenticated: boolean;
    disabledIfAuthenticated: boolean;
    disabledIfNotAuthenticated: boolean;
    items: Array<IMainMenuItem>;
}

export class MainMenuItem implements IMainMenuItem {
    public id: string = '';
    public title: string = '';
    public subtitle: string = '';
    public icon: string = '';
    public href: string | null = null;
    public target: string | null = null;
    public to: string | null = null;
    public clickHandler: Function | null = null;
    public class: string = '';
    public titleClass: string = '';
    public subtitleClass: string = '';
    public disabled: boolean = false;
    public ifAuthenticated: boolean = false;
    public ifNotAuthenticated: boolean = false;
    public disabledIfAuthenticated: boolean = false;
    public disabledIfNotAuthenticated: boolean = false;
    public items: Array<IMainMenuItem> = [];

    public constructor(options: any = null) {
        options = options || {};
        this.id = options.id || '';
        this.title = options.title || '';
        this.subtitle = options.subtitle || '';
        this.icon = options.icon || '';
        this.href = options.href || null;
        this.target = options.target || null;
        this.to = options.to || null;
        this.clickHandler = options.clickHandler || options.handler || null;
        this.class = options.class || '';
        this.titleClass = options.titleClass || '';
        this.subtitleClass = options.subtitleClass || '';
        this.disabled = options.disabled || false;
        this.ifAuthenticated = options.ifAuthenticated || false;
        this.ifNotAuthenticated = options.ifNotAuthenticated || false;
        this.disabledIfAuthenticated = options.disabledIfAuthenticated || false;
        this.disabledIfNotAuthenticated = options.disabledIfNotAuthenticated || false;
        this.items = [];
        if (options.items && options.items.length > 0) {
            options.items.forEach((x: any) => {
                let mi: MainMenuItem | null = null;
                if (x instanceof MainMenuItem) mi = x;
                else mi = new MainMenuItem(x);
                if (mi) this.items.push(mi);
            });
        }
    }
}
