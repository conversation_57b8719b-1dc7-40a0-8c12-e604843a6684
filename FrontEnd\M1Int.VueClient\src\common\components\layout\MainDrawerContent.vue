<template>
    <q-scroll-area class="fit main-drawer-content">
        <q-list>
            <q-item
                v-if="isUserAuthenticated"
                id="main-nav-user"
                class="full-width q-py-md row justify-between items-center primary text-white"
                style="width: 100%"
                role=""
            >
                <div style="width: 24px">
                    <q-item-section avatar>
                        <q-icon name="account_circle" size="md" class="text-white" />
                    </q-item-section>
                </div>
                <div class="col grow q-mx-md">
                    <div v-if="authUserName" class="q-mx-none" style="font-size: 0.95em" :aria-label="authUserName">
                        {{ authUserName }}
                    </div>
                    <div
                        v-if="authUserDepartment"
                        class="q-mx-none"
                        style="font-size: 0.85em"
                        :aria-label="authUserDepartment"
                    >
                        {{ authUserDepartment }}
                    </div>
                </div>
                <div style="width: 24px" @click="closeDrawer(true)">
                    <q-item-section avatar>
                        <q-icon name="chevron_left" size="md" class="text-white" />
                    </q-item-section>
                </div>
            </q-item>
            <q-separator key="sep-menu-item-header" />
            <main-menu-item-component
                v-for="menuItem in menuItems"
                :key="menuItem.id"
                :menuItem="menuItem"
                @click="menuItemClick(menuItem)"
            />
        </q-list>
    </q-scroll-area>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import type { PropType } from 'vue';
import M1Helper from '@/m1Module/components/M1Helper';
import { getCommonStore, ICommonStore } from '@/common/stores/Store';
import { getM1Store, IM1Store } from '@/m1Module/stores/M1Store';
import { IMainMenuItem, MainMenuItem } from '@/common/models/MainMenuItem';
import MainMenuItemComponent from './MainMenuItem.vue';

export default defineComponent({
    name: 'main-drawer-content',
    mixins: [M1Helper],
    components: { MainMenuItemComponent },
    props: {
        commonStore: { type: Object as PropType<ICommonStore>, default: () => getCommonStore() },
        m1Store: { type: Object as PropType<IM1Store>, default: () => getM1Store() }
    },
    emits: ['closeDrawer'],
    data() {
        const dataObj: any = {};
        return dataObj;
    },
    computed: {
        isUserAuthenticated(): boolean {
            return !!this.commonStore?.authData?.userName === true;
        },
        authUserName(): string {
            return this.commonStore?.authData?.userName || '';
        },
        authUserDepartment(): string {
            return this.commonStore?.authData?.userDepartment || '';
        },
        menuItems(): Array<IMainMenuItem> {
            const menuItems: Array<IMainMenuItem> = [];
            menuItems.push(new MainMenuItem({ id: 'main-nav-item-home', title: 'home', icon: 'home', to: '/' }));
            if (this.isUserAuthenticated) {
                menuItems.push(
                    new MainMenuItem({
                        id: 'main-nav-item-submitted-declarations',
                        title: 'submittedDeclarations',
                        icon: 'format_list_bulleted',
                        to: '/submittedDeclarations'
                    })
                );
            }
            menuItems.push(new MainMenuItem({ id: 'main-nav-item-about', title: 'about', icon: 'info', to: '/about' }));
            return menuItems;
        }
    },
    methods: {
        isMenuItemEnabled(item: any): boolean {
            if (!item) return false;
            if (!item.ifAuthenticated && !item.ifNotAuthenticated) return true;
            if (item.ifAuthenticated) return this.isUserAuthenticated;
            if (item.ifNotAuthenticated) return !this.isUserAuthenticated;
            return false;
        },
        closeDrawer(forceClose: boolean = false) {
            this.$emit('closeDrawer', forceClose);
        },
        menuItemClick(menuItem: IMainMenuItem) {
            if (menuItem?.items?.length) return;
            this.closeDrawer();
        }
    }
});
</script>

<style>
.main-drawer-content .q-item {
    cursor: pointer;
}
.main-drawer-content .menu-item-header {
    height: 70px;
    background-color: var(--palette-secondary) !important;
    color: white !important;
    font-size: 1.5em;
    font-weight: bold;
    text-align: center;
}
.main-drawer-content .q-item.q-router-link--active,
.main-drawer-content .q-item--active {
    /* color: #006ea1; */
    color: var(--palette-primary-medium);
}
.main-drawer-content .q-item__section--avatar {
    min-width: 40px;
}
.main-drawer-content .q-item .q-icon {
    font-weight: 100;
    color: #777777;
}
.main-drawer-content .q-item.q-router-link--active .q-icon {
    color: var(--palette-primary-medium);
}
</style>
