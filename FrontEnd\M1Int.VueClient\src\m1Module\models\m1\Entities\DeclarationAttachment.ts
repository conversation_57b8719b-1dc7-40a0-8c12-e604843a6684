export class DeclarationAttachment {
    public id?: number;
    public m1Id?: number;
    public fileName: string | null = null;
    public fileType: string | null = null;

    constructor(options?: any) {
        options = options || {};
        this.id = options.id || undefined;
        this.m1Id = options.m1Id || undefined;
        this.fileName = options.fileName || null;
        this.fileType = options.fileType || null;
    }

    public get title(): string | null {
        if (!this.fileName || this.fileName.trim() === '') {
            return '';
        }

        const firstPos = this.fileName.indexOf('_');
        if (firstPos < 0) {
            return this.fileName;
        }

        const secondPos = this.fileName.indexOf('_', firstPos + 1);
        if (secondPos < 0) {
            return this.fileName;
        }

        if ((secondPos + 7) >= this.fileName.length) {
            return this.fileName;
        }

        const result = this.fileName.substring(secondPos + 7);
        return result;
    }       

}
