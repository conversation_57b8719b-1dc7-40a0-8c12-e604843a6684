export class PagingParams {
    public sortBy?: string | null = null;
    public descending?: boolean = true;;

    public page?: number;
    public rowsPerPage?: number;

    public rowsNumber: number;

    public constructor(options?: any) {
        options = options || {};
        this.page = options.page ?? 1;
        this.rowsPerPage = options.rowsPerPage ?? 50;
        this.sortBy = options.sortBy || 'Id';
        // this.descending = !!options.descending;
        this.descending = options.descending ?? true;
        this.rowsNumber = options.rowsNumber ?? null;
    }
}
