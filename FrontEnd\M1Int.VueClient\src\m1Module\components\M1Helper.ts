import { defineComponent } from 'vue';
import type { PropType } from 'vue';
import { getM1Store, IM1Store } from '../stores/M1Store';
import { ICommonStore, getCommonStore } from '@/common/stores/Store';
import config from '@/config';

export default defineComponent({
    name: 'm1-helper',
    props: {
        commonStore: {
            type: Object as PropType<ICommonStore>,
            default: () => getCommonStore()
        },
        store: {
            type: Object as PropType<IM1Store>,
            default: () => getM1Store()
        }
    },
    computed: {
        zeroAfm(): string {
            return config.ZERO_AFM;
        }
    }
});
