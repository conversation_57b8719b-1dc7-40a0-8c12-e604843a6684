import dayjs from '@/plugins/dayjs';
import { DeclarationAttachment } from './DeclarationAttachment';
import { DeclarationEmail } from './DeclarationEmail';

export enum GenderEnum {
    Male = 1,
    Female = 2
}

export enum CountryEnum {
    Greek = 1,
    Abroad = 2
}
export enum TaxpayerStatusEnum {
    JudicialSupportYes = 1,
    JudicialSupportNo = 2
}
export enum SubmissionModeEnum {
    Self = 0,
    LegalRepresentative = 1,
    AuthorizedRepresentative = 2
}
export enum ServiceChannelTypeEnum {
    None = 0,
    NavigateToFAABooking = 1,           // Άμεση Βιντεοκλήση myAADElive
    NavigateToMyAADEliveBooking = 2,            // Ραντεβού myAADElive βιντεοκλήση
    NavigateToDoyBooking = 3,           // Ραντεβού ΔΟΥ φυσική παρουσία
    NavigateToTicketBooking = 4,			// Καταχώρηση αιτήματος
    NavigateToMyAADEliveSpecialBooking = 5				// Ραντεβού myAADElive με μετάφραση ή στην Νοηματική γλώσσα
}
export enum NotificationChoiceEnum {
    None = 0,                           // Δεν υπάρχει επιλογή κοινοποίησης
    TaxRepresentativeOnly = 1,          // Επιλογή κοινοποίησης ΜΟΝΟ στον φορολογικό εκπρόσωπο
    TaxRepresentativePlusSelf = 2,      // Επιλογή κοινοποίησης στον φορολογικό εκπρόσωπο ΚΑΙ στον φορολογούμενο (αν δεν υπάρχει δικαιοπρακτική ικανότητα στους νόμιμους εκπροσώπους)
    SelfOnly = 3,                       // Επιλογή κοινοποίησης ΜΟΝΟ στον φορολογούμενο (αν δεν υπάρχει δικαιοπρακτική ικανότητα στους νόμιμους εκπροσώπους)
}


export class RelatedTinData {
    public id?: number;

    public relAfm: string | null = null;
    public relIndicator: string | null = null;
    public relStartDate: Date | null = null;
    public relNames: string | null = null;
    public relCategory: number | null = null;
    public relKind: number | null = null;
    public relKindDescription: string | null = null;
    public relProofDoc: number | null = null;
    public relProofDocDescription: string | null = null;
    public relProofDocNo: string | null = null;
    public relProofDocDate: Date | null = null;
    public relProofDocAuthor: string | null = null;
    public relEmail: string | null = null;

    public constructor(options?: any) {
        options = options || {};
        this.id = options.id || undefined;

        this.relAfm = options.relAfm || null;
        this.relIndicator = options.relIndicator || null;
        if (options.relStartDate) this.relStartDate = dayjs(options.relStartDate).toDate();
        this.relNames = options.relNames || null;
        this.relCategory = options.relCategory || null;
        this.relKind = options.relKind || null;
        this.relKindDescription = options.relKindDescription || null;
        this.relProofDoc = options.relProofDoc || null;
        this.relProofDocDescription = options.relProofDocDescription || null;
        this.relProofDocNo = options.relProofDocNo || null;
        if (options.relProofDocDate) this.relProofDocDate = dayjs(options.relProofDocDate).toDate();
        this.relProofDocAuthor = options.relProofDocAuthor || null;
        this.relEmail = options.relEmail || null;
    }

    public get relStartDateText(): string {
        if (!this.relStartDate) return '';
        return dayjs(this.relStartDate).format('L');
    }

    public get relProofDocDateText(): string {
        if (!this.relProofDocDate) return '';
        return dayjs(this.relProofDocDate).format('L');
    }

}


export class Declaration {
    public id?: number;
    public m1Afm: string | null = null;
    public m1Sex: GenderEnum | null = null;

    public m1SurNameA: string | null = null;
    public m1SurNameB: string | null = null;
    public m1Name: string | null = null;
    public m1FatherSurname: string | null = null;
    public m1FatherName: string | null = null;
    public m1MotherSurname: string | null = null;
    public m1MotherName: string | null = null;

    public m1BirthDate: Date | null = null;
    public m1Country: CountryEnum | null = null;
    public m1BirthPlace: string | null = null;
    public m1BirthCountry: string | null = null;
    public m1BirthCountryDescription: string | null = null;

    public m1IdKind: number | null = null;
    public m1IdKindDescription: string | null = null;
    public m1IdNumber: string | null = null;
    public m1IdIsssueDate: Date | null = null;
    public m1IdEndDate: Date | null = null;
    public m1IdAuthority: string | null = null;

    public m1PermitKind: number | null = null;
    public m1PermitKindDescription: string | null = null;
    public m1PermitNumber: string | null = null;
    public m1PermitIssueDate: Date | null = null;
    public m1PermitEndDate: Date | null = null;
    public m1PermitAuthority: string | null = null;

    public m1MaritalStatus: number | null = null;
    public m1MaritalStatusDescription: string | null = null;
    public m1TaxpayerStatus: TaxpayerStatusEnum | null = null;
    public m1Citizenship: string | null = null;
    public m1ResidenseKind: CountryEnum | null = null;
    public m1ResidenseAbroad: string | null = null;
    public m1ResidenseAbroadDescription: string | null = null;
    public m1Street: string | null = null;
    public m1StreetNo: string | null = null;
    public m1PostCode: string | null = null;
    public m1PostCodeMunicipality: string | null = null;
    public m1PostCodePrefecture: string | null = null;
    public m1PhoneNumber: string | null = null;
    public m1Email: string | null = null;
    public m1Doy: string | null = null;
    public m1DoyDescription: string | null = null;

    public m1RelAfm: string | null = null;
    public m1RelIndicator: string | null = null;
    public m1RelStartDate: Date | null = null;
    public m1RelNames: string | null = null;
    public m1RelCategory: number | null = null;
    public m1RelKind: number | null = null;
    public m1RelKindDescription: string | null = null;
    public m1RelProofDoc: number | null = null;
    public m1RelProofDocDescription: string | null = null;
    public m1RelProofDocNo: string | null = null;
    public m1RelProofDocDate: Date | null = null;
    public m1RelProofDocAuthor: string | null = null;
    public m1RelEmail: string | null = null;

    public m1RelAfm2: string | null = null;
    public m1RelIndicator2: string | null = null;
    public m1RelStartDate2: Date | null = null;
    public m1RelNames2: string | null = null;
    public m1RelCategory2: number | null = null;
    public m1RelKind2: number | null = null;
    public m1RelKind2Description: string | null = null;
    public m1RelProofDoc2: number | null = null;
    public m1RelProofDoc2Description: string | null = null;
    public m1RelProofDocNo2: string | null = null;
    public m1RelProofDocDate2: Date | null = null;
    public m1RelProofDocAuthor2: string | null = null;
    public m1RelEmail2: string | null = null;

    public m1RelAfm3: string | null = null;
    public m1RelIndicator3: string | null = null;
    public m1RelStartDate3: Date | null = null;
    public m1RelNames3: string | null = null;
    public m1RelCategory3: number | null = null;
    public m1RelKind3: number | null = null;
    public m1RelKind3Description: string | null = null;
    public m1RelProofDoc3: number | null = null;
    public m1RelProofDoc3Description: string | null = null;
    public m1RelProofDocNo3: string | null = null;
    public m1RelProofDocDate3: Date | null = null;
    public m1RelProofDocAuthor3: string | null = null;
    public m1RelEmail3: string | null = null;

    public m1AfmAbroad: string | null = null;
    public m1Checkbox: boolean = false;
    public m1Created: Date | null = null;
    public m1UpdatedEmpl: Date | null = null;
    public m1ReplacedBy: number | null = null;

    public m1RelAfm4: string | null = null;
    public m1RelIndicator4: string | null = null;
    public m1RelStartDate4: Date | null = null;
    public m1RelNames4: string | null = null;
    public m1RelCategory4: number | null = null;
    public m1RelKind4: number | null = null;
    public m1RelKind4Description: string | null = null;
    public m1RelProofDoc4: number | null = null;
    public m1RelProofDoc4Description: string | null = null;
    public m1RelProofDocNo4: string | null = null;
    public m1RelProofDocDate4: Date | null = null;
    public m1RelProofDocAuthor4: string | null = null;
    public m1RelEmail4: string | null = null;

    public m1RelAfm5: string | null = null;
    public m1RelIndicator5: string | null = null;
    public m1RelStartDate5: Date | null = null;
    public m1RelNames5: string | null = null;
    public m1RelCategory5: number | null = null;
    public m1RelKind5: number | null = null;
    public m1RelKind5Description: string | null = null;
    public m1RelProofDoc5: number | null = null;
    public m1RelProofDoc5Description: string | null = null;
    public m1RelProofDocNo5: string | null = null;
    public m1RelProofDocDate5: Date | null = null;
    public m1RelProofDocAuthor5: string | null = null;
    public m1RelEmail5: string | null = null;

    public m1BirthPlaceCode: number | null = null;
    public m1IdAuthorityCode: number | null = null;
    public m1PermitAuthorityCode: number | null = null;
    public m1CitizenshipCode: string | null = null;
    public m1SubmissionMode: SubmissionModeEnum | null = null;
    public m1RepresentativeUsername: string | null = null;
    public m1RepresentativeAfm: string | null = null;
    public m1RepresentativeName: string | null = null;
    public m1RepresentativeEmail: string | null = null;
    public m1RepresentativeMobile: string | null = null;
    public m1RepresentativePhone: string | null = null;
    public m1RepresentativeSendEmail: boolean = false;
    public m1PreferredUsername: string | null = null;
    public m1ApplicationEmailConfirmed: boolean = false;
    public m1CreateAuthenticationkey: boolean = false;

    public m1RelAfm8: string | null = null;
    public m1RelIndicator8: string | null = null;
    public m1RelStartDate8: Date | null = null;
    public m1RelNames8: string | null = null;
    public m1RelCategory8: number | null = null;
    public m1RelKind8: number | null = null;
    public m1RelKind8Description: string | null = null;
    public m1RelProofDoc8: number | null = null;
    public m1RelProofDoc8Description: string | null = null;
    public m1RelProofDocNo8: string | null = null;
    public m1RelProofDocDate8: Date | null = null;
    public m1RelProofDocAuthor8: string | null = null;
    public m1RelEmail8: string | null = null;

    public m1ServiceChannelType: ServiceChannelTypeEnum | null = null;

    public m1ServiceChannelId: string | null = null;
    public m1EmployeeUserInfo: string | null = null;
    public m1LanguageInfo: string | null = null;
    public m1PostcodeAbroad: string | null = null;
    public m1NotificationChoice: NotificationChoiceEnum | null = null;

    public attachments: Array<DeclarationAttachment> = [];
    public emails: Array<DeclarationEmail> = [];

    public constructor(options?: any) {
        options = options || {};
        this.id = options.id || undefined;
        this.m1Afm = options.m1Afm || null;
        this.m1Sex = options.m1Sex || null;

        this.m1SurNameA = options.m1SurNameA || null;
        this.m1SurNameB = options.m1SurNameB || null;
        this.m1Name = options.m1Name || null;
        this.m1FatherSurname = options.m1FatherSurname || null;
        this.m1FatherName = options.m1FatherName || null;
        this.m1MotherSurname = options.m1MotherSurname || null;
        this.m1MotherName = options.m1MotherName || null;

        if (options.m1BirthDate) this.m1BirthDate = dayjs(options.m1BirthDate).toDate();
        this.m1Country = options.m1Country || null;
        this.m1BirthPlace = options.m1BirthPlace || null;
        this.m1BirthCountry = options.m1BirthCountry || null;
        this.m1BirthCountryDescription = options.m1BirthCountryDescription || null;

        this.m1IdKind = options.m1IdKind || null;
        this.m1IdKindDescription = options.m1IdKindDescription || null;
        this.m1IdNumber = options.m1IdNumber || null;
        if (options.m1IdIsssueDate) this.m1IdIsssueDate = dayjs(options.m1IdIsssueDate).toDate();
        if (options.m1IdEndDate) this.m1IdEndDate = dayjs(options.m1IdEndDate).toDate();
        this.m1IdAuthority = options.m1IdAuthority || null;

        this.m1PermitKind = options.m1PermitKind || null;
        this.m1PermitKindDescription = options.m1PermitKindDescription || null;
        this.m1PermitNumber = options.m1PermitNumber || null;
        if (options.m1PermitIssueDate) this.m1PermitIssueDate = dayjs(options.m1PermitIssueDate).toDate();
        if (options.m1PermitEndDate) this.m1PermitEndDate = dayjs(options.m1PermitEndDate).toDate();
        this.m1PermitAuthority = options.m1PermitAuthority || null;

        this.m1MaritalStatus = options.m1MaritalStatus || null;
        this.m1MaritalStatusDescription = options.m1MaritalStatusDescription || null;
        this.m1TaxpayerStatus = options.m1TaxpayerStatus || null;
        this.m1Citizenship = options.m1Citizenship || null;
        this.m1ResidenseKind = options.m1ResidenseKind || null;
        this.m1ResidenseAbroad = options.m1ResidenseAbroad || null;
        this.m1ResidenseAbroadDescription = options.m1ResidenseAbroadDescription || null;
        this.m1Street = options.m1Street || null;
        this.m1StreetNo = options.m1StreetNo || null;
        this.m1PostCode = options.m1PostCode || null;
        this.m1PostCodeMunicipality = options.m1PostCodeMunicipality || null;
        this.m1PostCodePrefecture = options.m1PostCodePrefecture || null;
        this.m1PhoneNumber = options.m1PhoneNumber || null;
        this.m1Email = options.m1Email || null;
        this.m1Doy = options.m1Doy || null;
        this.m1DoyDescription = options.m1DoyDescription || null;

        this.m1RelAfm = options.m1RelAfm || null;
        this.m1RelIndicator = options.m1RelIndicator || null;
        if (options.m1RelStartDate) this.m1RelStartDate = dayjs(options.m1RelStartDate).toDate();
        this.m1RelNames = options.m1RelNames || null;
        this.m1RelCategory = options.m1RelCategory || null;
        this.m1RelKind = options.m1RelKind || null;
        this.m1RelKindDescription = options.m1RelKindDescription || null;
        this.m1RelProofDoc = options.m1RelProofDoc || null;
        this.m1RelProofDocDescription = options.m1RelProofDocDescription || null;
        this.m1RelProofDocNo = options.m1RelProofDocNo || null;
        if (options.m1RelProofDocDate) this.m1RelProofDocDate = dayjs(options.m1RelProofDocDate).toDate();
        this.m1RelProofDocAuthor = options.m1RelProofDocAuthor || null;
        this.m1RelEmail = options.m1RelEmail || null;

        this.m1RelAfm2 = options.m1RelAfm2 || null;
        this.m1RelIndicator2 = options.m1RelIndicator2 || null;
        if (options.m1RelStartDate2) this.m1RelStartDate2 = dayjs(options.m1RelStartDate2).toDate();
        this.m1RelNames2 = options.m1RelNames2 || null;
        this.m1RelCategory2 = options.m1RelCategory2 || null;
        this.m1RelKind2 = options.m1RelKind2 || null;
        this.m1RelKind2Description = options.m1RelKind2Description || null;
        this.m1RelProofDoc2 = options.m1RelProofDoc2 || null;
        this.m1RelProofDoc2Description = options.m1RelProofDoc2Description || null;
        this.m1RelProofDocNo2 = options.m1RelProofDocNo2 || null;
        if (options.m1RelProofDocDate2) this.m1RelProofDocDate2 = dayjs(options.m1RelProofDocDate2).toDate();
        this.m1RelProofDocAuthor2 = options.m1RelProofDocAuthor2 || null;
        this.m1RelEmail2 = options.m1RelEmail2 || null;

        this.m1RelAfm3 = options.m1RelAfm3 || null;
        this.m1RelIndicator3 = options.m1RelIndicator3 || null;
        if (options.m1RelStartDate3) this.m1RelStartDate3 = dayjs(options.m1RelStartDate3).toDate();
        this.m1RelNames3 = options.m1RelNames3 || null;
        this.m1RelCategory3 = options.m1RelCategory3 || null;
        this.m1RelKind3 = options.m1RelKind3 || null;
        this.m1RelKind3Description = options.m1RelKind3Description || null;
        this.m1RelProofDoc3 = options.m1RelProofDoc3 || null;
        this.m1RelProofDoc3Description = options.m1RelProofDoc3Description || null;
        this.m1RelProofDocNo3 = options.m1RelProofDocNo3 || null;
        if (options.m1RelProofDocDate3) this.m1RelProofDocDate3 = dayjs(options.m1RelProofDocDate3).toDate();
        this.m1RelProofDocAuthor3 = options.m1RelProofDocAuthor3 || null;
        this.m1RelEmail3 = options.m1RelEmail3 || null;

        this.m1AfmAbroad = options.m1AfmAbroad || null;
        this.m1Checkbox = !!options.m1Checkbox;
        if (options.m1Created) this.m1Created = dayjs(options.m1Created).toDate();
        if (options.m1UpdatedEmpl) this.m1UpdatedEmpl = dayjs(options.m1UpdatedEmpl).toDate();
        this.m1ReplacedBy = options.m1ReplacedBy || null;

        this.m1RelAfm4 = options.m1RelAfm4 || null;
        this.m1RelIndicator4 = options.m1RelIndicator4 || null;
        if (options.m1RelStartDate4) this.m1RelStartDate4 = dayjs(options.m1RelStartDate4).toDate();
        this.m1RelNames4 = options.m1RelNames4 || null;
        this.m1RelCategory4 = options.m1RelCategory4 || null;
        this.m1RelKind4 = options.m1RelKind4 || null;
        this.m1RelKind4Description = options.m1RelKind4Description || null;
        this.m1RelProofDoc4 = options.m1RelProofDoc4 || null;
        this.m1RelProofDoc4Description = options.m1RelProofDoc4Description || null;
        this.m1RelProofDocNo4 = options.m1RelProofDocNo4 || null;
        if (options.m1RelProofDocDate4) this.m1RelProofDocDate4 = dayjs(options.m1RelProofDocDate4).toDate();
        this.m1RelProofDocAuthor4 = options.m1RelProofDocAuthor4 || null;
        this.m1RelEmail4 = options.m1RelEmail4 || null;

        this.m1RelAfm5 = options.m1RelAfm5 || null;
        this.m1RelIndicator5 = options.m1RelIndicator5 || null;
        if (options.m1RelStartDate5) this.m1RelStartDate5 = dayjs(options.m1RelStartDate5).toDate();
        this.m1RelNames5 = options.m1RelNames5 || null;
        this.m1RelCategory5 = options.m1RelCategory5 || null;
        this.m1RelKind5 = options.m1RelKind5 || null;
        this.m1RelKind5Description = options.m1RelKind5Description || null;
        this.m1RelProofDoc5 = options.m1RelProofDoc5 || null;
        this.m1RelProofDoc5Description = options.m1RelProofDoc5Description || null;
        this.m1RelProofDocNo5 = options.m1RelProofDocNo5 || null;
        if (options.m1RelProofDocDate5) this.m1RelProofDocDate5 = dayjs(options.m1RelProofDocDate5).toDate();
        this.m1RelProofDocAuthor5 = options.m1RelProofDocAuthor5 || null;
        this.m1RelEmail5 = options.m1RelEmail5 || null;

        this.m1BirthPlaceCode = options.m1BirthPlaceCode || null;
        this.m1IdAuthorityCode = options.m1IdAuthorityCode || null;
        this.m1PermitAuthorityCode = options.m1PermitAuthorityCode || null;
        this.m1CitizenshipCode = options.m1CitizenshipCode || null;
        this.m1SubmissionMode = options.m1SubmissionMode;  
        this.m1RepresentativeUsername = options.m1RepresentativeUsername || null;
        this.m1RepresentativeAfm = options.m1RepresentativeAfm || null;
        this.m1RepresentativeName = options.m1RepresentativeName || null;
        this.m1RepresentativeEmail = options.m1RepresentativeEmail || null;
        this.m1RepresentativeMobile = options.m1RepresentativeMobile || null;
        this.m1RepresentativePhone = options.m1RepresentativePhone || null;
        this.m1RepresentativeSendEmail = !!options.m1RepresentativeSendEmail;
        this.m1PreferredUsername = options.m1PreferredUsername || null;
        this.m1ApplicationEmailConfirmed = !!options.m1ApplicationEmailConfirmed;
        this.m1CreateAuthenticationkey = !!options.m1CreateAuthenticationkey;

        this.m1RelAfm8 = options.m1RelAfm8 || null;
        this.m1RelIndicator8 = options.m1RelIndicator8 || null;
        if (options.m1RelStartDate8) this.m1RelStartDate8 = dayjs(options.m1RelStartDate8).toDate();
        this.m1RelNames8 = options.m1RelNames8 || null;
        this.m1RelCategory8 = options.m1RelCategory8 || null;
        this.m1RelKind8 = options.m1RelKind8 || null;
        this.m1RelKind8Description = options.m1RelKind8Description || null;
        this.m1RelProofDoc8 = options.m1RelProofDoc8 || null;
        this.m1RelProofDoc8Description = options.m1RelProofDoc8Description || null;
        this.m1RelProofDocNo8 = options.m1RelProofDocNo8 || null;
        if (options.m1RelProofDocDate8) this.m1RelProofDocDate8 = dayjs(options.m1RelProofDocDate8).toDate();
        this.m1RelProofDocAuthor8 = options.m1RelProofDocAuthor8 || null;
        this.m1RelEmail8 = options.m1RelEmail8 || null;

        this.m1ServiceChannelType = options.m1ServiceChannelType || null;

        this.m1ServiceChannelId = options.m1ServiceChannelId || null;
        this.m1EmployeeUserInfo = options.m1EmployeeUserInfo || null;
        this.m1LanguageInfo = options.m1LanguageInfo || null;
        this.m1PostcodeAbroad = options.m1PostcodeAbroad || null;
        this.m1NotificationChoice = options.m1NotificationChoice; // SOS AGX || null;

        if (options.attachments?.length) {
            if (options.attachments[0] instanceof DeclarationAttachment) this.attachments = options.attachments;
            else this.attachments = options.attachments.map((x: any) => new DeclarationAttachment(x));
        }

        if (options.emails?.length) {
            if (options.emails[0] instanceof DeclarationEmail) this.emails = options.emails;
            else this.emails = options.emails.map((x: any) => new DeclarationEmail(x));
        }
    }

    public get isNew(): boolean {
        return !this.id;
    }

    public get m1BirthDateText(): string {
        if (!this.m1BirthDate) return '';
        return dayjs(this.m1BirthDate).format('L');
    }

    public get m1IdIsssueDateText(): string {
        if (!this.m1IdIsssueDate) return '';
        return dayjs(this.m1IdIsssueDate).format('L');
    }

    public get m1IdEndDateText(): string {
        if (!this.m1IdEndDate) return '';
        return dayjs(this.m1IdEndDate).format('L');
    }

    public get m1PermitIssueDateText(): string {
        if (!this.m1PermitIssueDate) return '';
        return dayjs(this.m1PermitIssueDate).format('L');
    }

    public get m1PermitEndDateText(): string {
        if (!this.m1PermitEndDate) return '';
        return dayjs(this.m1PermitEndDate).format('L');
    }

    public get m1RelStartDateText(): string {
        if (!this.m1RelStartDate) return '';
        return dayjs(this.m1RelStartDate).format('L');
    }

    public get m1RelProofDocDateText(): string {
        if (!this.m1RelProofDocDate) return '';
        return dayjs(this.m1RelProofDocDate).format('L');
    }

    public get m1RelStartDate2Text(): string {
        if (!this.m1RelStartDate2) return '';
        return dayjs(this.m1RelStartDate2).format('L');
    }


    public get m1RelProofDocDate2Text(): string {
        if (!this.m1RelProofDocDate2) return '';
        return dayjs(this.m1RelProofDocDate2).format('L');
    }

    public get m1RelStartDate3Text(): string {
        if (!this.m1RelStartDate3) return '';
        return dayjs(this.m1RelStartDate3).format('L');
    }

    public get m1RelProofDocDate3Text(): string {
        if (!this.m1RelProofDocDate3) return '';
        return dayjs(this.m1RelProofDocDate3).format('L');
    }


    public get m1RelStartDate4Text(): string {
        if (!this.m1RelStartDate4) return '';
        return dayjs(this.m1RelStartDate4).format('L');
    }

    public get m1RelProofDocDate4Text(): string {
        if (!this.m1RelProofDocDate4) return '';
        return dayjs(this.m1RelProofDocDate4).format('L');
    }

    public get m1RelStartDate5Text(): string {
        if (!this.m1RelStartDate5) return '';
        return dayjs(this.m1RelStartDate5).format('L');
    }

    public get m1RelProofDocDate5Text(): string {
        if (!this.m1RelProofDocDate5) return '';
        return dayjs(this.m1RelProofDocDate5).format('L');
    }

    public get m1RelStartDate8Text(): string {
        if (!this.m1RelStartDate8) return '';
        return dayjs(this.m1RelStartDate8).format('L');
    }

    public get m1RelProofDocDate8Text(): string {
        if (!this.m1RelProofDocDate8) return '';
        return dayjs(this.m1RelProofDocDate8).format('L');
    }

    public get m1CreatedText(): string {
        // if (!this.m1Created) return '';
        // return dayjs(this.m1Created).format('L');
        const dt: Date | null = this.m1Created;
        if (!dt) return '';
        const dtjs = dayjs(dt);
        return `${dtjs.format('L')} ${dtjs.format('HH:mm:ss')}`;
    }

    public get m1UpdatedEmplText(): string {
        // if (!this.m1UpdatedEmpl) return '';
        // return dayjs(this.m1UpdatedEmpl).format('L');
        const dt: Date | null = this.m1UpdatedEmpl;
        if (!dt) return '';
        const dtjs = dayjs(dt);
        return `${dtjs.format('L')} ${dtjs.format('HH:mm:ss')}`;
    }

    public get m1SexText(): string {
        if (this.m1Sex === undefined || this.m1Sex === null) return 'Undefined';
        return GenderEnum[this.m1Sex];
    }

    public get m1CountryText(): string {
        if (this.m1Country === undefined || this.m1Country === null) return 'Undefined';
        return CountryEnum[this.m1Country];
    }

    public get m1ResidenseKindText(): string {
        if (this.m1ResidenseKind === undefined || this.m1ResidenseKind === null) return 'Undefined';
        return CountryEnum[this.m1ResidenseKind];
    }

    public get m1TaxpayerStatusText(): string {
        if (this.m1TaxpayerStatus === undefined || this.m1TaxpayerStatus === null) return 'Undefined';
        return TaxpayerStatusEnum[this.m1TaxpayerStatus];
    }

    public get m1SubmissionModeText(): string {
        if (this.m1SubmissionMode === undefined || this.m1SubmissionMode === null) return 'Undefined';
        return SubmissionModeEnum[this.m1SubmissionMode];
    }

    public get m1NotificationChoiceText(): string {
        if (this.m1NotificationChoice === undefined || this.m1NotificationChoice === null) return 'Undefined';
        return NotificationChoiceEnum[this.m1NotificationChoice];
    }

    public get m1ServiceChannelTypeText(): string {
        if (this.m1ServiceChannelType === undefined || this.m1ServiceChannelType === null) return 'Undefined';
        return NotificationChoiceEnum[this.m1ServiceChannelType];
    }

    public get submittedByApplicant(): boolean {
        return this.m1SubmissionMode === SubmissionModeEnum.Self;
    }

    public get submittedByRepresentative(): boolean {
        return this.m1SubmissionMode === SubmissionModeEnum.LegalRepresentative ||
            this.m1SubmissionMode === SubmissionModeEnum.AuthorizedRepresentative;
    }

    public get submittedByAuthorizedRepresentative(): boolean {
        return this.m1SubmissionMode === SubmissionModeEnum.AuthorizedRepresentative;
    }

    public get applicantName(): string {
        let res: string = this.m1SurNameA || '';
        if (this.m1SurNameB) {
            if (res) res += ' ';
            res += this.m1SurNameB;
        }
        if (this.m1Name) {
            if (res) res += ' ';
            res += this.m1Name;
        }
        return res;
    }

    public get applicantDescription(): string {
        let res: string = this.applicantName;
        if (this.m1Afm) res += ` (${this.m1Afm})`;
        return res;
    }

    public get m1FullDoyDescription(): string {
        let res: string = this.m1Doy || '';
        if (this.m1DoyDescription) res += ` (${this.m1DoyDescription})`;
        return res;
    }

    public get representativeName(): string {
        const res: string = this.m1RepresentativeName || '';
        return res;
    }

    public get representativeDescription(): string {
        let res: string = this.representativeName;
        if (this.m1RepresentativeAfm) res += ` (${this.m1RepresentativeAfm})`;
        return res;
    }

    public get submitterName(): string {
        if (this.submittedByApplicant) {
            return this.applicantName;
        }
        if (this.submittedByRepresentative) {
            return this.representativeName;
        }
        return '';
    }

    public get submitterAfm(): string {
        if (this.submittedByApplicant) {
            return this.m1Afm || '';
        }
        if (this.submittedByRepresentative) {
            return this.m1RepresentativeAfm || '';
        }
        return '';
    }

    public get submitterDescription(): string | null {
        if (this.submittedByApplicant) {
            return this.applicantDescription;
        }
        if (this.submittedByRepresentative) {
            return this.representativeDescription;
        }
        return null;
    }

    public get submitterEmail(): string | null {
        if (this.submittedByApplicant) {
            return this.m1Email;
        }
        if (this.submittedByRepresentative) {
            return this.m1RepresentativeEmail;
        }
        return null;
    }

    public get submitterTelephone(): string | null {
        if (this.submittedByApplicant) {
            return this.m1PhoneNumber;
        }
        if (this.submittedByRepresentative) {
            let res: string = this.m1RepresentativePhone || '';
            if (this.m1RepresentativeMobile) res += ` - ${this.m1RepresentativeMobile}`;
            return res;
        }
        return null;
    }

    public get postCode(): string | null {
        if (this.m1ResidenseKind === CountryEnum.Abroad) {
            return this.m1PostcodeAbroad;
        }
        return this.m1PostCode;
    }

    public get municipality(): string | null {
        if (this.m1ResidenseKind === CountryEnum.Greek) {
            return '';
        }
        return null;
    }

    public get prefecture(): string | null {
        if (this.m1ResidenseKind === CountryEnum.Greek) {
            return '';
        }
        return null;
    }

    public get contactDescription(): string {
        let res: string = '';
        if (this.m1Street) {
            if (res) res += ' ';
            res += this.m1Street;
        }
        if (this.m1StreetNo) {
            if (res) res += ' ';
            res += this.m1StreetNo;
        }
        if (this.postCode) {
            if (res) res += ' ';
            res += this.postCode;
        }
        if (this.m1ResidenseAbroad) {
            if (this.m1ResidenseAbroadDescription) {
                if (res) res += ' ';
                res += this.m1ResidenseAbroadDescription;
            }
            if (this.m1AfmAbroad) {
                if (res) res += ' ';
                res += this.m1AfmAbroad;
            }
        }
        else {
            if (this.m1PostCodeMunicipality) {
                if (res) res += ' ';
                res += this.m1PostCodeMunicipality;
            }
            if (this.m1PostCodePrefecture) {
                if (res) res += ' ';
                res += this.m1PostCodePrefecture;
            }
        }
        if (this.m1PhoneNumber) {
            if (res) res += ' - ';
            res += this.m1PhoneNumber;
        }
        if (this.m1Email) {
            if (res) res += ' - ';
            res += this.m1Email;
        }
        if (this.m1FullDoyDescription) {
            if (res) res += ' - ';
            res += this.m1FullDoyDescription;
        }
        return res;
    }

    public get relatedTinsData(): Array<RelatedTinData> | [] {
        const data = [];
        for (let i = 1; i <= 5; i++) {
            const nameSuffix = i === 1 ? '' : i.toString();
            const relAfm = this[`m1RelAfm${nameSuffix}` as keyof typeof this] as string | null;

            // Only add rows that have relAfm
            if (relAfm) {
                const relIndicator = this[`m1RelIndicator${nameSuffix}` as keyof typeof this] as string | null;
                const relStartDate = this[`m1RelStartDate${nameSuffix}` as keyof typeof this] as Date | null;
                const relStartDateText = this[`m1RelStartDate${nameSuffix}Text` as keyof typeof this] as string;
                const relNames = this[`m1RelNames${nameSuffix}` as keyof typeof this] as string | null;
                const relCategory = this[`m1RelCategory${nameSuffix}` as keyof typeof this] as number | null;
                const relKind = this[`m1RelKind${nameSuffix}` as keyof typeof this] as number | null;
                const relKindDescription = this[`m1RelKind${nameSuffix}Description` as keyof typeof this] as string | null;
                const relProofDoc = this[`m1RelProofDoc${nameSuffix}` as keyof typeof this] as number | null;
                const relProofDocDescription = this[`m1RelProofDoc${nameSuffix}Description` as keyof typeof this] as string | null;
                const relProofDocNo = this[`m1RelProofDocNo${nameSuffix}` as keyof typeof this] as string | null;
                const relProofDocDate = this[`m1RelProofDocDate${nameSuffix}` as keyof typeof this] as Date | null;
                const relProofDocDateText = this[`m1RelProofDocDate${nameSuffix}Text` as keyof typeof this] as string;
                const relProofDocAuthor = this[`m1RelProofDocAuthor${nameSuffix}` as keyof typeof this] as string | null;
                const relEmail = this[`m1RelEmail${nameSuffix}` as keyof typeof this] as string | null;

                data.push(new RelatedTinData({
                    id: i,
                    relAfm,
                    relIndicator,
                    relStartDate,
                    relStartDateText,
                    relNames,
                    relCategory,
                    relKind,
                    relKindDescription,
                    relProofDoc,
                    relProofDocDescription,
                    relProofDocNo,
                    relProofDocDate,
                    relProofDocDateText,
                    relProofDocAuthor,
                    relEmail
                }));
            }
        }

        return data;
    };

    public get relatedTinsDescription(): string {
        let res: string = '';
        if (this.relatedTinsData?.length > 0) {
            res = this.relatedTinsData
                .map(item => `${item.relAfm} (${item.relKindDescription})`)
                .join(' - ');
        }
        return res;
    }

    public get attachmentsDescription(): string {
        let res: string = '';
        if (this.attachments?.length > 0) {
            res = this.attachments
                .map(item => `${item.title}`)
                .join(', ');
        }
        return res;
    }

    public get emailsDescription(): string {
        let res: string = '';
        if (this.emails?.length > 0) {
            res = this.emails
                .map(item => `${item.emailTo}`)
                .join(', ');
        }
        return res;
    }

}
