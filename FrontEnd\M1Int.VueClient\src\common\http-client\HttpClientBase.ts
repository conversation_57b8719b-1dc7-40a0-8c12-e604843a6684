import { IHttpClient } from './IHttpClient';

export abstract class HttpClientBase implements IHttpClient {
    constructor(public readonly baseUrl: string) {}

    protected getOptions(options?: any): any {
        options = options || {};
        if (options.url) {
            options.url = `${this.baseUrl}/${options.url}`;
        }
        options.headers = options.headers || {};
        if (!options.headers['Content-Type']) {
            options.headers['Content-Type'] = 'application/json; charset=utf-8';
        }
        return options;
    }

    protected getUrlAndOptions(url?: string, options?: any): any {
        options = options || {};
        if (url) {
            options.url = url;
        }
        options = this.getOptions(options);
        return options;
    }

    public getDataObjectFromModel(model: any): any {
        return JSON.stringify(model);
    }

    public getFormDataFromModel(model: any, objectKey?: string): any {
        const formData = new FormData();
        let excludeModelFiles = false;
        if (model && model.files && model.files.length > 0) {
            excludeModelFiles = true;
            model.files.forEach((f: any) => {
                formData.append('files', f);
            });
        }
        if (objectKey) {
            if (excludeModelFiles) delete model.files;
            const data = this.getDataObjectFromModel(model).toString();
            formData.append(objectKey, data);
        } else {
            Object.keys(model).forEach((x) => {
                if (x && model[x] && (x !== 'files' || !excludeModelFiles)) {
                    const data = model[x].toString();
                    formData.append(x, data);
                }
            });
        }
        return formData;
    }

    public getDataFromModel(model: any, options: any): any {
        let handleFormData = false;
        if (options && options.headers) {
            const contentType: string = options.headers['Content-Type'];
            if (contentType && contentType.toLowerCase().includes('form-data')) {
                handleFormData = true;
            }
        }
        if (handleFormData) {
            return this.getFormDataFromModel(model, options?.objectKey);
        } else {
            if (options) {
                options.headers = options.headers || {};
                if (!options.headers['Content-Type']) {
                    options.headers['Content-Type'] = 'application/json; charset=utf-8';
                }
            }
            return this.getDataObjectFromModel(model);
        }
    }

    public abstract request<T>(options: any): Promise<T>;

    public abstract get<T>(url: string, options?: any): Promise<T>;

    public abstract post<T, U>(url: string, data: U, options?: any): Promise<T>;

    public abstract delete<T>(url: string, options?: any): Promise<T>;

    public abstract put<T, U>(url: string, data: U, options?: any): Promise<T>;
}
