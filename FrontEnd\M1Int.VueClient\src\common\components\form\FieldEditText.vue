<template>
    <q-input
        :id="elementId"
        :for="elementId"
        :name="elementName"
        v-model="modelObject[fieldId]"
        :error-message="validationMessage"
        :error="isDirty && isInvalid"
        :noErrorIcon="true"
        :type="typeResolved"
        :class="classResolved"
        :label="label"
        :placeholder="placeholder"
        :hint="hint"
        :hideHint="hideHint"
        :mask="mask"
        :fillMask="fillMask"
        :maxlength="maxLength"
        :autogrow="autogrow"
        :outlined="outlined"
        :filled="filled"
        :borderless="borderless"
        :rounded="rounded"
        :bottomSlots="bottomSlots"
        :hideBottomSpace="!bottomSlots"
        :dense="dense"
        :clearable="clearable && !disabled && !readonly"
        clear-icon="close"
        :disable="disabled"
        :readonly="readonly"
        :autofocus="autofocus && !disabled && !readonly"
        @update:model-value="valueChanged"
    >
        <template v-slot:label>
            <field-label :label="label" :hint="labelInfo" :isTooltip="isLabelInfoTooltip" />
        </template>
        <template v-slot:before v-if="beforeIcon">
            <q-icon :name="beforeIcon" @click="emitEvent('click:before')">
                <q-tooltip v-if="beforeIconTooltip" anchor="top middle" self="center middle">
                    {{ beforeIconTooltip }}
                </q-tooltip>
            </q-icon>
        </template>
        <template v-slot:after v-if="afterIcon">
            <q-icon :name="afterIcon" @click="emitEvent('click:after')">
                <q-tooltip v-if="afterIconTooltip" anchor="top middle" self="center middle">
                    {{ afterIconTooltip }}
                </q-tooltip>
            </q-icon>
        </template>
        <template v-slot:prepend v-if="prependIcon">
            <q-icon :name="prependIcon" @click="emitEvent('click:prepend')">
                <q-tooltip v-if="prependIconTooltip" anchor="top middle" self="center middle">
                    {{ prependIconTooltip }}
                </q-tooltip>
            </q-icon>
        </template>
        <template v-slot:append v-if="appendIcon">
            <q-icon :name="appendIcon" @click="emitEvent('click:append')">
                <q-tooltip v-if="appendIconTooltip" anchor="top middle" self="center middle">
                    {{ appendIconTooltip }}
                </q-tooltip>
            </q-icon>
        </template>
    </q-input>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import FieldEditTextCommon from './FieldEditTextCommon';

export default defineComponent({
    name: 'field-edit-text',
    mixins: [FieldEditTextCommon]
});
</script>
