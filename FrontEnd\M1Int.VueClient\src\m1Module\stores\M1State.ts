import { DeclarationLite } from '../models/m1/Entities/DeclarationLite';
import { PagedDeclarationsFilter } from '../models/m1/Entities/PagedDeclarationsFilter';
import { DeclarationsOptions } from '../models/m1/Entities/DeclarationsOptions';
import { GetSubmittedDeclarationsLiteRequest } from '../models/m1/Services/GetSubmittedDeclarationsLite/GetSubmittedDeclarationsLiteRequest';

export class M1State {
    public _processing: boolean = false;

    public _declarationsOptions: DeclarationsOptions | null = null;
    public _pagedDeclarationsFilter: PagedDeclarationsFilter | null = new PagedDeclarationsFilter();

    public _getSubmittedDeclarationsLiteRequest: GetSubmittedDeclarationsLiteRequest | null = null;
    public _submittedDeclarationsLite: Array<DeclarationLite> | null = null;

}
