import configData from './config.json';
import { Locale } from '../common/models/Locale';
import localeData from './locales.json';

export class Config {
    public readonly ARTIFICIAL_NETWORK_DELAY: number = 0;
    public readonly HTTP_CLIENT_IMPL: string = 'axios';
    public readonly MIN_NUMERIC_INPUT_VALUE: number = 0.01;
    public readonly MAX_NUMERIC_INPUT_VALUE: number = 9999999999;
    public readonly MAX_NUMERIC_INPUT_LENGTH: number = 18;
    public readonly MAX_INPUT_LENGTH_20: number = 20;
    public readonly MAX_INPUT_LENGTH_100: number = 100;
    public readonly MAX_INPUT_LENGTH_200: number = 200;
    public readonly MAX_INPUT_LENGTH_2000: number = 2000;
    public readonly USER_NAME_TAXISNET: string = 'TAXISNET';
    public readonly ZERO_AFM: string = '*********';

    public readonly AUTHENTICATION_REQUIRED_ROUTER_PATHS = [
        '/logout',
        '/taxEntity',
        '/newDeclaration',
        '/viewDeclaration',
        '/submittedDeclarations'
    ].map((x) => x.toLowerCase());
    public readonly AUTHENTICATION_FORBIDDEN_ROUTER_PATHS = ['/login'].map((x) => x.toLowerCase());

    public auth: any = configData.auth;
    public api: any = configData.api;
    public application: any = configData.application;

    public get APP_ROOT_PATH(): string {
        return import.meta.env.APP_ROOT_PATH || environVars?.applicationRoot || '';
    }

    public get API_SERVERURL(): string {
        return import.meta.env.API_SERVERURL || environVars?.apiServerUrl || this.api.serverUrl;
    }

    public get API_ROOT_PATH(): string {
        return import.meta.env.API_ROOT_PATH || environVars?.apiRootPath || this.api.rootPath;
    }

    public get BACKEND_API_URL(): string {
        const url: string = `${this.API_SERVERURL}/${this.API_ROOT_PATH}`;
        return import.meta.env.BACKEND_API_URL || url;
    }

    public USE_SERVER_SESSION: boolean = import.meta.env.USE_SERVER_SESSION || environVars?.useServerSession || false;

    public get LOCAL_STORAGE_LOCALE_KEY(): string {
        return (
            import.meta.env.LOCAL_STORAGE_LOCALE_KEY ||
            environVars?.application?.localStorage?.localeKey ||
            this.application.localStorage.localeKey ||
            'LOCALE'
        );
    }

    public get LOCAL_STORAGE_AUTH_DATA_KEY(): string {
        return (
            import.meta.env.LOCAL_STORAGE_AUTH_DATA_KEY ||
            environVars?.application?.localStorage?.authDataKey ||
            this.application.localStorage.authDataKey ||
            'AUTH_DATA'
        );
    }

    public locales: Array<Locale> = localeData.map((x) => new Locale(x));
    private _locale: string = import.meta.env.VUE_APP_I18N_LOCALE || this.application?.locales?.default || 'el';
    private _fallbackLocale: string =
        import.meta.env.VUE_APP_I18N_FALLBACK_LOCALE || this.application?.locales?.default || 'el';

    public constructor() {
        if (configPatch) {
            ['auth', 'api', 'application'].forEach((cp) => {
                if (configPatch[cp]) {
                    const self: any = this as any;
                    if (self[cp] === undefined || self[cp] === null) {
                        self[cp] = configPatch[cp];
                    } else {
                        Object.keys(configPatch[cp]).forEach((x) => {
                            self[cp][x] = configPatch[cp][x];
                        });
                    }
                }
            });
        }
        if (this.auth?.externalProvider?.loginUrl) {
            this.auth.externalProvider.loginUrl = this.getProcessedStringValue(this.auth.externalProvider.loginUrl);
        }
        if (this.auth?.externalProvider?.logoutUrl) {
            this.auth.externalProvider.logoutUrl = this.getProcessedStringValue(this.auth.externalProvider.logoutUrl);
        }
        if (this.api?.serverUrl) {
            this.api.serverUrl = this.getProcessedStringValue(this.api.serverUrl);
        }
        this.loadData();
    }

    private getProcessedStringValue(value: string | null | undefined): string | null | undefined {
        if (!value) return value;
        let res: string | null | undefined = value;
        if (appServerRootUrl) res = res.replace('{appServerRootUrl}', appServerRootUrl);
        if (appServerPathUrl) res = res.replace('{appServerPathUrl}', appServerPathUrl);
        return res;
    }

    private loadData() {
        this._locale = import.meta.env.VUE_APP_I18N_LOCALE || this.application?.locales?.default || 'el';
        if (this.application?.locales?.selectionEnabled) {
            const storedValue: string | null = localStorage.getItem(this.LOCAL_STORAGE_LOCALE_KEY);
            if (storedValue) this._locale = storedValue;
        }
    }

    public get locale(): string {
        return this._locale;
    }

    public set locale(value: string) {
        this._locale = value;
        localStorage.setItem(this.LOCAL_STORAGE_LOCALE_KEY, this._locale);
    }

    public get fallbackLocale(): string {
        return this._fallbackLocale;
    }
}

const config: Config = new Config();

export default config;
