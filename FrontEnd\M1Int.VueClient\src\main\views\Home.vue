<template>
    <div
        class="full-width column justify-center items-center home-view"
        :style="{
            'background-image': backgroundImageUrl,
            'background-position-x': '-200px',
            'background-repeat': 'no-repeat'
        }"
    >
        <div class="full-width column justify-center items-center home-content">
            <div class="full-width row">
                <div class="col"></div>
                <div class="col-10 col-sm-8 col-md-8 col-lg-6">
                    <div style="margin: 50px auto 30px auto">
                        <div class="title text-center">
                            <div v-if="$q?.screen.gt.md" class="text-h4 txt-image-height" style="font-weight: 500">
                                <span class="draw-top-line primary-dark--text"></span>
                                <span>{{ $t('welcomeMessage1') }}</span>
                                <br />
                            </div>
                            <div v-else class="text-h5 txt-image-height" style="font-weight: 500">
                                <span class="draw-top-line primary-dark--text"></span>
                                <span>{{ $t('welcomeMessage1') }}</span>
                                <br />
                            </div>
                            <div
                                v-if="$q?.screen.gt.md"
                                class="q-mt-xs text-h5 txt-image-height"
                                style="font-weight: 500"
                            >
                                <span class="primary--text">{{ $t('welcomeMessage2') }}</span>
                            </div>
                            <div v-else class="q-mt-xs text-h6 txt-image-height" style="font-weight: 500">
                                <span class="primary--text"> {{ $t('welcomeMessage2') }}</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col"></div>
            </div>
        </div>
        <div class="full-width row justify-center items-center home-actions" style="margin: 5px auto">
            <div v-if="loginEnabled" class="full-width row wrap justify-center items-center">
                <div
                    class="row justify-center items-center main-action-button primary-dark"
                    @click="gotoRoute('/login')"
                >
                    <div class="button-caption">{{ $t('login') }}</div>
                </div>
            </div>
            <div
                v-if="dataLoaded && isUserAuthenticated"
                :class="`full-width ${$q?.screen?.width >= 900 ? 'row wrap' : 'column'} justify-center items-center`"
            >
                <div
                    class="row justify-center items-center main-action-button primary"
                    @click="gotoRoute('/submittedDeclarations')"
                >
                    <div class="button-caption">{{ $t('submittedDeclarations') }}</div>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import type { PropType } from 'vue';
import M1Helper from '@/m1Module/components/M1Helper';
import { IM1Store, getM1Store } from '@/m1Module/stores/M1Store';
import authService from '@/auth/services/AuthService';
import config from '@/config';

export default defineComponent({
    name: 'home-view',
    mixins: [M1Helper],
    props: { mainStore: { type: Object as PropType<IM1Store>, default: () => getM1Store() } },
    data() {
        const dataObj: {
            notifyType: string | null;
            notifyTitle: string | null;
            notifyMessage: string | null;
            dataLoaded: boolean;
        } = { notifyType: null, notifyTitle: null, notifyMessage: null, dataLoaded: false };
        return dataObj;
    },
    computed: {
        title(): string {
            return this.$t('applicationTitle').toString();
        },
        backgroundImageUrl(): string {
            let res: string = '';
            if (this.$q?.screen.gt.md) {
                const applicationRootPath: string = config.APP_ROOT_PATH || '';
                res = `url(${applicationRootPath}/assets/img/main-background.jpg)`;
            }
            return res;
        },
        isStoreProcessing(): boolean {
            return this.mainStore?.processing;
        },
        authenticatedUserName(): string {
            return authService?.userName;
        },
        isUserAuthenticated(): boolean {
            return authService?.userAuthenticated;
        },
        loginEnabled(): boolean {
            if (this.isUserAuthenticated === true) return false;
            if (config.auth?.externalProvider?.enabled === true) {
                return config.auth?.externalProvider?.loginEnabled === true;
                // return true;
            }
            return true;
        }
    },
    methods: {
        handleRouteQueryParams() {
            if (!this.$router || !this.$route) return;
            const query: any = this.$route.query || {};
            const params: Array<string> = ['notifyType', 'notifyTitle', 'notifyMessage'];
            params.forEach((x) => {
                if (query[x]) (this as any)[x] = query[x];
            });
            this.$router.replace({ query: {} });
        },
        clearNotifyParams() {
            this.notifyType = null;
            this.notifyTitle = null;
            this.notifyMessage = null;
        },
        handleNotify() {
            if (this.notifyTitle || this.notifyMessage) {
                this.$alert.notify({
                    type: this.notifyType || 'info',
                    title: this.notifyTitle,
                    message: this.notifyMessage
                });
            }
            this.clearNotifyParams();
        },
        gotoRoute(routePath: string): void {
            if (!routePath) return;
            this.$router?.push(routePath);
        },
        async loadData() {
            try {
                await this.commonStore?.getServerCurrentDateTime();
                this.dataLoaded = true;
            } catch (ex: any) {
                console.error(ex);
            }
        }
    },
    async created() {
        this.handleRouteQueryParams();
        await this.loadData();
    },
    mounted() {
        this.handleRouteQueryParams();
    }
});
</script>

<style>
.home-view {
    min-height: 500px;
    background-color: white;
    /*
    background-image: url(/assets/img/main-background.jpg);
    background-position-x: -200px;
    */
}

.home-view .home-content .paragraph {
    font-size: 16px;
}

.home-view .main-action-button {
    width: 250px;
    height: 110px;
    margin: 15px;
    padding: 0 12px !important;
    border-radius: 12px;
    cursor: pointer;
}

.home-view .main-action-button .button-caption {
    font-size: 22px;
    font-weight: normal;
    color: white;
    text-align: center;
}
</style>
