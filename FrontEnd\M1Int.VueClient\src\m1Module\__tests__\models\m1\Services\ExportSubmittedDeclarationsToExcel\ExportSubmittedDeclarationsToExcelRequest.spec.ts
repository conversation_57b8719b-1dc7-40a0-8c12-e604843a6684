import { ExportSubmittedDeclarationsToExcelRequest } from '@/m1Module/models/m1/Services/ExportSubmittedDeclarationsToExcel/ExportSubmittedDeclarationsToExcelRequest';

describe('ExportSubmittedDeclarationsToExcelRequest', () => {
    let request: ExportSubmittedDeclarationsToExcelRequest;

    beforeEach(() => {
        request = new ExportSubmittedDeclarationsToExcelRequest();
    });

    it('should be valid', () => {
        expect(request).toBeTruthy();
    });
});
