import { shallowMount, VueWrapper } from '@vue/test-utils';
import mockAlert from '@/mocks/MockAlert';
import FieldEditCheckbox from '@/common/components/form/FieldEditCheckbox.vue';
import { Component } from 'vue';

describe('FieldEditCheckbox', () => {
    let wrapper: VueWrapper;
    let component: Component;

    const model: any = {
        id: '001',
        isOk: true
    };

    beforeEach(() => {
        wrapper = shallowMount(FieldEditCheckbox, {
            global: {
                mocks: {
                    $t: (key: string) => {
                        return key;
                    },
                    $alert: mockAlert
                }
            },
            props: {
                modelObject: model,
                fieldId: 'isOk',
                fieldTitle: 'Is Ok'
            }
        });
        component = wrapper.vm as Component;
    });

    afterEach(() => {});

    it('should mount', () => {
        expect(wrapper).toBeTruthy();
        expect(wrapper.vm).toBeTruthy();
        expect(wrapper.vm.$el).toBeTruthy();
    });

    it('component should be valid', () => {
        expect(component).toBeTruthy();
    });

    it('should have valid $t property', () => {
        expect(wrapper.vm.$t).toBeTruthy();
        expect(typeof wrapper.vm.$t).toEqual('function');
    });

    it('should have valid $alert property', () => {
        expect((wrapper.vm as any).$alert).toBeTruthy();
    });

    it('should have valid field isOk value', () => {
        expect((component as any).getValue()).toEqual(model.isOk);
    });

    it('should have valid field isOk label', async () => {
        expect(wrapper.attributes('label')).toContain((component as any).fieldTitle);
    });

    it('should update field isOk value', () => {
        const newValue: boolean = false;
        expect((component as any).getValue()).toEqual(model.isOk);
        model.isOk = newValue;
        expect((component as any).getValue()).toEqual(newValue);
    });
});
