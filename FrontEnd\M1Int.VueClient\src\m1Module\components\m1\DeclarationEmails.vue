<template>
    <div v-if="declaration" class="full-width q-pb-sm q-px-xs row justify-center items-center view-declaration-data-content">
        <q-table
            :columns="emailsTableColumns"
            :rows="declaration.emails"
            :pagination="{ rowsPerPage: 0 }"
            hide-pagination
            :grid="$q?.screen?.width < 900"
            :dense="false"
            style="width: 100%"
            table-header-class="bg-primary text-white"
            class="email-data-list-table"
        >
            <template v-slot:body-cell-emailSentAtText="props">
                <q-td :props="props">
                    <div class="q-mx-sm row no-wrap justify-start items-center">
                        {{ props.row.emailSentAtText }}
                    </div>
                </q-td>
            </template>
            <template v-slot:item="props">
                <div
                    class="q-px-xs q-py-sm col-12 grid-style-transition"
                    style="min-width: 300px; display: flex; flex-grow: 1"
                >
                    <q-card bordered flat style="width: 100%; background-color: rgb(250, 250, 250)">
                        <div
                            v-for="col in props.cols.filter((col: any) => col.name !== 'actions')"
                            :key="col.name"
                            class="q-px-md"
                        >
                            <field-display
                                class="full-width"
                                :modelObject="props.row"
                                :fieldId="col.name"
                                :fieldTitle="col.label"
                                :value="col.value"
                            />
                        </div>
                    </q-card>
                </div>
            </template>
        </q-table>
    </div>
</template>

<script lang="ts">
import { PropType, defineComponent } from 'vue';
import { Validation } from '@vuelidate/core';
import { Declaration } from '../../models/m1/Entities/Declaration';
import FieldDisplay from '@/common/components/form/FieldDisplay.vue';

export default defineComponent({
    name: 'declaration-emails',
    components: {
        FieldDisplay
    },
    props: {
        declaration: {
            type: Declaration,
            default: null
        },
        validationObject: {
            type: Object as PropType<Validation>,
            default: null
        }
    },
    computed: {
        emailsTableColumns(): any[] {
            return [
                {
                    name: 'emailTo',
                    label: this.$t('emailTo').toString(),
                    field: 'emailTo',
                    align: 'left',
                    sortable: true,
                    style: 'white-space: break-spaces'
                },
                {
                    name: 'emailFrom',
                    label: this.$t('emailFrom').toString(),
                    field: 'emailFrom',
                    align: 'left',
                    sortable: true,
                    style: 'white-space: break-spaces width: 80px;'
                },
                {
                    name: 'emailSentAtText',
                    label: this.$t('emailSentAt').toString(),
                    field: 'emailSentAtText',
                    align: 'left',
                    sortable: true,
                    style: 'white-space: break-spaces'
                }
            ];
        }
    }
});
</script>

<style>
.view-declaration-data .field-group-header {
    padding: 6px 12px;
    background-color: var(--palette-primary-dark);
    color: white;
    border-radius: 6px;
}
.view-declaration-data .q-expansion-item .q-expansion-item__toggle-icon {
    color: white;
}
.view-declaration-data .declaration-link {
    color: var(--palette-primary-medium);
    font-weight: 600;
    cursor: pointer;
}
.view-declaration-data .success--text textarea {
    color: var(--palette-success) !important;
}
.view-declaration-data .warning--text textarea {
    color: var(--palette-warning) !important;
}
.view-declaration-data .error--text textarea {
    color: var(--palette-error) !important;
}

/* .email-data-list-table thead tr th {
    position: sticky;
    z-index: 1;
}
.email-data-list-table thead tr:first-child th {
    top: 0;
}
.email-data-list-table .q-table__bottom {
    display: none;
} */

/* .email-data-list-table {
    Remove fixed height to allow natural sizing
} */

.email-data-list-table thead tr th {
    position: sticky;
    z-index: 1;
}

.email-data-list-table thead tr:first-child th {
    top: 0;
}

.email-data-list-table .q-table__bottom {
    display: none;
}

/* Remove any max-height constraints */
.email-data-list-table .q-table__middle {
    max-height: none !important;
}
</style>
