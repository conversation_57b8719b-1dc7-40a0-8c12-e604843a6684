import { GetSubmittedDeclarationsLiteRequest } from '@/m1Module/models/m1/Services/GetSubmittedDeclarationsLite/GetSubmittedDeclarationsLiteRequest';

describe('GetSubmittedDeclarationsLiteRequest', () => {
    let request: GetSubmittedDeclarationsLiteRequest;

    beforeEach(() => {
        request = new GetSubmittedDeclarationsLiteRequest();
    });

    it('should be valid', () => {
        expect(request).toBeTruthy();
    });
});
