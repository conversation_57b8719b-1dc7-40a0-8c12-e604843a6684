<template>
    <nav class="column justify-end items-end main-menu">
        <div class="row justify-center items-center">
            <auth-menu class="auth-menu" />
            <locale-selector v-if="localeSelectorEnabled" class="locale-selector" />
            <div class="user-session-timer-countdown-container">
                <user-session-timer-countdown v-if="isUserAuthenticated" :showNotifications="true" />
            </div>
        </div>
    </nav>
</template>

<script lang="ts">
import { defineComponent, PropType } from 'vue';
import config from '@/config';
import { ICommonStore, getCommonStore } from '@/common/stores/Store';
import AuthMenu from './AuthMenu.vue';
import LocaleSelector from './LocaleSelector.vue';
import UserSessionTimerCountdown from '../UserSessionTimerCountdown.vue';

export default defineComponent({
    name: 'main-menu',
    components: {
        AuthMenu,
        LocaleSelector,
        UserSessionTimerCountdown
    },
    props: {
        store: {
            type: Object as PropType<ICommonStore>,
            default: () => getCommonStore()
        }
    },
    computed: {
        isUserAuthenticated(): boolean {
            return !!this.store?.authData?.userName;
        },
        localeSelectorEnabled(): boolean {
            return !!config.application?.locales?.selectionEnabled;
        }
    }
});
</script>

<style scoped>
.auth-menu,
.locale-selector,
.user-session-timer-countdown-container {
    margin: 0;
    padding: 0;
}

@media screen and (min-width: 640px) {
    .auth-menu,
    .locale-selector,
    .user-session-timer-countdown-container {
        padding: 0 8px;
    }
}

@media screen and (min-width: 800px) {
    .auth-menu,
    .locale-selector,
    .user-session-timer-countdown-container {
        padding: 0 16px;
    }
}
</style>
