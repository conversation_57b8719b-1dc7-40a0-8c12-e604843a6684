{"name": "aade-m1int-web-app-vue-client", "version": "0.1.0", "description": "M1Int Vue Client", "author": "aade.gdiled.dafe", "scripts": {"format": "prettier --write .", "type-check": "vue-tsc --noEmit -p tsconfig.vitest.json --composite false", "lint": "eslint .", "lint:fix": "eslint . --fix", "clean:dist": "del-cli --force ../../M1Int.Web/wwwroot/Apps/M1Int/**/*", "build:dev": "cross-env NODE_ENV=development vite build --mode development --outDir ../../M1Int.Web/wwwroot/Apps/M1Int", "build:dev:watch": "cross-env NODE_ENV=development vite build --mode development --outDir ../../M1Int.Web/wwwroot/Apps/M1Int --watch", "build:prod": "cross-env NODE_ENV=production vite build --mode production --outDir ../../M1Int.Web/wwwroot/Apps/M1Int", "build:prod:watch": "cross-env NODE_ENV=production vite build --mode production --outDir ../../M1Int.Web/wwwroot/Apps/M1Int --watch", "build": "npm run clean:dist && npm run build:prod && npm run build:dev", "dev": "vite --port 8080", "preview": "vite preview --port 8080", "test:unit": "vitest run --environment jsdom --root src/", "test:unit:dev": "vitest watch --environment jsdom --root src/", "test:unit:coverage": "vitest run --environment jsdom --root src/ --coverage", "test:e2e:standalone": "start-server-and-test preview http://localhost:8080 'playwright test'", "test:e2e:ui:standalone": "start-server-and-test preview http://localhost:8080 'playwright test --ui'", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:report": "playwright show-report"}, "dependencies": {"@quasar/extras": "1.17.x", "@vuelidate/core": "2.0.x", "@vuelidate/validators": "2.0.x", "axios": "1.11.x", "body-scroll-lock": "^4.0.0-beta.0", "dayjs": "1.11.x", "mitt": "3.0.x", "pinia": "3.0.x", "quasar": "2.18.x", "sweetalert2": "11.22.x", "uuid": "11.1.x", "vue": "3.5.x", "vue-i18n": "11.1.x", "vue-router": "4.5.x", "vue-timer-hook": "1.0.x"}, "devDependencies": {"@intlify/unplugin-vue-i18n": "6.0.x", "@pinia/testing": "1.0.x", "@playwright/test": "1.54.x", "@quasar/vite-plugin": "1.10.x", "@rushstack/eslint-patch": "1.12.x", "@types/body-scroll-lock": "3.1.x", "@types/node": "22.15.x", "@types/uuid": "10.0.x", "@vitejs/plugin-legacy": "7.2.x", "@vitejs/plugin-vue": "6.0.x", "@vitest/coverage-istanbul": "3.2.x", "@vue/eslint-config-typescript": "14.6.x", "@vue/test-utils": "2.4.x", "cross-env": "10.0.x", "del-cli": "6.0.x", "eslint": "9.33.x", "eslint-plugin-vue": "10.4.x", "jsdom": "26.1.x", "prettier": "3.6.x", "sass": "1.90.x", "start-server-and-test": "2.0.x", "terser": "5.43.x", "typescript": "5.9.x", "vite": "7.1.x", "vitest": "3.2.x", "vue-tsc": "3.0.x"}}