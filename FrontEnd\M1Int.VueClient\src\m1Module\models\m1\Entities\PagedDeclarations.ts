import { Declaration } from '../Entities/Declaration';
import { PagingParams } from './PagingParams';

export class PagedDeclarations {
    public declarations: Array<Declaration> = [];

    public pagingParams: PagingParams | null = null;

    public constructor(options?: any) {
        options = options || {};

        if (options.declarations?.length) {
            if (options.declarations[0] instanceof Declaration) this.declarations = options.declarations;
            else this.declarations = options.declarations.map((x: any) => new Declaration(x));
        }

        if (options.pagingParams) {
            if (options.pagingParams instanceof PagingParams) this.pagingParams = options.pagingParams;
            else this.pagingParams = options.pagingParams.map((x: any) => new PagingParams(x));
        }
    }
}
