<template>
    <div class="full-width q-pa-sm row justify-center items-center declarations-filter">
        <div class="q-pa-sm col col-12 col-md-8 justify-center items-center">
            <div class="q-pa-sm col col-12 col-md-8 justify-center items-center">
                <div class="full-width row justify-center items-center">
                    <div class="col-12 col-sm-12 col-md-3 q-px-lg">
                        <field-edit-text
                            class="full-width"
                            :modelObject="declarationsFilter"
                            :validationObject="validationObject.declarationsFilter"
                            fieldId="id"
                            :fieldTitle="$t('declarationNumber')"
                            :clearable="true"
                            :dense="true"
                            @keydown.enter="filterChanged"
                            @clear="filterChanged"
                        />
                    </div>
                    <div class="col-12 col-sm-12 col-md-3 q-px-lg">
                        <field-edit-text
                            ref="m1RepresentativeAfm"
                            class="full-width"
                            :modelObject="declarationsFilter"
                            :validationObject="validationObject.declarationsFilter"
                            fieldId="m1RepresentativeAfm"
                            :fieldTitle="$t('representativeAfm')"
                            :clearable="true"
                            :dense="true"
                            @valueChanged="m1RepresentativeAfmChanged"
                            @keydown.enter="filterChanged"
                            @clear="filterChanged"
                        />
                    </div>
                    <div class="col-12 col-sm-12 col-md-3 q-px-lg">
                        <field-edit-text
                            ref="m1Afm"
                            class="full-width"
                            :modelObject="declarationsFilter"
                            :validationObject="validationObject.declarationsFilter"
                            fieldId="m1Afm"
                            :fieldTitle="$t('afm')"
                            :clearable="true"
                            :dense="true"
                            @valueChanged="m1AfmChanged"
                            @keydown.enter="filterChanged"
                            @clear="filterChanged"
                        />
                    </div>
                    <div class="col-12 col-sm-12 col-md-3 q-px-lg">
                        <field-edit-text
                            ref="m1IdNumber"
                            class="full-width"
                            :modelObject="declarationsFilter"
                            :validationObject="validationObject.declarationsFilter"
                            fieldId="m1IdNumber"
                            :fieldTitle="$t('idDocument')"
                            :clearable="true"
                            :dense="true"
                            @keydown.enter="filterChanged"
                            @clear="filterChanged"
                        />
                    </div>
                </div>
            </div>
            <div class="full-width row justify-center items-center">
                <div class="col-12 col-sm-12 col-md-6 q-px-lg">
                    <field-edit-text
                        ref="m1SurNameA"
                        class="full-width"
                        :modelObject="declarationsFilter"
                        :validationObject="validationObject.declarationsFilter"
                        fieldId="m1SurNameA"
                        :fieldTitle="$t('lastName')"
                        :clearable="true"
                        :dense="true"
                        @keydown.enter="filterChanged"
                        @clear="filterChanged"
                    />
                </div>
                <div class="col-12 col-sm-12 col-md-6 q-px-lg">
                    <field-edit-text
                        ref="m1Name"
                        class="full-width"
                        :modelObject="declarationsFilter"
                        :validationObject="validationObject.declarationsFilter"
                        fieldId="m1Name"
                        :fieldTitle="$t('firstName')"
                        :clearable="true"
                        :dense="true"
                        @keydown.enter="filterChanged"
                        @clear="filterChanged"
                    />
                </div>
            </div>
        </div>
        <div class="col-12 col-md-2 actions">
            <div class="full-width row justify-center items-center">
                <q-btn
                    size="md"
                    class="q-mx-sm semi-faded"
                    color="info"
                    icon="refresh"
                    :title="$t('refresh')"
                    @click="filterChanged"
                />
                <q-btn
                    v-if="exportEnabled"
                    size="md"
                    class="q-mx-sm semi-faded"
                    color="green-8"
                    icon="file_download"
                    :title="$t('exportToExcel')"
                    @click="exportToExcel"
                />
                <q-btn
                    v-if="exportCancelEnabled"
                    size="md"
                    class="q-mx-sm semi-faded"
                    color="orange"
                    icon="stop"
                    :title="$t('exportToExcelCancel')"
                    @click="cancelExport"
                >
                </q-btn>
            </div>
        </div>
        <div class="col-12 col-md-2">
            <div class="q-pa-sm full-width row justify-center items-center">
                <div v-if="showExportInfo && exportProcessMessage" class="export-info-display">
                    <div class="export-progress-card">
                        <div class="progress-text">{{ exportProcessMessage }}</div>
                        <div v-if="exportCancelEnabled" class="action-text">{{ $t('clickToCancelExport') }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import { defineComponent, PropType, Ref } from 'vue';
import { useVuelidate, Validation } from '@vuelidate/core';
import { IM1Store, getM1Store } from '@/m1Module/stores/M1Store';
import { DeclarationsFilter } from '@/m1Module/models/m1/Entities/DeclarationsFilter';
import FieldEditText from '@/common/components/form/FieldEditText.vue';
import { DeclarationsOptions } from '@/m1Module/models/m1/Entities/DeclarationsOptions';
import { validAFM } from '@/common/utilities';

export default defineComponent({
    name: 'declarations-filter',
    components: {
        FieldEditText
    },
    props: {
        store: {
            type: Object as PropType<IM1Store>,
            default: () => getM1Store()
        },
        exportEnabled: {
            type: Boolean,
            default: false
        },
        exportCancelEnabled: {
            type: Boolean,
            default: false
        },
        exportProcessMessage: {
            type: String as PropType<string | null>,
            default: null
        }
    },
    emits: ['filterChanged', 'filterInvalid', 'exportToCsv', 'exportToExcel', 'cancelExport'],
    validations() {
        return {
            declarationsFilter: {
                id: {},
                m1Afm: { validAFM },
                m1IdNumber: {},
                m1SurNameA: {},
                m1SurNameB: {},
                m1Name: {},
                m1RepresentativeAfm: { validAFM }
            }
        };
    },
    data() {
        const dataObj: {
            validationObject: Ref<Validation>;
            showExportInfo: boolean;
            hideInfoTimeout: ReturnType<typeof setTimeout> | null;
        } = {
            validationObject: useVuelidate(),
            showExportInfo: false,
            hideInfoTimeout: null
        };
        return dataObj;
    },
    computed: {
        title(): string {
            return this.$t('declarations').toString();
        },
        declarationsOptions(): DeclarationsOptions {
            return this.store?.declarationsOptions || new DeclarationsOptions();
        },
        declarationsFilter(): DeclarationsFilter | null {
            return this.store?.pagedDeclarationsFilter?.declarationsFilter || null;
        },
        isDirty(): boolean {
            if (!this.validationObject) return false;
            return this.validationObject?.$dirty || this.validationObject?.$anyDirty;
        },
        isValid(): boolean {
            if (!this.validationObject) return true;
            return !(this.validationObject?.$invalid || this.validationObject?.$anyError);
        }
    },
    watch: {
        // Watch for changes in export process message
        exportProcessMessage: {
            handler(newValue: string) {
                if (newValue) {
                    this.showExportInfo = true;
                    this.resetHideTimer();
                } else if (!this.exportCancelEnabled) {
                    // Only hide immediately if export is not running
                    this.showExportInfo = false;
                }
            },
            immediate: true
        },

        // Watch for export completion/cancellation
        exportCancelEnabled: {
            handler(newValue: boolean) {
                if (!newValue && this.exportProcessMessage) {
                    // Export finished or cancelled, start timer to hide info
                    this.resetHideTimer();
                } else if (newValue) {
                    // Export started, show info
                    this.showExportInfo = true;
                }
            },
            immediate: true
        }
    },
    methods: {
        validate(): boolean {
            if (!this.validationObject) return true;
            this.validationObject?.$touch();
            return this.isValid;
        },
        resumeFilterData() {
            if (!this.declarationsFilter) return;
            let doApplyFilter: boolean = false;
            const applicantAfm: string | null =
                this.$route?.query?.applicantAfm?.toString() || this.$route?.query?.afm?.toString() || null;
            if (applicantAfm) {
                if (this.declarationsFilter.m1Afm !== applicantAfm) {
                    this.declarationsFilter.m1Afm = applicantAfm;
                    doApplyFilter = true;
                }
            }
            if (doApplyFilter) this.filterChanged();
        },
        fixDeclarationsFilter() {
            if (this.declarationsFilter?.m1Afm) this.declarationsFilter.m1Afm = this.declarationsFilter?.m1Afm.trim();
        },
        filterChanged() {
            if (!this.validate()) {
                this.$emit('filterInvalid');
                return;
            }
            this.fixDeclarationsFilter();
            this.$emit('filterChanged', this.declarationsFilter);
            this.validationObject?.$reset();
        },
        m1AfmChanged() {
            if (!this.declarationsFilter?.m1Afm) return;
            if (!validAFM(this.declarationsFilter?.m1Afm)) return;
            this.filterChanged();
        },
        m1RepresentativeAfmChanged() {
            if (!this.declarationsFilter?.m1RepresentativeAfm) return;
            if (!validAFM(this.declarationsFilter?.m1RepresentativeAfm)) return;
            this.filterChanged();
        },
        exportToCsv() {
            if (!this.validate()) {
                this.$emit('filterInvalid');
                return;
            }
            this.fixDeclarationsFilter();
            this.$emit('exportToCsv', this.declarationsFilter);
            this.validationObject?.$reset();
        },
        exportToExcel() {
            if (!this.validate()) {
                this.$emit('filterInvalid');
                return;
            }
            this.fixDeclarationsFilter();
            this.$emit('exportToExcel', this.declarationsFilter);
            this.validationObject?.$reset();
        },
        cancelExport() {
            this.$emit('cancelExport');
        },
        resetHideTimer() {
            // Clear existing timer
            if (this.hideInfoTimeout) {
                clearTimeout(this.hideInfoTimeout);
                this.hideInfoTimeout = null;
            }

            // Set new timer to hide info after 5 seconds
            this.hideInfoTimeout = setTimeout(() => {
                this.showExportInfo = false;
                this.hideInfoTimeout = null;
            }, 5000);
        }
    },
    beforeUnmount() {
        // Clean up timer
        if (this.hideInfoTimeout) {
            clearTimeout(this.hideInfoTimeout);
        }
    },
    mounted() {
        this.$nextTick(() => {
            this.resumeFilterData();
            // Initialize showExportInfo based on current state
            this.showExportInfo = this.exportCancelEnabled || !!this.exportProcessMessage;
        });
    }
});
</script>

<style scoped>
.declarations-filter {
    max-width: 1500px;
    margin: 0 auto !important;
}
.declarations-filter .actions {
    text-align: center;
}
.export-info-display {
    position: relative;
    display: flex;
    align-items: center;
}

.export-progress-card {
    background-color: #009fdf;
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 12px;
    /* white-space: nowrap; */
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.progress-text {
    font-weight: 500;
    margin-bottom: 2px;
}

.action-text {
    font-size: 11px;
    opacity: 0.8;
}

/* Optional: Add animation for smooth appearance */
.export-info-display {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateX(-10px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}
</style>
