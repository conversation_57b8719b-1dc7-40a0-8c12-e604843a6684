import { DeclarationLite } from '../Entities/DeclarationLite';
import { PagingParams } from './PagingParams';

export class PagedDeclarationsLite {
    public declarations: Array<DeclarationLite> = [];

    public pagingParams: PagingParams | null = null;

    public constructor(options?: any) {
        options = options || {};

        if (options.declarations?.length) {
            if (options.declarations[0] instanceof DeclarationLite) this.declarations = options.declarations;
            else this.declarations = options.declarations.map((x: any) => new DeclarationLite(x));
        }

        if (options.pagingParams) {
            if (options.pagingParams instanceof PagingParams) this.pagingParams = options.pagingParams;
            else this.pagingParams = options.pagingParams.map((x: any) => new PagingParams(x));
        }
    }
}
