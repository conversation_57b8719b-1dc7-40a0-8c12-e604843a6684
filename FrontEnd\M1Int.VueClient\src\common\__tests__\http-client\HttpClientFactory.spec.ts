import { IHttpClient } from '@/common/http-client/IHttpClient';
import { createHttpClient } from '@/common/http-client/HttpClientFactory';

describe('HttpClientFactory', () => {
    let client: IHttpClient;

    beforeEach(() => {
        client = createHttpClient();
    });

    it('http client is valid', () => {
        expect(client).toBeTruthy();
    });

    it('http client has baseurl property', () => {
        expect(client.baseUrl).not.toBeUndefined();
        expect(client.baseUrl).not.toBeNull();
    });
});
