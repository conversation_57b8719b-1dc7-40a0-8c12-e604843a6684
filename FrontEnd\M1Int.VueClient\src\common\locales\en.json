{"language": "language", "english": "English", "greek": "Greek", "aadeTitle": "I.A.P.R.", "applicationTitle": "VAT and PIN requests management", "applicationDescription": "VAT and PIN requests management", "version": "Version", "message": "Message", "info": "Information", "information": "Information", "success": "Success", "warning": "Warning", "danger": "Danger", "error": "Error", "question": "Question", "confirmation": "Confirmation", "prompt": "Prompt", "progress": "Progress", "process": "Process", "pleaseWait": "Please wait", "continue": "Continue", "close": "Close", "yes": "Yes", "no": "No", "ok": "Ok", "cancel": "Cancel", "back": "Back", "return": "Return", "refresh": "Refresh", "submit": "Submit", "saveAndAdd": "Save and add", "save": "Save", "add": "Add", "edit": "Edit", "display": "Display", "delete": "Delete", "rename": "<PERSON><PERSON>", "search": "Search", "searchHint": "Type a term to search for", "selection": "Selection", "select": "Select", "selectAll": "Select all", "addFiles": "Add files", "addFile": "Add file", "selectFiles": "Select files", "selectFile": "Select file", "selectAttachments": "Select attachments", "selectAttachment": "Select attachment", "selectFileToUpload": "Select 1 file to upload", "selectFilesToUpload": "Select upto {0} files to upload", "errorTitle": "Error", "errorMessage": "An error has occured.", "unauthorizedAccess": "Unauthorized access attempted.", "forbiddenAccess": "Operation forbidden.", "formIsInvalid": "Form fields have invalid values.", "fieldIsRequired": "Field is required.", "fieldMinLength": "Field's \"{0}\" length must be at least {1}.", "fieldMaxLength": "<PERSON>'s \"{0}\" length can be at most {1}.", "fieldMinValue": "<PERSON>'s \"{0}\" value must be >= {1}.", "fieldMaxValue": "<PERSON>'s \"{0}\" value must be <= {1}.", "fieldIsInvalid": "Field \"{0}\" is invalid.", "deleteConfirmation": "Please confirm the deletion.", "discardChangesConfirmation": "If you close the form, any changes you have made will be lost. Are you sure you want to close the form?", "saveInvalidConfirmation": "The form has fields with invalid values. Are you sure you want to save?", "home": "Home", "about": "About", "contact": "Contact", "form": "Form", "homeLinkDescription": "Link to home page", "aboutLinkDescription": "Link to about page", "contactLinkDescription": "Link to contact page", "formLinkDescription": "Link to form page", "toggleDrawerDescription": "Toggle drawer", "closeDrawerDescription": "Close drawer", "authentication": "Authentication", "userAuthentication": "User authentication", "registration": "Registration", "register": "Register", "login": "<PERSON><PERSON>", "logout": "Logout", "user": "User", "userName": "Username", "password": "Password", "userAuthenticationSuccess": "User authentication succeeded.", "userAuthenticationFailure": "User authentication failed.", "userAuthorizationExpired": "Authorization for user {0} has expired.", "userRegistrationSuccess": "User registration succeeded.", "userRegistrationFailure": "User registration failed.", "userLogoutSuccess": "User disconnected successfully.", "loginWithUsernameAndPassword": "Login using Username and Password", "userSession": "User session", "userSessionExpirationMessage": "<div>Your session will expire in {0}.</div><div>You should save any work you have in progress before the session expires.</div>", "userSessionHasExpired": "You session has expired.", "userSessionExpiredLoginRequired": "You session has expired. In order to continue using the application you will have to login.", "timerCountdown": "Countdown timer", "timerTimeLeftMessage": "Timer will expire in {0}.", "timerHasExpired": "Timer has expired.", "name": "Name", "email": "e-mail", "date": "Date", "time": "Time", "now": "Now", "dateFormat": "DD/MM/YYYY", "datePlaceholder": "DD/MM/YYYY", "dateTimeFormat": "DD/MM/YYYY HH:mm", "dateTimeSecondsFormat": "DD/MM/YYYY HH:mm:ss", "dateTimePlaceholder": "DD/MM/YYYY HH:mm", "dateTimeSecondsPlaceholder": "DD/MM/YYYY HH:mm:ss", "timeFormat": "HH:mm", "timeSecondsFormat": "HH:mm:ss", "timePlaceholder": "HH:mm", "timeSecondsPlaceholder": "HH:mm:ss", "thousandSeparator": ",", "decimalSeparator": ".", "list": "List", "table": "Table", "cards": "Cards", "homeTitle": "Home", "homeMessage": "This is the home page", "welcomeMessage": "Welcome to the VAT and PIN requests management of IAPR", "aboutTitle": "About", "aboutMessage": "This is the about page", "contactTitle": "Contact", "contactMessage": "This is the contact page", "number1": "Number 1", "number2": "Number 2", "person": "Person", "persons": "Persons", "file": "File", "files": "Files", "availableSpace": "Available space", "attachment": "Attachment", "attachments": "Attachments", "attachmentRejected": "File attachment is not allowed", "attachmentInvalidType": "Invalid file type {0}, allowed file types are: {1}", "attachmentsMaxFilesNumberExceeded": "Maximum allowed number of files exceeded ({0})", "attachmentMaxFileSizeExceeded": "Maximum allowed file size exceeded ({0} KB / {1} KB)", "attachmentsMaxTotalFilesSizeExceeded": "Total maximum allowed file size exceeded ({0} KB)", "welcomeMessage1": "Welcome to the", "welcomeMessage2": "VAT and PIN requests management", "welcomeMessage3": "service of IAPR", "print": "Print", "registryData": "Registry data", "taxEntity": "Tax entity", "taxEntityData": "Tax entity data", "taxRepresentative": "Tax representative", "taxRepresentativeData": "Tax representative data", "loadingTaxEntityData": "Loading tax entity data", "taxEntityDataLoadFailure": "Tax entity data loading failed", "taxEntityDataForUserNotFound": "Tax entity data for user '{0}' could not be found", "taxEntityNotAllowedToSubmitDeclaration": "Tax entity with  vat number {0} is not allowed to submit application.", "taxEntityIsInactiveTitle": "TIN {0} is deactivated according to IAPR registry.", "taxEntityIsInactive": "The use of the application is not permitted for the taxpayer with TIN {0} due to deactivation in the IAPR Registry.", "taxEntityIsDeadTitle": "TIN {0} belongs to a deceased person according to IAPR registry.", "taxEntityIsDead": "The use of the application is not permitted for the taxpayer with TIN {0} due to a death declaration in the IAPR Registry.", "taxEntityIsStolenIdTitle": "Identification document theft has been reported for TIN {0} according to IAPR registry.", "taxEntityIsStolenId": "The use of the application is not permitted for the taxpayer with TIN {0} due to a declaration of identification document theft in the IAPR Registry.", "taxEntityIsNotEpitid": "This application can only be used by professionals.", "taxEntityViesCheck": "The submission of a application by the TIN {0} is not permitted due to suspension of use for intra-community transactions (POL.1200/2015).", "printTaxEntityData": "Print tax entity data", "actAs": "Act", "me": "Me", "self": "For myself", "for": "for", "attributes": "Attributes", "isRegistryInvalid": "Registry entry is invalid", "isRegistryValid": "Registry entry is valid", "individual": "Individual", "firm": "Firm", "dead": "Dead", "badAfm": "Wrong TIN", "invalidAfm": "Invalid TIN", "inactiveAfm": "Inactive TIN", "stolenId": "Stolen ID card", "epitid": "Freelancer", "viesCheck": "VIES check", "accountant": "Accountant", "asAccountant": "as an accountant", "accountingOffice": "Accounting office", "asAccountingOffice": "as an accountant in an accounting office", "legalRepresentative": "Legal representative", "asLegalRepresentative": "as a representative of a legal entity", "selectAuthorizer": "Select authorizer", "searchAuthorizer": "Search authorizer", "afm": "TIN", "doy": "<PERSON><PERSON>", "firmName": "Company name", "firstName": "First name", "lastName": "Last name", "fullName": "Name", "fatherName": "Fathername", "idDocNumber": "Identification document number", "phone": "Phone", "telephone": "Telephone", "address": "Address", "declaration": "Application", "submission": "Submission", "served": "Serviced at", "idDocument": "Identification document", "submissionMode": "Sybmitted by", "submissionSelf": "Applicant", "submissionLegalRepresentative": "Legal Representative", "submissionAuthorizedRepresentative": "Authorized Representative", "submissionUndefined": "undefined", "submitDeclaration": "Submit application", "submitDeclarationConfirmationTitle": "The application is on time", "submitDeclarationConfirmationOverdueTitle": "The application is overdue", "submitOverdueDeclarationConfirmationOverdueMessage": "You will be informed by the competent Service regarding the fine provided for in article 53 of the C.P.D.", "submitDeclarationConfirmationMessage": "Please note that after final submission, you cannot change the data or delete the finally submitted application.<br/>Select \"Return\" to change any data you wish or \"Submit\" to submit it finally.", "submittedDeclarations": "Applications", "viewDeclaration": "View application", "viewAttachment": "View attachment", "printDeclaration": "Application digital file", "printDeclarationPayment": "Debt digital file", "noSubmittedDeclarationsFound": "No applications found", "inputAllRequiredDeclarationFieldsSubmit": "Please fill in the requested fields in order to submit your application.", "deleteAllTransactionsConfirmation": "Are you sure you want to delete all application's counter parties?", "deleteTransactionConfirmation": "Are you sure you want to delete the counter party?", "declarationSubmitConfirmation": "Are you sure you want to submit application?", "declarationDeleteConfirmation": "Are you sure you want to delete application?", "declarationDeleteSuccess": "Application deleted sucessfully", "declarationDeleteFailure": "Application deletion failed", "declarationSaveSuccess": "Application saved sucessfully", "declarationSaveFailure": "Application save failed", "declarationSubmitSuccess": "Application with number {0} submitted sucessfully", "declarationSubmitFailure": "Application submission failed", "declarationGetSuccess": "Application retrieved successfully", "declarationGetFailure": "Application retrieval failed", "declarationGetNotFound": "Application with id {0} not found", "declarationInitialDescription": "Initial transaction fee application", "declarationModificationDescription": "Amending transaction fee application", "declarationRelated": "Related application", "status": "Status", "declarationApprovalStatus": "Approval status", "declarationApprovalStatusApproved": "Approved", "declarationApprovalStatusRejected": "Rejected", "declarationApprovalStatusPending": "Pending approval", "declarationApprovalStatusExpired": "Expired", "year": "Year", "searchTerm": "Search term", "declarationNumber": "Application number", "declarationDate": "Submission date", "project": "Project", "projectDescription": "Brief project description / other comments", "projectStart": "Start date of works", "projectStartInvalid": "Invalid start date of works", "projectValue": "Project value (euro)", "projectValueUndefined": "Undefined value", "projectValueHint": "An amount exceeding 6,000 euros is filled in. Agreements for lower value projects are submitted electronically in the \"Status of Agreements, paragraph 16, article 8, law 1882/1990\" if you are a professional.", "projectMinValue": "The field is filled in with amounts over {0} euros.", "projectMaxValue": "Field's value can be up to {0} euros.", "projectAttachment": "Attach agreement file", "applicantAfm": "Applicant TIN", "foreignAfm": "Without Greek TIN", "foreignAfmRequired": "Please fill in the foreigner's TIN", "assignor": "Assignor", "assignorAFM": "Assignor TIN", "transactors": "Transactors", "transactorsAfms": "Transactor TINs", "transactor": "Transactor", "transactorData": "Contractor/subcontractor details", "transactorAFM": "Counterparty TIN", "addressData": "Address data", "street": "Street", "number": "Number", "city": "City", "zip": "Zip code", "country": "Country", "firmTransactorNotAllowed": "The counterparty is a firm. The application must be submitted by the counterparty.", "getAfmRegistryError": "Failed to retrieve registry details for TIN {0}.", "isSameApplicantTransactorAfm": "The TIN of the counterparty is the same with that of the assignor.", "sameApplicantTransactorAfm": "The TIN of the counterparty is not allowed to be the same as that of the assignor.", "transactorAfmExists": "You have already registered a counterparty with TIN {0}.", "outOfMarginSubmissionMessage": "<div class='text-left' style='font-size: 0.85rem;'>The application is overdue.</ div>", "protocolNumber": "Pending request protocol number", "protocolNumberHint": "The protocol number of any relevant request submitted through the \"My Requests\" application and remaining pending on 01/11/2024 is filled in.", "protocolNumberInvalid": "An unacceptable number has been entered in the \"Pending request protocol number\" field.", "relatedDeclaration": "Link to relevant submission", "relatedDeclarationHint": "Select from the list or fill in the number of the previous application in case of contract modification.", "relatedDeclarationInvalid": "Invalid characters have been entered in the \"Link to previous submission\" field.", "relatedDeclarationNotFound": "Related application not found. No application found to modify.", "relatedDeclarationIdNotFound": "Related application with id {0} not found.", "declarationNoItemsNotAllowed": "Submission of application without counter parties is not allowed.", "attachmentsMaxTotalFilesSize": "The total size of the files you have attached is {0} Kb. Maximum total attachments size allowed is {1} Kb.", "approve": "Approve", "approval": "Approval", "approveTransactor": "Approve application", "approveTransactorDescription": "Approve application {0}", "approveTransactorWarning": "The application approval process is irreversible.", "approveTransactorConfirmation": "The application approval process is irreversible.<br/>Are you sure you want to approve application {0}?", "approveTransactorFailure": "Approval of application {0} failed.", "approveTransactorSuccess": "Approval of application {0} completed successfully.", "reject": "Reject", "rejection": "Rejection", "rejectedDeclaration": "The application has been rejected by at least one counterparty.", "rejectTransactor": "Reject application", "rejectTransactorDescription": "Reject application {0}", "rejectTransactorWarning": "The application rejection process is irreversible.", "rejectTransactorConfirmation": "The application rejection process is irreversible.<br/>Are you sure you want to reject application {0}?", "rejectTransactorFailure": "Rejection of application {0} failed.", "rejectTransactorSuccess": "Rejection of application {0} was completed successfully.", "expire": "Terminate", "expiration": "Termination", "expiredDeclarationSubmitted": "The contract has been terminated.", "expireDeclaration": "Termination of contract", "expireDeclarationDateAfterToday": "The contract termination date cannot be later than today.", "expireDeclarationDate": "Contract termination date", "expireDeclarationComments": "Contract termination comments", "expireDeclarationDescription": "Termination of contract {0}", "expireDeclarationWarning": "The contract termination process is irreversible.", "expireDeclarationConfirmation": "The contract termination process is irreversible.<br/>Are you sure you want to terminate the agreement {0}?", "expireDeclarationFailure": "The termination of the agreement {0} failed.", "expireDeclarationSuccess": "The termination of the agreement {0} was completed successfully.", "export": "Export", "exportToCsv": "Export to CSV", "exportToExcel": "Export to Excel", "exportToExcelCancel": "Cancel export to Excel", "exportProcess": "File {0} / {1}", "exportProcessCompleted": "Export to Excel completed. Number of files {0}.", "exportProcessCanceled": "Export to Excel canceled. Number of files {0}.", "clickToCancelExport": "Click on Cancel button to cancel process", "declarationConsentTitle": "Condition", "declarationConsentText": "With my personal responsibility and knowing the sanctions provided by the provisions of par. 6 of article 22 of Law 1599/1986, I declare that only recreational technical games are held at the specially prepared tables of the census application.", "declarationConsent": "I hereby declare that I have read and accept the above condition.", "declarationConsentRequired": "Acceptance of the condition is mandatory.", "representativeAfm": "Representative T<PERSON>", "representativeName": "Representative name", "declarationReplacedBy": "Application is replaced by application with number {0}", "applicationData": "Application Information", "personalIdentificationData": "Personal & Identification Information", "personalData": "Personal Information", "identificationData": "Identification Information", "contactData": "Contact Information", "generalData": "General Information", "relatedTinsData": "Related Persons’ TINs", "relatedTinData": "Related", "relatedTin1": "TIN 1", "relatedTin2": "TIN 2", "relatedTin3": "TIN 3", "relatedTin4": "TIN 4", "relatedTin5": "TIN 5", "applicantName": "Applicant Name", "applicantFatherName": "Applicant's Father Name", "applicantMotherName": "Applicant's Mother Name", "sex": "Sex", "surNameA": "Surname A", "surNameB": "Surname B", "fathersSurName": "Father's Surname", "fathersName": "Father's Name", "mothersSurName": "<PERSON>'s Surname", "mothersName": "Mother's Name", "dateOfBirth": "Date of Birth", "countryOfBirth": "Country of Birth", "placeOfBirthInGreece": "Place of Birth in Greece", "genderMale": "Male", "genderFemale": "Female", "idKind": "Type of identification document", "idNumber": "Identification document number", "issueDate": "Date of Issue", "endDate": "Date of Expiry", "authority": "Issuing Authority", "permitKind": "Type of Residence Permit", "permitNumber": "Residence Permit Number", "maritalStatus": "Marital Status", "judicialSupport": "Judicial Support", "citizenShip": "Citizenship", "taxpayerStatusJudicialSupportYes": "Yes", "taxpayerStatusJudicialSupportNo": "No", "residenseKind": "Country of Residence", "residenseKindGreek": "Greece", "residenseKindAbroad": "Abroad", "residenseAbroad": "Country of Residence Abroad", "afmAbroad": "Foreign Country TIN Number", "municipality": "Municipality", "prefecture": "Prefecture", "region": "Region", "notificationChoice": "Tax representative and Tax Administration notifications", "notificationChoiceUndefined": "not defined Tax representative and Tax Administration notifications", "notificationChoiceNone": "not defined Tax representative and Tax Administration notifications", "notificationChoiceTaxRepresentativeOnly": "I wish to appoint a tax representative to whom all types of acts, documents and individual notices from the Tax Administration will be communicated to me.", "notificationChoiceTaxRepresentativePlusSelf": "I wish to appoint a tax representative, and I declare that I wish the notification of all types of acts, documents and individual notices of the Tax Administration to me to be made to the contact details I declare to the Tax Administration in addition to my tax representative.", "notificationChoiceSelfOnly": "I do not wish to appoint a tax representative and I accept that the notification of all types of acts, documents and individual notices of the Tax Administration to me shall be made to the contact details I declare to the Tax Administration.", "relStartDate": "Relation Start Date", "relKind": "Type of Relation", "relProofDoc": "Proof of Relation Document", "relProofDocNo": "Proof of Relation Document Number", "relProofDocDate": "Proof of Relation Document Date", "relProofDocAuthor": "Proof of Relation Document Issuing Authority", "m1Created": "Application creation date", "m1UpdatedEmpl": "TIN obtaining date", "m1Afm": "TIN assigned", "representativeSendEmail": "Send email", "sentEmails": "Sent emails", "emailTo": "Recipient", "emailFrom": "Sender", "emailSentAt": "Sent Date", "serviceChannelType": "Service channel", "serviceChannelTypeUndefined": "No service channel has been defined", "serviceChannelTypeNone": "No service channel has been defined", "serviceChannelTypeNavigateToFAABooking": "Direct Video Call", "serviceChannelTypeNavigateToMyAADEliveBooking": "myAADElive appointment", "serviceChannelTypeNavigateToDoyBooking": "Tax Office appointment (Physical presence)", "serviceChannelTypeNavigateToTicketBooking": "Add request", "serviceChannelTypeNavigateToMyAADEliveSpecialBooking": "myAADElive appointment (with translation or in Sign Language)", "of": "of", "recordsPerPage:": "Records per page:"}