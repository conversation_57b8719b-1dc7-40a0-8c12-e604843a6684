import { HttpClientBase } from './HttpClientBase';
import config from '@/config';

export class HttpClientFetch extends HttpClientBase {
    constructor(public readonly baseUrl: string = config.BACKEND_API_URL) {
        super(baseUrl);
    }

    private getErrorMessage(err: any): string {
        let errorMessage = 'API Error';
        if (err) {
            if (err.errorMessage || err.message) {
                errorMessage = err.errorMessage || err.message;
            } else if (err.errorMessages && err.errorMessages.length > 0) {
                errorMessage = err.errorMessages.join(', ');
            } else if (err.messages && err.messages.length > 0) {
                errorMessage = err.messages.join(', ');
            }
        }
        return errorMessage;
    }

    private handleApiResponse(response: any): Promise<any> {
        if (!response) throw new Error('Invalid response object!');
        let contentType = '';
        if (response.headers && response.headers.has('Content-Type'))
            contentType = response.headers.get('Content-Type');
        // let isJson: boolean = false;
        let responseContentPromise: Promise<any>;
        if (contentType.includes('application/json')) {
            // isJson = true;
            responseContentPromise = response.json();
        } else {
            responseContentPromise = response.text();
        }
        if (response.ok) {
            return responseContentPromise;
        } else {
            return responseContentPromise.then((err) => {
                /*
                    if (isJson)
                        err = JSON.stringify(err);
                    */
                return Promise.reject(this.getErrorMessage(err));
            });
        }
    }

    protected getOptions(options?: any): any {
        options = super.getOptions(options);
        options.credentials = options.credentials || 'same-origin';
        return options;
    }

    public request<T>(options: any): Promise<T> {
        options = this.getUrlAndOptions(options?.url, options);
        return fetch(options.url, options)
            .then((response) => this.handleApiResponse(response))
            .then((result) => <T>result);
    }

    public get<T>(url: string, options?: any): Promise<T> {
        options = this.getUrlAndOptions(url, options);
        options.method = 'GET';
        return fetch(options.url, options)
            .then((response) => this.handleApiResponse(response))
            .then((result) => <T>result);
    }

    public post<T, U>(url: string, data: U, options?: any): Promise<T> {
        options = this.getUrlAndOptions(url, options);
        options.method = 'POST';
        options.body = this.getDataFromModel(data, options);
        return fetch(options.url, options)
            .then((response) => this.handleApiResponse(response))
            .then((result) => <T>result);
    }

    public delete<T>(url: string, options?: any): Promise<T> {
        options = this.getUrlAndOptions(url, options);
        options.method = 'DELETE';
        return fetch(options.url, options)
            .then((response) => this.handleApiResponse(response))
            .then((result) => <T>result);
    }

    public put<T, U>(url: string, data: U, options?: any): Promise<T> {
        options = this.getUrlAndOptions(url, options);
        options.method = 'PUT';
        options.body = this.getDataFromModel(data, options);
        return fetch(options.url, options)
            .then((response) => this.handleApiResponse(response))
            .then((result) => <T>result);
    }
}
