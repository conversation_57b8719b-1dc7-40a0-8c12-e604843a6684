<template>
    <div class="full-width declaration-attachments">
        <div class="full-width declaration-attachments-content">
            <div v-if="hasAttachments" class="full-width attachments-list">
                <div v-for="(attachment, index) in declaration.attachments" :key="index" class="attachment-container">
                    <q-chip
                        size="14px"
                        :clickable="true"
                        :v-model="attachment"
                        text-color="white"
                        :class="attachment?.id ? 'bg-primary' : 'bg-blue-grey-6'"
                        :label="attachment.title || ''"
                        :title="attachment.fileName"
                        @click="viewAttachment($event, attachment)"
                    />
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import { defineComponent, PropType } from 'vue';
import DeclarationHelper from './DeclarationHelper';
import { Declaration } from '../../models/m1/Entities/Declaration';
import { DeclarationAttachment } from '@/m1Module/models/m1/Entities/DeclarationAttachment';
import { getM1Store, IM1Store } from '@/m1Module/stores/M1Store';

export default defineComponent({
    name: 'declaration-attachments',
    mixins: [DeclarationHelper],
    props: {
        declaration: {
            type: Declaration,
            default: null
        },
        store: {
            type: Object as PropType<IM1Store>,
            default: () => getM1Store()
        }
    },
    computed: {
        hasAttachments(): boolean {
            return this.declaration?.attachments?.length > 0;
        }
    },
    methods: {
        async viewAttachment(event: Event, attachment: DeclarationAttachment) {
            if (event) {
                event.stopPropagation();
                event.preventDefault();
            }
            this.viewDeclarationAttachment(this.declaration, attachment);
        }
    }
});
</script>

<style scoped>
.declaration-attachments-content {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: start;
    align-items: start;
}
.declaration-attachments-content .attachments-list {
    margin: 10px 0 0 0;
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: start;
    align-items: center;
    flex-wrap: wrap;
}
.declaration-attachments-content .attachments-list .attachment-container {
    width: auto;
}
.declaration-attachments-content .attachments-add {
    margin: 0 0 10px 0;
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: start;
    align-items: center;
}
</style>
