<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1670068371173" clover="3.2.0">
  <project timestamp="1670068371174" name="All files">
    <metrics statements="745" coveredstatements="181" conditionals="689" coveredconditionals="122" methods="218" coveredmethods="49" elements="1652" coveredelements="352" complexity="0" loc="745" ncloc="745" packages="16" files="32" classes="32"/>
    <package name="auth.models">
      <metrics statements="34" coveredstatements="21" conditionals="50" coveredconditionals="19" methods="5" coveredmethods="3"/>
      <file name="Auth.ts" path="C:/Development/vue3-app-template-vite/src/auth/models/Auth.ts">
        <metrics statements="34" coveredstatements="21" conditionals="50" coveredconditionals="19" methods="5" coveredmethods="3"/>
        <line num="5" count="4" type="stmt"/>
        <line num="6" count="4" type="stmt"/>
        <line num="9" count="4" type="cond" truecount="1" falsecount="1"/>
        <line num="10" count="4" type="cond" truecount="1" falsecount="3"/>
        <line num="11" count="4" type="cond" truecount="1" falsecount="2"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="20" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="21" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="22" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="27" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="28" count="2" type="stmt"/>
        <line num="33" count="2" type="stmt"/>
        <line num="34" count="2" type="stmt"/>
        <line num="35" count="2" type="stmt"/>
        <line num="36" count="2" type="stmt"/>
        <line num="37" count="2" type="stmt"/>
        <line num="38" count="2" type="stmt"/>
        <line num="41" count="2" type="cond" truecount="1" falsecount="1"/>
        <line num="42" count="2" type="cond" truecount="3" falsecount="0"/>
        <line num="43" count="2" type="cond" truecount="1" falsecount="2"/>
        <line num="44" count="2" type="cond" truecount="1" falsecount="2"/>
        <line num="45" count="2" type="cond" truecount="1" falsecount="2"/>
        <line num="46" count="2" type="cond" truecount="3" falsecount="0"/>
        <line num="47" count="2" type="cond" truecount="3" falsecount="0"/>
        <line num="48" count="2" type="cond" truecount="1" falsecount="1"/>
        <line num="49" count="0" type="stmt"/>
        <line num="54" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
      </file>
    </package>
    <package name="auth.services">
      <metrics statements="61" coveredstatements="18" conditionals="34" coveredconditionals="3" methods="18" coveredmethods="4"/>
      <file name="AuthService.ts" path="C:/Development/vue3-app-template-vite/src/auth/services/AuthService.ts">
        <metrics statements="46" coveredstatements="11" conditionals="30" coveredconditionals="2" methods="13" coveredmethods="2"/>
        <line num="6" count="1" type="stmt"/>
        <line num="9" count="1" type="stmt"/>
        <line num="10" count="1" type="stmt"/>
        <line num="13" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="14" count="1" type="stmt"/>
        <line num="16" count="1" type="stmt"/>
        <line num="20" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="21" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="32" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="45" count="0" type="stmt"/>
        <line num="49" count="1" type="stmt"/>
        <line num="50" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="51" count="1" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="68" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="72" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="78" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="87" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="94" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="100" count="1" type="stmt"/>
        <line num="102" count="1" type="stmt"/>
      </file>
      <file name="AuthStore.ts" path="C:/Development/vue3-app-template-vite/src/auth/services/AuthStore.ts">
        <metrics statements="15" coveredstatements="7" conditionals="4" coveredconditionals="1" methods="5" coveredmethods="2"/>
        <line num="6" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="19" count="1" type="stmt"/>
        <line num="20" count="1" type="stmt"/>
        <line num="21" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="25" count="1" type="stmt"/>
        <line num="29" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="30" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="38" count="1" type="stmt"/>
      </file>
    </package>
    <package name="common">
      <metrics statements="2" coveredstatements="2" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="i18n.ts" path="C:/Development/vue3-app-template-vite/src/common/i18n.ts">
        <metrics statements="2" coveredstatements="2" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="8" count="1" type="stmt"/>
        <line num="10" count="1" type="stmt"/>
      </file>
    </package>
    <package name="common.components">
      <metrics statements="63" coveredstatements="4" conditionals="48" coveredconditionals="22" methods="30" coveredmethods="3"/>
      <file name="AlertDemo.vue" path="C:/Development/vue3-app-template-vite/src/common/components/AlertDemo.vue">
        <metrics statements="63" coveredstatements="4" conditionals="48" coveredconditionals="22" methods="30" coveredmethods="3"/>
        <line num="2" count="3" type="stmt"/>
        <line num="3" count="0" type="cond" truecount="2" falsecount="0"/>
        <line num="4" count="0" type="cond" truecount="2" falsecount="0"/>
        <line num="7" count="0" type="cond" truecount="2" falsecount="0"/>
        <line num="8" count="0" type="cond" truecount="2" falsecount="0"/>
        <line num="11" count="4" type="stmt"/>
        <line num="12" count="0" type="cond" truecount="2" falsecount="0"/>
        <line num="15" count="0" type="cond" truecount="2" falsecount="2"/>
        <line num="16" count="0" type="cond" truecount="2" falsecount="2"/>
        <line num="19" count="4" type="stmt"/>
        <line num="20" count="0" type="cond" truecount="2" falsecount="2"/>
        <line num="21" count="0" type="cond" truecount="2" falsecount="2"/>
        <line num="22" count="0" type="cond" truecount="2" falsecount="2"/>
        <line num="23" count="0" type="cond" truecount="2" falsecount="2"/>
        <line num="24" count="4" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="37" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="38" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="55" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="56" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="73" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="74" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="100" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
        <line num="192" count="0" type="stmt"/>
      </file>
    </package>
    <package name="common.http-client">
      <metrics statements="156" coveredstatements="10" conditionals="118" coveredconditionals="4" methods="55" coveredmethods="5"/>
      <file name="HttpClientAxios.ts" path="C:/Development/vue3-app-template-vite/src/common/http-client/HttpClientAxios.ts">
        <metrics statements="59" coveredstatements="2" conditionals="44" coveredconditionals="1" methods="25" coveredmethods="1"/>
        <line num="6" count="5" type="cond" truecount="1" falsecount="0"/>
        <line num="7" count="5" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="13" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="14" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="15" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="26" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="33" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="34" count="0" type="stmt"/>
        <line num="36" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="46" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="47" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="55" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
      </file>
      <file name="HttpClientBase.ts" path="C:/Development/vue3-app-template-vite/src/common/http-client/HttpClientBase.ts">
        <metrics statements="40" coveredstatements="1" conditionals="41" coveredconditionals="0" methods="8" coveredmethods="1"/>
        <line num="4" count="7" type="stmt"/>
        <line num="7" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="8" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="9" count="0" type="stmt"/>
        <line num="11" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="12" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="13" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="19" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="20" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="21" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="40" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="59" count="0" type="stmt"/>
        <line num="62" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="63" count="0" type="stmt"/>
        <line num="65" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="66" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="67" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="68" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
      </file>
      <file name="HttpClientFactory.ts" path="C:/Development/vue3-app-template-vite/src/common/http-client/HttpClientFactory.ts">
        <metrics statements="5" coveredstatements="5" conditionals="4" coveredconditionals="2" methods="2" coveredmethods="2"/>
        <line num="6" count="2" type="stmt"/>
        <line num="13" count="3" type="cond" truecount="1" falsecount="1"/>
        <line num="14" count="3" type="stmt"/>
        <line num="18" count="2" type="stmt"/>
        <line num="20" count="3" type="cond" truecount="1" falsecount="0"/>
      </file>
      <file name="HttpClientFetch.ts" path="C:/Development/vue3-app-template-vite/src/common/http-client/HttpClientFetch.ts">
        <metrics statements="52" coveredstatements="2" conditionals="29" coveredconditionals="1" methods="20" coveredmethods="1"/>
        <line num="5" count="2" type="cond" truecount="1" falsecount="0"/>
        <line num="6" count="2" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="12" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="13" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="14" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="17" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="24" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="28" count="0" type="stmt"/>
        <line num="31" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="33" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="37" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="38" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="55" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
      </file>
    </package>
    <package name="common.models">
      <metrics statements="14" coveredstatements="14" conditionals="16" coveredconditionals="12" methods="2" coveredmethods="2"/>
      <file name="Locale.ts" path="C:/Development/vue3-app-template-vite/src/common/models/Locale.ts">
        <metrics statements="7" coveredstatements="7" conditionals="8" coveredconditionals="4" methods="1" coveredmethods="1"/>
        <line num="2" count="24" type="stmt"/>
        <line num="3" count="24" type="stmt"/>
        <line num="4" count="24" type="stmt"/>
        <line num="7" count="24" type="cond" truecount="1" falsecount="1"/>
        <line num="8" count="24" type="cond" truecount="1" falsecount="1"/>
        <line num="9" count="24" type="cond" truecount="1" falsecount="1"/>
        <line num="10" count="24" type="cond" truecount="1" falsecount="1"/>
      </file>
      <file name="ResponseBase.ts" path="C:/Development/vue3-app-template-vite/src/common/models/ResponseBase.ts">
        <metrics statements="7" coveredstatements="7" conditionals="8" coveredconditionals="8" methods="1" coveredmethods="1"/>
        <line num="2" count="11" type="stmt"/>
        <line num="3" count="11" type="stmt"/>
        <line num="4" count="11" type="stmt"/>
        <line num="7" count="11" type="cond" truecount="2" falsecount="0"/>
        <line num="8" count="11" type="cond" truecount="2" falsecount="0"/>
        <line num="9" count="11" type="cond" truecount="2" falsecount="0"/>
        <line num="10" count="11" type="cond" truecount="2" falsecount="0"/>
      </file>
    </package>
    <package name="common.services">
      <metrics statements="1" coveredstatements="1" conditionals="2" coveredconditionals="2" methods="1" coveredmethods="1"/>
      <file name="AppService.ts" path="C:/Development/vue3-app-template-vite/src/common/services/AppService.ts">
        <metrics statements="1" coveredstatements="1" conditionals="2" coveredconditionals="2" methods="1" coveredmethods="1"/>
        <line num="7" count="1" type="cond" truecount="2" falsecount="0"/>
      </file>
    </package>
    <package name="common.utilities">
      <metrics statements="281" coveredstatements="18" conditionals="351" coveredconditionals="12" methods="61" coveredmethods="9"/>
      <file name="IAlert.ts" path="C:/Development/vue3-app-template-vite/src/common/utilities/IAlert.ts">
        <metrics statements="9" coveredstatements="6" conditionals="3" coveredconditionals="2" methods="4" coveredmethods="1"/>
        <line num="1" count="8" type="stmt"/>
        <line num="3" count="8" type="cond" truecount="2" falsecount="0"/>
        <line num="4" count="8" type="stmt"/>
        <line num="5" count="8" type="stmt"/>
        <line num="6" count="8" type="stmt"/>
        <line num="7" count="8" type="stmt"/>
        <line num="13" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="16" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
      </file>
      <file name="SweetAlert.ts" path="C:/Development/vue3-app-template-vite/src/common/utilities/SweetAlert.ts">
        <metrics statements="209" coveredstatements="1" conditionals="264" coveredconditionals="1" methods="39" coveredmethods="1"/>
        <line num="9" count="2" type="cond" truecount="1" falsecount="1"/>
        <line num="13" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="14" count="0" type="stmt"/>
        <line num="18" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="19" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="20" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="21" count="0" type="stmt"/>
        <line num="25" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="35" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="36" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="94" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="95" count="0" type="stmt"/>
        <line num="99" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="100" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="103" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="104" count="0" type="stmt"/>
        <line num="107" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="108" count="0" type="stmt"/>
        <line num="112" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="113" count="0" type="stmt"/>
        <line num="117" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="120" count="0" type="cond" truecount="0" falsecount="8"/>
        <line num="124" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="140" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="153" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="154" count="0" type="stmt"/>
        <line num="155" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="156" count="0" type="stmt"/>
        <line num="157" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="158" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="178" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="179" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="185" count="0" type="stmt"/>
        <line num="186" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="187" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="188" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="189" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="190" count="0" type="stmt"/>
        <line num="191" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="192" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="193" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="198" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="199" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="204" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="205" count="0" type="stmt"/>
        <line num="206" count="0" type="stmt"/>
        <line num="210" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="211" count="0" type="stmt"/>
        <line num="212" count="0" type="stmt"/>
        <line num="216" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="217" count="0" type="stmt"/>
        <line num="218" count="0" type="stmt"/>
        <line num="222" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="223" count="0" type="stmt"/>
        <line num="224" count="0" type="stmt"/>
        <line num="228" count="0" type="stmt"/>
        <line num="229" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="230" count="0" type="stmt"/>
        <line num="231" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="232" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="233" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="234" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="239" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="240" count="0" type="stmt"/>
        <line num="241" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="242" count="0" type="stmt"/>
        <line num="246" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="247" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="248" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="249" count="0" type="stmt"/>
        <line num="253" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="254" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="255" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="256" count="0" type="stmt"/>
        <line num="260" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="261" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="262" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="263" count="0" type="stmt"/>
        <line num="267" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="268" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="269" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="270" count="0" type="stmt"/>
        <line num="274" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="275" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="276" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="277" count="0" type="stmt"/>
        <line num="281" count="0" type="stmt"/>
        <line num="282" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="283" count="0" type="stmt"/>
        <line num="284" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="285" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="286" count="0" type="stmt"/>
        <line num="287" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="288" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="289" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="290" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="291" count="0" type="stmt"/>
        <line num="292" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="293" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="294" count="0" type="stmt"/>
        <line num="295" count="0" type="stmt"/>
        <line num="299" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="300" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="301" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="302" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="303" count="0" type="stmt"/>
        <line num="307" count="0" type="stmt"/>
        <line num="308" count="0" type="stmt"/>
        <line num="309" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="310" count="0" type="stmt"/>
        <line num="311" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="312" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="313" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="314" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="315" count="0" type="stmt"/>
        <line num="316" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="317" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="318" count="0" type="stmt"/>
        <line num="319" count="0" type="stmt"/>
        <line num="320" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="321" count="0" type="stmt"/>
        <line num="325" count="0" type="stmt"/>
        <line num="326" count="0" type="stmt"/>
        <line num="327" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="328" count="0" type="stmt"/>
        <line num="329" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="330" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="331" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="332" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="333" count="0" type="stmt"/>
        <line num="334" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="335" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="336" count="0" type="stmt"/>
        <line num="337" count="0" type="stmt"/>
        <line num="338" count="0" type="stmt"/>
        <line num="339" count="0" type="stmt"/>
        <line num="340" count="0" type="stmt"/>
        <line num="341" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="383" count="0" type="stmt"/>
        <line num="388" count="0" type="stmt"/>
        <line num="389" count="0" type="stmt"/>
        <line num="390" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="391" count="0" type="stmt"/>
        <line num="392" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="394" count="0" type="stmt"/>
        <line num="396" count="0" type="stmt"/>
        <line num="400" count="0" type="stmt"/>
        <line num="401" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="402" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="403" count="0" type="stmt"/>
        <line num="405" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="406" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="408" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="410" count="0" type="stmt"/>
        <line num="411" count="0" type="stmt"/>
        <line num="416" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="417" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="418" count="0" type="stmt"/>
        <line num="420" count="0" type="stmt"/>
        <line num="423" count="0" type="stmt"/>
        <line num="424" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="425" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="426" count="0" type="stmt"/>
        <line num="427" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="429" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="431" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="432" count="0" type="stmt"/>
        <line num="433" count="0" type="stmt"/>
        <line num="437" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="438" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="439" count="0" type="stmt"/>
        <line num="440" count="0" type="stmt"/>
        <line num="441" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="C:/Development/vue3-app-template-vite/src/common/utilities/index.ts">
        <metrics statements="63" coveredstatements="11" conditionals="84" coveredconditionals="9" methods="18" coveredmethods="7"/>
        <line num="6" count="16" type="stmt"/>
        <line num="10" count="1" type="stmt"/>
        <line num="11" count="1" type="stmt"/>
        <line num="13" count="1" type="stmt"/>
        <line num="17" count="1" type="cond" truecount="2" falsecount="0"/>
        <line num="18" count="1" type="cond" truecount="2" falsecount="3"/>
        <line num="19" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="20" count="0" type="stmt"/>
        <line num="22" count="1" type="cond" truecount="3" falsecount="1"/>
        <line num="23" count="1" type="stmt"/>
        <line num="24" count="1" type="stmt"/>
        <line num="25" count="1" type="stmt"/>
        <line num="26" count="1" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="35" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="36" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="37" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="38" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="49" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="50" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="51" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="52" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="53" count="0" type="stmt"/>
        <line num="58" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="68" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="71" count="0" type="stmt"/>
        <line num="75" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="78" count="0" type="stmt"/>
        <line num="82" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="85" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="86" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="92" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="96" count="0" type="stmt"/>
        <line num="100" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="101" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="102" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="109" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="110" count="0" type="stmt"/>
      </file>
    </package>
    <package name="config">
      <metrics statements="20" coveredstatements="17" conditionals="17" coveredconditionals="16" methods="7" coveredmethods="6"/>
      <file name="index.ts" path="C:/Development/vue3-app-template-vite/src/config/index.ts">
        <metrics statements="20" coveredstatements="17" conditionals="17" coveredconditionals="16" methods="7" coveredmethods="6"/>
        <line num="6" count="11" type="stmt"/>
        <line num="7" count="11" type="stmt"/>
        <line num="9" count="11" type="stmt"/>
        <line num="11" count="11" type="cond" truecount="3" falsecount="0"/>
        <line num="12" count="11" type="cond" truecount="3" falsecount="0"/>
        <line num="13" count="11" type="cond" truecount="3" falsecount="0"/>
        <line num="15" count="7" type="stmt"/>
        <line num="16" count="7" type="cond" truecount="2" falsecount="0"/>
        <line num="19" count="22" type="stmt"/>
        <line num="20" count="11" type="cond" truecount="2" falsecount="0"/>
        <line num="21" count="11" type="cond" truecount="2" falsecount="0"/>
        <line num="24" count="11" type="stmt"/>
        <line num="28" count="11" type="stmt"/>
        <line num="29" count="11" type="cond" truecount="1" falsecount="1"/>
        <line num="30" count="0" type="stmt"/>
        <line num="34" count="10" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="43" count="1" type="stmt"/>
        <line num="47" count="11" type="stmt"/>
      </file>
    </package>
    <package name="main.components">
      <metrics statements="12" coveredstatements="11" conditionals="2" coveredconditionals="1" methods="3" coveredmethods="2"/>
      <file name="About.vue" path="C:/Development/vue3-app-template-vite/src/main/components/About.vue">
        <metrics statements="1" coveredstatements="1" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="2" count="6" type="stmt"/>
      </file>
      <file name="Home.vue" path="C:/Development/vue3-app-template-vite/src/main/components/Home.vue">
        <metrics statements="2" coveredstatements="2" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="2" count="9" type="stmt"/>
        <line num="16" count="2" type="stmt"/>
      </file>
      <file name="PersonsList.vue" path="C:/Development/vue3-app-template-vite/src/main/components/PersonsList.vue">
        <metrics statements="4" coveredstatements="3" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="2" count="2" type="stmt"/>
        <line num="4" count="35" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="27" count="2" type="stmt"/>
      </file>
      <file name="PersonsListItem.vue" path="C:/Development/vue3-app-template-vite/src/main/components/PersonsListItem.vue">
        <metrics statements="5" coveredstatements="5" conditionals="2" coveredconditionals="1" methods="2" coveredmethods="2"/>
        <line num="2" count="3" type="stmt"/>
        <line num="4" count="3" type="stmt"/>
        <line num="6" count="6" type="stmt"/>
        <line num="8" count="6" type="stmt"/>
        <line num="26" count="3" type="stmt"/>
      </file>
    </package>
    <package name="main.models">
      <metrics statements="19" coveredstatements="19" conditionals="20" coveredconditionals="14" methods="3" coveredmethods="3"/>
      <file name="GetPersonsRequest.ts" path="C:/Development/vue3-app-template-vite/src/main/models/GetPersonsRequest.ts">
        <metrics statements="1" coveredstatements="1" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="1"/>
        <line num="5" count="8" type="stmt"/>
      </file>
      <file name="Person.ts" path="C:/Development/vue3-app-template-vite/src/main/models/Person.ts">
        <metrics statements="18" coveredstatements="18" conditionals="20" coveredconditionals="14" methods="2" coveredmethods="2"/>
        <line num="2" count="17" type="stmt"/>
        <line num="3" count="17" type="stmt"/>
        <line num="4" count="17" type="stmt"/>
        <line num="5" count="17" type="stmt"/>
        <line num="6" count="17" type="stmt"/>
        <line num="7" count="17" type="stmt"/>
        <line num="10" count="17" type="cond" truecount="1" falsecount="1"/>
        <line num="11" count="17" type="cond" truecount="2" falsecount="0"/>
        <line num="12" count="17" type="cond" truecount="1" falsecount="1"/>
        <line num="13" count="17" type="cond" truecount="1" falsecount="1"/>
        <line num="14" count="17" type="cond" truecount="2" falsecount="0"/>
        <line num="15" count="17" type="cond" truecount="2" falsecount="0"/>
        <line num="16" count="17" type="cond" truecount="2" falsecount="0"/>
        <line num="20" count="7" type="cond" truecount="1" falsecount="1"/>
        <line num="21" count="7" type="cond" truecount="1" falsecount="1"/>
        <line num="22" count="7" type="cond" truecount="1" falsecount="1"/>
        <line num="23" count="7" type="stmt"/>
        <line num="25" count="7" type="stmt"/>
      </file>
    </package>
    <package name="main.services">
      <metrics statements="8" coveredstatements="1" conditionals="6" coveredconditionals="0" methods="1" coveredmethods="0"/>
      <file name="MainService.ts" path="C:/Development/vue3-app-template-vite/src/main/services/MainService.ts">
        <metrics statements="8" coveredstatements="1" conditionals="6" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="31" count="0" type="stmt"/>
        <line num="78" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="79" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="87" count="2" type="stmt"/>
      </file>
    </package>
    <package name="main.stores">
      <metrics statements="29" coveredstatements="20" conditionals="17" coveredconditionals="11" methods="7" coveredmethods="5"/>
      <file name="StoreMain.ts" path="C:/Development/vue3-app-template-vite/src/main/stores/StoreMain.ts">
        <metrics statements="28" coveredstatements="19" conditionals="17" coveredconditionals="11" methods="7" coveredmethods="5"/>
        <line num="19" count="7" type="cond" truecount="1" falsecount="1"/>
        <line num="20" count="7" type="stmt"/>
        <line num="21" count="7" type="stmt"/>
        <line num="22" count="7" type="stmt"/>
        <line num="25" count="14" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="33" count="7" type="cond" truecount="1" falsecount="1"/>
        <line num="34" count="7" type="cond" truecount="1" falsecount="1"/>
        <line num="35" count="7" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="44" count="7" type="stmt"/>
        <line num="45" count="7" type="cond" truecount="1" falsecount="1"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="49" count="7" type="cond" truecount="3" falsecount="0"/>
        <line num="50" count="7" type="cond" truecount="1" falsecount="1"/>
        <line num="51" count="0" type="stmt"/>
        <line num="56" count="7" type="stmt"/>
        <line num="57" count="7" type="stmt"/>
        <line num="58" count="7" type="cond" truecount="3" falsecount="1"/>
        <line num="59" count="7" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="63" count="7" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="71" count="7" type="stmt"/>
        <line num="72" count="7" type="stmt"/>
      </file>
      <file name="StoreStateMain.ts" path="C:/Development/vue3-app-template-vite/src/main/stores/StoreStateMain.ts">
        <metrics statements="1" coveredstatements="1" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="4" count="7" type="stmt"/>
      </file>
    </package>
    <package name="main.views">
      <metrics statements="8" coveredstatements="7" conditionals="2" coveredconditionals="2" methods="4" coveredmethods="3"/>
      <file name="About.vue" path="C:/Development/vue3-app-template-vite/src/main/views/About.vue">
        <metrics statements="2" coveredstatements="2" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="2" count="5" type="stmt"/>
        <line num="19" count="1" type="stmt"/>
      </file>
      <file name="Home.vue" path="C:/Development/vue3-app-template-vite/src/main/views/Home.vue">
        <metrics statements="6" coveredstatements="5" conditionals="2" coveredconditionals="2" methods="4" coveredmethods="3"/>
        <line num="2" count="14" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="27" count="1" type="stmt"/>
        <line num="31" count="14" type="cond" truecount="2" falsecount="0"/>
        <line num="35" count="7" type="stmt"/>
        <line num="39" count="7" type="stmt"/>
      </file>
    </package>
    <package name="mocks">
      <metrics statements="33" coveredstatements="14" conditionals="6" coveredconditionals="4" methods="21" coveredmethods="3"/>
      <file name="MockAlert.ts" path="C:/Development/vue3-app-template-vite/src/mocks/MockAlert.ts">
        <metrics statements="18" coveredstatements="1" conditionals="0" coveredconditionals="0" methods="18" coveredmethods="0"/>
        <line num="8" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="76" count="7" type="stmt"/>
      </file>
      <file name="MockMainService.ts" path="C:/Development/vue3-app-template-vite/src/mocks/MockMainService.ts">
        <metrics statements="7" coveredstatements="5" conditionals="6" coveredconditionals="4" methods="1" coveredmethods="1"/>
        <line num="8" count="7" type="stmt"/>
        <line num="9" count="7" type="cond" truecount="1" falsecount="1"/>
        <line num="10" count="7" type="stmt"/>
        <line num="14" count="7" type="cond" truecount="3" falsecount="1"/>
        <line num="15" count="7" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
      </file>
      <file name="commonComponentMocks.ts" path="C:/Development/vue3-app-template-vite/src/mocks/commonComponentMocks.ts">
        <metrics statements="3" coveredstatements="3" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="1"/>
        <line num="8" count="6" type="stmt"/>
        <line num="10" count="35" type="stmt"/>
        <line num="15" count="6" type="stmt"/>
      </file>
      <file name="mockData.ts" path="C:/Development/vue3-app-template-vite/src/mocks/mockData.ts">
        <metrics statements="5" coveredstatements="5" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="1"/>
        <line num="4" count="3" type="stmt"/>
        <line num="5" count="3" type="stmt"/>
        <line num="6" count="15" type="stmt"/>
        <line num="7" count="15" type="stmt"/>
        <line num="8" count="15" type="stmt"/>
      </file>
    </package>
    <package name="plugins">
      <metrics statements="4" coveredstatements="4" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="dayjs.ts" path="C:/Development/vue3-app-template-vite/src/plugins/dayjs.ts">
        <metrics statements="4" coveredstatements="4" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="9" count="7" type="stmt"/>
        <line num="10" count="7" type="stmt"/>
        <line num="11" count="7" type="stmt"/>
        <line num="12" count="7" type="stmt"/>
      </file>
    </package>
  </project>
</coverage>
