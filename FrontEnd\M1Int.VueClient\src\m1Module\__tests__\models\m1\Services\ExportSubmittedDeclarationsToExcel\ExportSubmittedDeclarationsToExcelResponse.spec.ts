import { ExportSubmittedDeclarationsToExcelResponse } from '@/m1Module/models/m1/Services/ExportSubmittedDeclarationsToExcel/ExportSubmittedDeclarationsToExcelResponse';

describe('ExportSubmittedDeclarationsToExcelResponse', () => {
    let response: ExportSubmittedDeclarationsToExcelResponse;

    beforeEach(() => {
        response = new ExportSubmittedDeclarationsToExcelResponse();
    });

    it('should be valid', () => {
        expect(response).toBeTruthy();
    });

    it('should have valid data', () => {
        expect(response.success).toEqual(false);
        expect(response.result).toBeFalsy();
        expect(response.message).toBeFalsy();
    });
});
