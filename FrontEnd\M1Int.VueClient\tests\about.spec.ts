import { test, expect } from '@playwright/test';
import { locales } from './fixtures/about.fixture.json';

test.describe('About Page', () => {
    test.beforeEach(async ({ page }) => {
        await page.goto('M1Int#/about');
    });

    test.describe('should have valid structure', () => {
        test('title should be valid', async ({ page }) => {
            await expect(page).toHaveTitle('Συμφωνητικά ανάληψης τεχνικών έργων');
        });

        test('should have header', async ({ page }) => {
            const locator = page.locator('.main-header');
            await expect(locator).toBeTruthy();
        });

        test('should contain main title', async ({ page }) => {
            const locator = page.locator('.main-title');
            await expect(locator).toBeTruthy();
            await expect(locator).toBeVisible();
        });

        test('should contain main locale menu', async ({ page }) => {
            const locator = page.locator('.main-locale-menu-button');
            await expect(locator).toBeTruthy();
            await expect(locator).toBeVisible();
        });
    });

    test.describe('should support multiple locales', () => {
        locales.data.forEach((locale) => {
            test.describe(`locale ${locale.id}`, () => {
                test.beforeEach(async ({ page }) => {
                    await page.locator('.main-locale-menu-button').click();
                    await page.waitForTimeout(250);
                    await page.locator(`.main-locale-menu .locale-item.${locale.id}`).click();
                });

                test.afterEach(async ({ page }) => {
                    await page.locator('.main-locale-menu-button').click();
                    await page.waitForTimeout(250);
                    await page.locator(`.main-locale-menu .locale-item.${locales.default}`).click();
                });

                test('main drawer should contain title', async ({ page }) => {
                    const locator = page.locator('.main-drawer');
                    await expect(locator).toContainText(locale.title);
                });

                test('main header should contain main title', async ({ page }) => {
                    const locator = page.locator('.main-title');
                    await expect(locator).toContainText(locale.mainTitle);
                });

                test('main content should contain message', async ({ page }) => {
                    const locator = page.locator('.main-content');
                    await expect(locator).toBeTruthy();
                    await expect(locator).toBeVisible();
                    await expect(locator).toContainText(locale.message);
                });
            });
        });
    });
});
