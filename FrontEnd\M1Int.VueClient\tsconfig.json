{
    "compilerOptions": {
        "target": "ES2020",
        "useDefineForClassFields": true,
        "module": "ESNext",
        "lib": ["ES2020", "DOM", "DOM.Iterable"],
        "skipLibCheck": true,
        "baseUrl": ".",
        "paths": {
            "@/*": ["./src/*"]
        },

        /* Bundler mode */
        "moduleResolution": "bundler",
        "allowImportingTsExtensions": true,
        "resolveJsonModule": true,
        "isolatedModules": true,
        "noEmit": true,
        "jsx": "preserve",

        /* Linting */
        "strict": true,
        "noUnusedLocals": true,
        "noUnusedParameters": true,
        "noFallthroughCasesInSwitch": true,

        "types": ["vitest/globals"]
    },
    "include": ["package.json", "env.d.ts", "src/**/*", "src/**/*.vue", "src/**/*.json"],
    // "exclude": ["src/**/__tests__/*"],
    "references": [{ "path": "./tsconfig.node.json" }]
}
