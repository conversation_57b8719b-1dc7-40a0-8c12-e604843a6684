import dayjs from '@/plugins/dayjs';

export class LoginModel {
    public userName: string = '';
    public password: string = '';
    public isExternalProvider: boolean = false;

    constructor(options: any = null) {
        options = options || {};
        this.userName = options.userName || options.user || options.name || '';
        this.password = options.password || options.userPassword || '';
        this.isExternalProvider = options.isExternalProvider || options.isExternal || false;
    }
}

export class RegistrationModel {
    public userName: string = '';
    public password: string = '';

    constructor(options: any = null) {
        options = options || {};
        this.userName = options.name || options.userName || '';
        this.password = options.password || options.userPassword || '';
    }
}

export enum UserAuthType {
    Local = 0,
    AzureAd = 1,
    ExternalProvider = 2
}

export class UserAuthData {
    public userAuthType: UserAuthType = UserAuthType.Local;
    public userId: string = '';
    public userName: string = '';
    public userEmail: string = '';
    public userDepartment: string = '';
    public authToken: string = '';
    public authTokenExpiresAt: Date | null = null;

    constructor(options: any = null) {
        options = options || {};
        this.userAuthType = options.userAuthType || options.authType || UserAuthType.Local;
        this.userId = options.userId || options.id || '';
        this.userName = options.userName || options.name || '';
        this.userEmail = options.userEmail || options.email || '';
        this.userDepartment = options.userDepartment || options.department || '';
        this.authToken = options.token || options.authToken || '';
        const authTokenExpiresAt = options.authTokenExpiresAt || options.tokenExpiresAt || options.expiresAt;
        if (authTokenExpiresAt) {
            this.authTokenExpiresAt = dayjs(authTokenExpiresAt).toDate();
        }
    }

    public hasExpired(): boolean {
        if (!this.authToken || !this.authTokenExpiresAt) return true;
        const now = dayjs();
        const expiration = dayjs(this.authTokenExpiresAt);
        if (!now.isBefore(expiration)) return true;
        return false;
    }
}
