
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for auth/models/Auth.ts</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> / <a href="index.html">auth/models</a> Auth.ts</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">62.85% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>22/35</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">38% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>19/50</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">60% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>3/5</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">61.76% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>21/34</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input oninput="onInput()" type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line medium'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a></td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">import dayjs from '@/plugins/dayjs';
&nbsp;
&nbsp;
export class LoginModel {
    public userName: string = '';
    public password: string = '';
&nbsp;
    constructor(options: any = <span class="branch-0 cbranch-no" title="branch not covered" >null) {</span>
        options = options || <span class="branch-1 cbranch-no" title="branch not covered" >{};</span>
        this.userName = options.userName || <span class="branch-1 cbranch-no" title="branch not covered" >options.user || <span class="branch-2 cbranch-no" title="branch not covered" >o</span>ptions.name || <span class="branch-3 cbranch-no" title="branch not covered" >'</span>';</span>
        this.password = options.password || <span class="branch-1 cbranch-no" title="branch not covered" >options.userPassword || <span class="branch-2 cbranch-no" title="branch not covered" >'</span>';</span>
    }
}
&nbsp;
export class RegistrationModel {
    public userName: string = <span class="cstat-no" title="statement not covered" >'';</span>
    public password: string = <span class="cstat-no" title="statement not covered" >'';</span>
&nbsp;
<span class="fstat-no" title="function not covered" >    constructor(o</span>ptions: any = <span class="branch-0 cbranch-no" title="branch not covered" >null) {</span>
<span class="cstat-no" title="statement not covered" >        options = options || {};</span>
<span class="cstat-no" title="statement not covered" >        this.userName = options.name || options.userName || '';</span>
<span class="cstat-no" title="statement not covered" >        this.password = options.password || options.userPassword || '';</span>
    }
}
&nbsp;
&nbsp;
export enum UserAuthType {
    Local = 0
    // , AzureAd = 1
}
&nbsp;
export class UserAuthData {
    public userAuthType: UserAuthType = UserAuthType.Local;
    public userId: string = '';
    public userName: string = '';
    public userEmail: string = '';
    public authToken: string = '';
    public authTokenExpiresAt: Date | null = null;
&nbsp;
    constructor(options: any = <span class="branch-0 cbranch-no" title="branch not covered" >null) {</span>
        options = options || <span class="branch-1 cbranch-no" title="branch not covered" >{};</span>
        this.userAuthType = options.userAuthType || options.authType || UserAuthType.Local;
        this.userId = options.userId || <span class="branch-1 cbranch-no" title="branch not covered" >options.id || <span class="branch-2 cbranch-no" title="branch not covered" >'</span>';</span>
        this.userName = options.userName || <span class="branch-1 cbranch-no" title="branch not covered" >options.name || <span class="branch-2 cbranch-no" title="branch not covered" >'</span>';</span>
        this.userEmail = options.userEmail || <span class="branch-1 cbranch-no" title="branch not covered" >options.email || <span class="branch-2 cbranch-no" title="branch not covered" >'</span>';</span>
        this.authToken = options.token || options.authToken || '';
        const authTokenExpiresAt = options.authTokenExpiresAt || options.tokenExpiresAt || options.expiresAt;
        <span class="missing-if-branch" title="if path not taken" >I</span>if (authTokenExpiresAt) {
<span class="cstat-no" title="statement not covered" >            this.authTokenExpiresAt = dayjs(authTokenExpiresAt).utc().toDate();</span>
        }
    }
&nbsp;
    public <span class="fstat-no" title="function not covered" >hasExpired(): boolean {</span>
<span class="cstat-no" title="statement not covered" >        if (!this.authToken || !this.authTokenExpiresAt)</span>
<span class="cstat-no" title="statement not covered" >            return true;</span>
        const now: Date = <span class="cstat-no" title="statement not covered" >dayjs().utc().toDate();</span>
        const expiration = <span class="cstat-no" title="statement not covered" >this.authTokenExpiresAt;</span>
<span class="cstat-no" title="statement not covered" >        if (now &gt;= expiration)</span>
<span class="cstat-no" title="statement not covered" >            return true;</span>
<span class="cstat-no" title="statement not covered" >        return false;</span>
    }
}
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2022-12-03T11:52:51.044Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    