import { RequestBase } from '@/common/models/RequestBase';

export class GetDeclarationAttachmentRequest extends RequestBase {
    // public applicantAfm: string | null = null;
    // public year: number | null = null;
    public declarationId: number | null = null;
    public declarationAttachmentId: number | null = null;

    public constructor(options?: any) {
        super();
        options = options || {};
        // this.applicantAfm = options.applicantAfm || null;
        // this.year = options.year || null;
        this.declarationId = options.declarationId || null;
        this.declarationAttachmentId = options.declarationAttachmentId || null;
    }
}
