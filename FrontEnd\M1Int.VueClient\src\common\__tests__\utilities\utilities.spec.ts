import { getNewUID, sleep, goToUrl } from '@/common/utilities';

describe('utilities', () => {
    describe('getNewUID', () => {
        it('getNewUID is valid', () => {
            expect(getNewUID).toBeTruthy();
            expect(typeof getNewUID).toEqual('function');
        });

        it('getNewUID returns valid result', () => {
            const id = getNewUID();
            expect(id).toBeTruthy();
            expect(id.length).toBeGreaterThan(0);
        });
    });

    describe('sleep', () => {
        it('sleep is valid', () => {
            expect(sleep).toBeTruthy();
            expect(typeof sleep).toEqual('function');
        });

        it('sleep trully sleeps', async () => {
            const t1 = Date.now();
            await sleep(10);
            const t2 = Date.now();
            expect(t2 - t1).toBeGreaterThanOrEqual(10);
        });
    });

    describe('goToUrl', () => {
        it('goToUrl is valid', () => {
            expect(goToUrl).toBeTruthy();
            expect(typeof goToUrl).toEqual('function');
        });

        it('goToUrl goes to url', async () => {
            const result = await goToUrl();
            expect(result).toBeTruthy();
        });
    });
});
