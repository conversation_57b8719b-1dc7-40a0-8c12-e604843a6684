import { shallowMount, VueWrapper } from '@vue/test-utils';
import AboutComponent from '@/main/components/About.vue';
import { commonComponentMocks } from '@/mocks/commonComponentMocks';

describe('AboutComponent', () => {
    let wrapper: VueWrapper;

    beforeEach(() => {
        wrapper = shallowMount(AboutComponent, {
            global: {
                mocks: commonComponentMocks
            }
        });
    });

    afterEach(() => {});

    it('should mount', () => {
        expect(wrapper).toBeTruthy();
        expect(wrapper.vm).toBeTruthy();
        expect(wrapper.vm.$el).toBeTruthy();
    });

    it('element should be valid', () => {
        expect(wrapper.vm.$el.classList).toBeTruthy();
        expect(Array.from(wrapper.vm.$el.classList)).toContain('about-component');
    });

    it('should have valid $t property', () => {
        expect(wrapper.vm.$t).toBeTruthy();
        expect(typeof wrapper.vm.$t).toEqual('function');
    });

    it('$t should return valid result', () => {
        expect(wrapper.vm.$t('AboutComponent')).toEqual('AboutComponent');
    });

    it('should have valid $alert property', () => {
        expect((wrapper.vm as any).$alert).toBeTruthy();
    });

    it('element should contain aade title', () => {
        expect(wrapper.text()).toContain('aadeTitle');
    });

    it('element should contain application title', () => {
        expect(wrapper.text()).toContain('applicationDescription');
    });
});
