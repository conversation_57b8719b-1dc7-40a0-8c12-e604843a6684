<template>
    <div v-if="menuItem && validAuthenticated" class="main-nav-item">
        <q-item
            v-if="menuItem.items.length <= 0"
            clickable
            v-ripple
            :href="menuItem.href || undefined"
            :target="menuItem.target || undefined"
            :to="menuItem.to"
            :id="menuItem.id"
            :class="menuItem.class"
            role=""
            :aria-label="$t(menuItem.title)"
            :disable="isDisabled"
            @click.stop="itemClick"
        >
            <q-item-section avatar>
                <q-icon :name="menuItem.icon" />
            </q-item-section>
            <q-item-section>
                {{ $t(menuItem.title) }}
            </q-item-section>
        </q-item>
        <q-expansion-item
            v-else
            expand-separator
            :icon="menuItem.icon"
            :label="$t(menuItem.title)"
            :caption="menuItem.subtitle"
            role=""
            :aria-label="$t(menuItem.title)"
        >
            <menu-item v-for="childItem in menuItem.items" :key="childItem.id" :menuItem="childItem" class="q-ml-sm" />
        </q-expansion-item>
    </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import type { PropType } from 'vue';
import { getCommonStore, ICommonStore } from '@/common/stores/Store';

export default defineComponent({
    name: 'menu-item',
    props: {
        commonStore: {
            type: Object as PropType<ICommonStore>,
            default: () => getCommonStore()
        },
        menuItem: {
            type: Object,
            default: null
        }
    },
    emits: ['click'],
    computed: {
        isUserAuthenticated(): boolean {
            return !!this.commonStore?.authData?.userName === true;
        },
        validAuthenticated(): boolean {
            if (!this.menuItem) return false;
            const isAuthenticated = this.isUserAuthenticated;
            if (this.menuItem?.ifAuthenticated && !isAuthenticated) return false;
            if (this.menuItem?.ifNotAuthenticated && isAuthenticated) return false;
            return true;
        },
        isDisabled(): boolean {
            if (!this.menuItem) return false;
            if (this.menuItem.disabled) return true;
            const isAuthenticated = this.isUserAuthenticated;
            if (this.menuItem.disabledIfAuthenticated && isAuthenticated) return true;
            if (this.menuItem.disabledIfNotAuthenticated && !isAuthenticated) return true;
            return false;
        }
    },
    methods: {
        itemClick(event: Event): void {
            if (this.menuItem?.clickHandler) {
                event.preventDefault();
                event.stopPropagation();
                this.menuItem?.clickHandler(this);
            } else if (this.menuItem.to) {
                this.$router?.push({ path: this.menuItem.to });
            }
            this.$emit('click', this);
        }
    }
});
</script>

<style>
.main-nav-item .q-item {
    cursor: pointer;
}
.main-nav-item .menu-item-header {
    height: 70px;
    background-color: var(--palette-secondary) !important;
    color: white !important;
    font-size: 1.5em;
    font-weight: bold;
    text-align: center;
}
.main-nav-item .q-item.q-router-link--active,
.main-nav-item .q-item--active {
    /* color: #006ea1; */
    color: var(--palette-primary-medium);
}
.main-nav-item .q-item__section--avatar {
    min-width: 40px;
}
</style>
