import { Locale } from '@/common/models/Locale';

const dataEN: any = {
    id: 'en',
    title: 'english'
};

const dataEL: any = {
    id: 'en',
    title: 'english'
};

describe('Locale', () => {
    describe('English', () => {
        let locale: Locale;

        beforeEach(() => {
            locale = new Locale(dataEN);
        });

        it('should be valid', () => {
            expect(locale).toBeTruthy();
        });

        it('should have valid data', () => {
            expect(locale.id).toEqual(dataEN.id);
            expect(locale.title).toEqual(dataEN.title);
        });
    });

    describe('Greek', () => {
        let locale: Locale;

        beforeEach(() => {
            locale = new Locale(dataEL);
        });

        it('should be valid', () => {
            expect(locale).toBeTruthy();
        });

        it('should have valid data', () => {
            expect(locale.id).toEqual(dataEL.id);
            expect(locale.title).toEqual(dataEL.title);
        });
    });
});
