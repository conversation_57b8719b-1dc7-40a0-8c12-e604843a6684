<template>
    <div class="full-width q-mx-auto login-form">
        <div id="login-title" class="q-mt-lg text-center">
            <div class="text-h4">{{ $t('login') }}</div>
        </div>
        <div class="q-ma-none q-pa-none row justify-center items-center">
            <q-card flat outlined class="q-py-md q-px-md" style="max-width: 600px; min-height: 150px">
                <div class="row">
                    <div class="col-12">
                        <FieldEditText
                            class="q-mt-sm"
                            fieldId="userName"
                            :fieldTitle="$t('userName')"
                            :modelObject="model"
                            :validationObject="validationObject.model"
                            prependIcon="person"
                            @keydown.enter="loginIfValid($event)"
                        />
                    </div>
                    <div class="col-12 q-mt-sm">
                        <FieldEditText
                            class="q-mt-sm"
                            :type="passwordVisible ? 'text' : 'password'"
                            fieldId="password"
                            :fieldTitle="$t('password')"
                            :modelObject="model"
                            :validationObject="validationObject.model"
                            prependIcon="vpn_key"
                            :appendIcon="passwordVisible ? 'visibility_off' : 'visibility'"
                            @click:append="togglePasswordVisible"
                            @keydown.enter="loginIfValid($event)"
                        />
                    </div>
                </div>
                <div class="login-actions">
                    <q-btn id="login-button" class="q-mx-md" color="primary" :disabled="!isValid" @click="login">
                        <q-icon left name="login" />
                        {{ $t('login') }}
                    </q-btn>
                </div>
            </q-card>
        </div>
    </div>
</template>

<script lang="ts">
import { defineComponent, Ref } from 'vue';
import { LoginModel } from '../models/Auth';
import { useVuelidate, Validation } from '@vuelidate/core';
import { required } from '@vuelidate/validators';
import FieldEditText from '@/common/components/form/FieldEditText.vue';
import authService from '../services/AuthService';

export default defineComponent({
    name: 'login-component',
    components: {
        FieldEditText
    },
    validations() {
        return {
            model: {
                userName: { required },
                password: { required }
            }
        };
    },
    data() {
        const dataObj: {
            model: LoginModel;
            validationObject: Ref<Validation>;
            passwordVisible: boolean;
        } = {
            model: new LoginModel(),
            validationObject: useVuelidate(),
            passwordVisible: false
        };
        return dataObj;
    },
    computed: {
        title(): string {
            if (this.$t) return this.$t('login').toString();
            else return 'Login';
        },
        isValid(): boolean {
            if (!this.model.userName || !this.model.password) return false;
            return !this.validationObject.$invalid && !this.validationObject.$anyError;
        }
    },
    methods: {
        togglePasswordVisible() {
            this.passwordVisible = !this.passwordVisible;
        },
        async validate() {
            this.validationObject.$touch();
            return this.isValid;
        },
        resumeFocus() {
            if (!this.isValid) {
                if (!this.model.userName || this.validationObject.model?.userName?.$invalid) {
                    setTimeout(() => document.getElementById('userName')?.focus(), 100);
                } else if (!this.model.password || this.validationObject.model?.password?.$invalid) {
                    setTimeout(() => document.getElementById('password')?.focus(), 100);
                }
            }
        },
        async loginIfValid(event?: Event) {
            if (event) {
                event.preventDefault();
                event.stopPropagation();
            }
            this.validate();
            if (!this.isValid) {
                this.resumeFocus();
                return;
            }
            await this.login();
        },
        async login(event?: Event) {
            if (event) {
                event.preventDefault();
                event.stopPropagation();
            }
            const isValid: boolean = await this.validate();
            if (!isValid) {
                await this.$alert.messageBoxWarning({
                    title: this.title,
                    message: this.$t('formIsInvalid')
                });
                this.resumeFocus();
                return;
            }
            this.$alert.progress({
                title: this.title,
                message: this.$t('userAuthentication').toString()
            });
            try {
                await authService.login(this.model.userName, this.model.password);
                this.$alert.close();
                this.$nextTick(() => this.$router.push({ name: 'login-callback' }));
            } catch (ex: any) {
                // console.error(ex);
                this.$alert.close();
                // this.$messageBus.emit(LOGIN_FAILED);
                await this.$alert.messageBoxWarning({
                    title: this.title,
                    message: this.$t('userAuthenticationFailure')
                });
                this.resumeFocus();
            }
        }
    }
});
</script>

<style scoped>
.login-actions {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-top: 16px;
}
.login-actions button,
.login-actions .q-btn {
    width: 206px;
    margin: 16px auto;
}

@media (min-width: 500px) {
    .login-actions {
        flex-direction: row;
    }
    .login-actions button,
    .login-actions .q-btn {
        width: auto;
        margin: 16px 16px;
    }
}
</style>
