<template>
    <q-expansion-item
        expand-separator
        class="full-width tax-entity-panel"
        header-class="bg-grey-4"
        v-model="isPanelExpanded"
    >
        <template v-slot:header>
            <div class="full-width row no-wrap justify-start items-center">
                <div>
                    <q-icon
                        name="portrait"
                        size="sm"
                        class="text-grey-7 tax-entity-icon"
                        @click.stop="toggleExpansion"
                    />
                </div>
                <div>
                    <div v-if="declaration" class="q-px-md row wrap justify-start items-center tax-entity-header">
                        <div>
                            <span class="title">{{ title }}</span>
                            <span>&nbsp;:&nbsp;</span>
                        </div>
                        <div class="row wrap justify-start items-center">
                            <div class="q-mr-xs">{{ declaration.submitterDescription }}</div>
                            <!-- <div v-if="declaration.submitterAfm" class="q-mx-xs row justify-start items-center">
                                <div>(</div>
                                <div>{{ $t('afm') }}</div>
                                &nbsp;
                                <div>{{ declaration.submitterAfm}}</div>
                                <div>)</div>
                            </div> -->
                        </div>
                    </div>
                </div>
            </div>
        </template>
        <template v-slot:default>
            <div class="q-ma-0 q-pa-xs tax-entity-panels">
                <declaration-tax-entity-data-card :declaration="declaration" />
            </div>
        </template>
    </q-expansion-item>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import type { PropType } from 'vue';
import { Declaration } from '@/m1Module/models/m1/Entities/Declaration';
import DeclarationTaxEntityDataCard from './DeclarationTaxEntityDataCard.vue';

export default defineComponent({
    name: 'tax-entity-panel',
    components: {
        DeclarationTaxEntityDataCard
    },
    props: {
        declaration: {
            type: Object as PropType<Declaration | null>,
            default: null
        },
        title: {
            type: String,
            default: null
        },
        isExpanded: {
            type: Boolean,
            default: false
        }
    },
    data() {
        const dataObj: {
            isPanelExpanded: boolean;
        } = {
            isPanelExpanded: false
        };
        return dataObj;
    },
    methods: {
        toggleExpansion() {
            this.isPanelExpanded = !this.isPanelExpanded;
        }
    },
    created() {
        this.isPanelExpanded = this.isExpanded;
    }
});
</script>

<style scoped>
.tax-entity-panel.q-expansion-item {
    border: 1px solid #e7e7e7;
    border-radius: 2px;
}
.tax-entity-panel .tax-entity-icon {
    cursor: pointer;
}
.tax-entity-panel .tax-entity-header {
    color: var(--palette-primary-dark);
    font-size: 1em;
    font-weight: 600;
}
</style>
