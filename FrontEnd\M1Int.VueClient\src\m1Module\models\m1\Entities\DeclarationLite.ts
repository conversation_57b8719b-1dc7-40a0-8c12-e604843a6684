import dayjs from '@/plugins/dayjs';
import { SubmissionModeEnum } from './Declaration';

export class DeclarationLite {
    public id?: number;
    public m1Afm: string | null = null;

    public m1SurNameA: string | null = null;
    public m1SurNameB: string | null = null;
    public m1Name: string | null = null;

    public m1BirthDate: Date | null = null;
    public m1IdKind: number | null = null;
    public m1IdNumber: string | null = null;

    public m1Created: Date | null = null;
    public m1UpdatedEmpl: Date | null = null;
    public m1ReplacedBy: number | null = null;

    public m1SubmissionMode: SubmissionModeEnum | null = null;
    public m1RepresentativeAfm: string | null = null;

    public constructor(options?: any) {
        options = options || {};
        this.id = options.id || undefined;
        this.m1Afm = options.m1Afm || null;

        this.m1SurNameA = options.m1SurNameA || null;
        this.m1SurNameB = options.m1SurNameB || null;
        this.m1Name = options.m1Name || null;

        if (options.m1BirthDate) this.m1BirthDate = dayjs(options.m1BirthDate).toDate();

        this.m1IdKind = options.m1IdKind || null;
        this.m1IdNumber = options.m1IdNumber || null;

        if (options.m1Created) this.m1Created = dayjs(options.m1Created).toDate();
        if (options.m1UpdatedEmpl) this.m1UpdatedEmpl = dayjs(options.m1UpdatedEmpl).toDate();
        this.m1ReplacedBy = options.m1ReplacedBy || null;

        this.m1SubmissionMode = options.m1SubmissionMode;   
        this.m1RepresentativeAfm = options.m1RepresentativeAfm || null;
    }

    public get m1CreatedText(): string {
        // if (!this.m1Created) return '';
        // return dayjs(this.m1Created).format('L');
        const dt: Date | null = this.m1Created;
        if (!dt) return '';
        const dtjs = dayjs(dt);
        return `${dtjs.format('L')} ${dtjs.format('HH:mm:ss')}`;
    }

    public get m1UpdatedEmplText(): string {
        // if (!this.m1UpdatedEmpl) return '';
        // return dayjs(this.m1UpdatedEmpl).format('L');
        const dt: Date | null = this.m1UpdatedEmpl;
        if (!dt) return '';
        const dtjs = dayjs(dt);
        return `${dtjs.format('L')} ${dtjs.format('HH:mm:ss')}`;
    }

    public get m1SubmissionModeText(): string {
        if (this.m1SubmissionMode === undefined || this.m1SubmissionMode === null) return 'Undefined';
        return SubmissionModeEnum[this.m1SubmissionMode];
    }

    public get applicantName(): string {
        let res: string = this.m1SurNameA || '';
        if (this.m1SurNameB) {
            if (res) res += ' ';
            res += this.m1SurNameB;
        }
        if (this.m1Name) {
            if (res) res += ' ';
            res += this.m1Name;
        }
        return res;
    }

    public get applicantDescription(): string {
        let res: string = this.applicantName;
        if (this.m1Afm) res += ` (${this.m1Afm})`;
        return res;
    }
}
