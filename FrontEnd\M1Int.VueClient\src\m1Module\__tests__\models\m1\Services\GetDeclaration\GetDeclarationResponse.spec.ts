import { GetDeclarationResponse } from '@/m1Module/models/m1/Services/GetDeclaration/GetDeclarationResponse';

describe('GetDeclarationResponse', () => {
    let response: GetDeclarationResponse;

    beforeEach(() => {
        response = new GetDeclarationResponse();
    });

    it('should be valid', () => {
        expect(response).toBeTruthy();
    });

    it('should have valid data', () => {
        expect(response.success).toEqual(false);
        expect(response.result).toBeFalsy();
        expect(response.message).toBeFalsy();
    });
});
