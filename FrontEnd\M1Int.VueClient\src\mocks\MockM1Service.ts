import { IM1Service } from '@/m1Module/services/M1Service';
import { MOCK_DECLARATIONS_OPTIONS } from './mockData';
import { GetDeclarationReportRequest } from '@/m1Module/models/m1/Services/GetDeclarationReport/GetDeclarationReportRequest';
import { GetDeclarationRequest } from '@/m1Module/models/m1/Services/GetDeclaration/GetDeclarationRequest';
import { GetDeclarationResponse } from '@/m1Module/models/m1/Services/GetDeclaration/GetDeclarationResponse';
import { GetDeclarationAttachmentRequest } from '@/m1Module/models/m1/Services/GetDeclarationAttachment/GetDeclarationAttachmentRequest';
import { GetSubmittedDeclarationsLiteRequest } from '@/m1Module/models/m1/Services/GetSubmittedDeclarationsLite/GetSubmittedDeclarationsLiteRequest';
import { GetSubmittedDeclarationsLiteResponse } from '@/m1Module/models/m1/Services/GetSubmittedDeclarationsLite/GetSubmittedDeclarationsLiteResponse';
import { GetDeclarationsOptionsResponse } from '@/m1Module/models/m1/Services/GetDeclarationsOptions/GetDeclarationsOptionsResponse';
import { ExportSubmittedDeclarationsToCsvRequest } from '@/m1Module/models/m1/Services/ExportSubmittedDeclarationsToCsv/ExportSubmittedDeclarationsToCsvRequest';
import { ExportSubmittedDeclarationsToCsvResponse } from '@/m1Module/models/m1/Services/ExportSubmittedDeclarationsToCsv/ExportSubmittedDeclarationsToCsvResponse';
import { ExportSubmittedDeclarationsToExcelRequest } from '@/m1Module/models/m1/Services/ExportSubmittedDeclarationsToExcel/ExportSubmittedDeclarationsToExcelRequest';

export class MockM1Service implements IM1Service {
    public async getDeclarationsOptions(/* request: GetDeclarationsOptionsRequest */): Promise<GetDeclarationsOptionsResponse> {
        try {
            const response: GetDeclarationsOptionsResponse = new GetDeclarationsOptionsResponse({
                success: true,
                result: MOCK_DECLARATIONS_OPTIONS
            });
            return response;
        } catch (ex: any) {
            console.error(ex);
            throw ex;
        }
    }

    async getDeclaration(request: GetDeclarationRequest): Promise<GetDeclarationResponse> {
        try {
            if (!request?.id) throw new Error('Invalid request!');
            throw new Error('Not implemented!');
        } catch (ex: any) {
            console.error(ex);
            throw ex;
        }
    }

    public async getSubmittedDeclarationsLite(
        request: GetSubmittedDeclarationsLiteRequest
    ): Promise<GetSubmittedDeclarationsLiteResponse> {
        if (!(request?.applicantAfm && request?.declarationYear) && !request?.declarationId)
            throw new Error('getSubmittedDeclarationsLite => invalid request!');
        const response: GetSubmittedDeclarationsLiteResponse = new GetSubmittedDeclarationsLiteResponse({
            success: true,
            result: []
        });
        return response;
    }

    public async exportSubmittedDeclarationsToCsv(
        request: ExportSubmittedDeclarationsToCsvRequest
    ): Promise<ExportSubmittedDeclarationsToCsvResponse> {
        try {
            if (!request?.applicantAfm) throw new Error('exportSubmittedDeclarationsToCsv => invalid request!');
            const response: ExportSubmittedDeclarationsToCsvResponse = new GetSubmittedDeclarationsLiteResponse({
                success: true,
                result: []
            });
            return response;
        } catch (ex: any) {
            console.error(ex);
            throw ex;
        }
    }

    public async exportSubmittedDeclarationsToExcel(request: ExportSubmittedDeclarationsToExcelRequest): Promise<any> {
        try {
            if (!request) throw new Error('exportSubmittedDeclarationsToExcel => invalid request!');
            return [];
        } catch (ex: any) {
            console.error(ex);
            throw ex;
        }
    }

    public async getDeclarationAttachment(request: GetDeclarationAttachmentRequest): Promise<any> {
        try {
            if (!request)
                throw new Error('Invalid getDeclarationAttachment request!');
            throw new Error('Not implemented!');
        } catch (ex: any) {
            console.error(ex);
            throw ex;
        }
    }

    public async getDeclarationReport(request: GetDeclarationReportRequest): Promise<any> {
        try {
            if (!request?.id) throw new Error('Invalid request!');
            throw new Error('Not implemented!');
        } catch (ex: any) {
            console.error(ex);
            throw ex;
        }
    }
}
