import { resolve, dirname } from 'path';
import { fileURLToPath, URL } from 'node:url';
import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import VueI18nPlugin from '@intlify/unplugin-vue-i18n/vite';
// import { splitVendorChunkPlugin } from 'vite';
// import legacy from '@vitejs/plugin-legacy';
import { quasar, transformAssetUrls } from '@quasar/vite-plugin';

export default defineConfig({
    build: {
        // target: 'modules',
        // modulePreload: true,
        sourcemap: true,
        minify: process.env.NODE_ENV === 'production' ? 'esbuild' : false,
        outDir: process.env.NODE_ENV === 'production' ? 'dist/prod' : 'dist/dev',
        rollupOptions: {
            input: {
                main: resolve(__dirname, 'index.html')
                // , other: resolve(__dirname, 'other.html')
            },
            output: {
                // entryFileNames: `js/[name].${process.env.NODE_ENV === 'production' ? 'min.' : ''}js`,
                entryFileNames: `js/main.${process.env.NODE_ENV === 'production' ? 'min.' : ''}js`,
                // chunkFileNames: `js/[name].${process.env.NODE_ENV === 'production' ? 'min.' : ''}js`,
                chunkFileNames: `js/main.${process.env.NODE_ENV === 'production' ? 'min.' : ''}js`,
                // assetFileNames: `assets/[name].${process.env.NODE_ENV === 'production' ? 'min.' : ''}[ext]`
                assetFileNames: `assets/main.${process.env.NODE_ENV === 'production' ? 'min.' : ''}[ext]`
            }
        }
    },
    plugins: [
        vue({
            template: { transformAssetUrls }
        }),
        quasar({
            sassVariables: '@/common/quasar-variables.sass'
        }),
        VueI18nPlugin({
            runtimeOnly: true,
            compositionOnly: false,
            fullInstall: true,
            strictMessage: false,
            include: resolve(dirname(fileURLToPath(import.meta.url)), './src/common/locales/**')
        })
        // , splitVendorChunkPlugin()
        // , legacy()
    ],
    resolve: {
        alias: {
            '@': fileURLToPath(new URL('./src', import.meta.url))
        }
    }
});
