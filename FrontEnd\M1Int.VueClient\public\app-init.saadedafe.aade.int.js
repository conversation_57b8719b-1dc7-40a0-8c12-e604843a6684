/*
This script is imported and executed during application initialization.
Supply values to the global variables below, in order to alter application configuration.
*/

// environment variables
var environVars = {
    applicationRoot: '/saadedafe/M1Int/Apps/M1Int',
    useServerSession: true
};

// config patch
var configPatch = {
    auth: {
        externalProvider: {
            enabled: true,
            logoutUrl:
                'https://login.ggps.gsis/oam/server/logout?end_url=https://sintranet.ggps.gsis/saadedafe/M1Int/Apps/M1Int',
            userIdHeaderKey: 'OAM_REMOTE_USER',
            simulateExternalProviderUserIdHeader: false,
            loginEnabled: false
        }
    },
    api: {
        serverUrl: 'https://sintranet.ggps.gsis/saadedafe',
        rootPath: 'M1Int/api'
    },
    application: {
        locales: {
            default: 'el',
            selectionEnabled: true
        }
    }
};
