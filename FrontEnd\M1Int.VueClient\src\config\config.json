{"auth": {"externalProvider": {"enabled": false, "loginUrlSSO": "https://testlogin.gsis.gr/mylogin/login.jsp?resource_url={appServerRootUrl}", "logoutUrlSSO": "https://testlogin.gsis.gr/oam/server/logout?end_url={appServerRootUrl}", "userIdHeaderKey": "OAM_REMOTE_USER", "simulateExternalProviderUserIdHeader": false, "loginEnabled": false}}, "api": {"serverUrl": "{appServerRootUrl}", "rootPath": "api"}, "application": {"localStorage": {"localeKey": "LOCALE", "authDataKey": "AUTH_DATA"}, "locales": {"default": "el", "selectionEnabled": true}}}