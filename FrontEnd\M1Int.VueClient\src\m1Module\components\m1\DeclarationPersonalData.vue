<template>
    <div v-if="declaration" class="full-width q-px-xs row justify-center items-center view-declaration-data-content">
        <div class="full-width row declaration-data-container">
            <div class="col-12 col-sm-12 col-md-3 q-px-md">
                <field-display
                    class="full-width"
                    :modelObject="declaration"
                    fieldId="m1SurNameA"
                    :fieldTitle="$t('surNameA')"
                    :dense="true"
                />
            </div>
            <div class="col-12 col-sm-12 col-md-3 q-px-md">
                <field-display
                    class="full-width"
                    :modelObject="declaration"
                    fieldId="m1SurNameB"
                    :fieldTitle="$t('surNameB')"
                    :dense="true"
                />
            </div>
            <div class="col-12 col-sm-12 col-md-3 q-px-md">
                <field-display
                    class="full-width"
                    :modelObject="declaration"
                    fieldId="m1Name"
                    :fieldTitle="$t('name')"
                    :dense="true"
                />
            </div>
        </div>
        <div class="full-width row declaration-data-container">
            <div class="col-12 col-sm-12 col-md-3 q-px-md">
                <field-display
                    class="full-width"
                    :modelObject="declaration"
                    fieldId="m1FatherSurname"
                    :fieldTitle="$t('fathersSurName')"
                    :dense="true"
                />
            </div>
            <div class="col-12 col-sm-12 col-md-3 q-px-md">
                <field-display
                    class="full-width"
                    :modelObject="declaration"
                    fieldId="m1FatherName"
                    :fieldTitle="$t('fathersName')"
                    :dense="true"
                />
            </div>
            <div class="col-12 col-sm-12 col-md-3 q-px-md">
                <field-display
                    class="full-width"
                    :modelObject="declaration"
                    fieldId="m1MotherSurname"
                    :fieldTitle="$t('mothersSurName')"
                    :dense="true"
                />
            </div>
            <div class="col-12 col-sm-12 col-md-3 q-px-md">
                <field-display
                    class="full-width"
                    :modelObject="declaration"
                    fieldId="m1MotherName"
                    :fieldTitle="$t('mothersName')"
                    :dense="true"
                />
            </div>
        </div>
        <div class="full-width row declaration-data-container">
            <div class="col-12 col-sm-12 col-md-3 q-px-md">
                <field-display
                    class="full-width"
                    :modelObject="declaration"
                    fieldId="m1Sex"
                    :value="$t(`gender${declaration.m1SexText}`)"
                    :fieldTitle="$t('sex')"
                    :dense="true"
                />
            </div>
            <div class="col-12 col-sm-12 col-md-3 q-px-md">
                <field-display
                    class="full-width"
                    :modelObject="declaration"
                    fieldId="m1MaritalStatusDescription"
                    :fieldTitle="$t('maritalStatus')"
                    :dense="true"
                />
            </div>
            <div class="col-12 col-sm-12 col-md-3 q-px-md">
                <field-display
                    class="full-width"
                    :modelObject="declaration"
                    fieldId="m1TaxpayerStatus"
                    :value="$t(`taxpayerStatus${declaration.m1TaxpayerStatusText}`)"
                    :fieldTitle="$t('judicialSupport')"
                    :dense="true"
                />
            </div>
            <div class="col-12 col-sm-12 col-md-3 q-px-md">
                <field-display
                    class="full-width"
                    :modelObject="declaration"
                    fieldId="m1Citizenship"
                    :fieldTitle="$t('citizenShip')"
                    :dense="true"
                />
            </div>
        </div>
        <div class="full-width row declaration-data-container">
            <div class="col-12 col-sm-12 col-md-3 q-px-md">
                <field-display
                    class="full-width"
                    :modelObject="declaration"
                    fieldId="m1BirthDateText"
                    :fieldTitle="$t('dateOfBirth')"
                    :dense="true"
                />
            </div>
            <div
                v-if="declaration?.m1BirthCountry && declaration?.m1BirthCountryDescription"
                class="col-12 col-sm-12 col-md-3 q-px-md"
            >
                <field-display
                    class="full-width"
                    :modelObject="declaration"
                    fieldId="m1BirthCountryDescription"
                    :fieldTitle="$t('countryOfBirth')"
                    :dense="true"
                />
            </div>
            <div v-if="declaration?.m1BirthPlace" class="col-12 col-sm-12 col-md-3 q-px-md">
                <field-display
                    class="full-width"
                    :modelObject="declaration"
                    fieldId="m1BirthPlace"
                    :fieldTitle="$t('placeOfBirthInGreece')"
                    :dense="true"
                />
            </div>
        </div>
        <div class="full-width row declaration-data-container">
            <div class="col-12 col-sm-12 col-md-3 q-px-md">
                <field-display
                    class="full-width"
                    :modelObject="declaration"
                    fieldId="m1IdKindDescription"
                    :fieldTitle="$t('idKind')"
                    :dense="true"
                />
            </div>
            <div class="col-12 col-sm-12 col-md-3 q-px-md">
                <field-display
                    class="full-width"
                    :modelObject="declaration"
                    fieldId="m1IdNumber"
                    :fieldTitle="$t('idNumber')"
                    :dense="true"
                />
            </div>
            <div class="col-12 col-sm-12 col-md-1 q-px-md">
                <field-display
                    class="full-width"
                    :modelObject="declaration"
                    fieldId="m1IdIsssueDateText"
                    :fieldTitle="$t('issueDate')"
                    :dense="true"
                />
            </div>
            <div class="col-12 col-sm-12 col-md-1 q-px-md">
                <field-display
                    class="full-width"
                    :modelObject="declaration"
                    fieldId="m1IdEndDateText"
                    :fieldTitle="$t('endDate')"
                    :dense="true"
                />
            </div>
            <div class="col-12 col-sm-12 col-md-4 q-px-md">
                <field-display
                    class="full-width"
                    :modelObject="declaration"
                    fieldId="m1IdAuthority"
                    :fieldTitle="$t('authority')"
                    :dense="true"
                />
            </div>
        </div>
        <div
            v-if="declaration?.m1PermitKind && declaration?.m1PermitKindDescription"
            class="full-width row declaration-data-container"
        >
            <div class="col-12 col-sm-12 col-md-3 q-px-md">
                <field-display
                    class="full-width"
                    :modelObject="declaration"
                    fieldId="m1PermitKindDescription"
                    :fieldTitle="$t('permitKind')"
                    :dense="true"
                />
            </div>
            <div class="col-12 col-sm-12 col-md-3 q-px-md">
                <field-display
                    class="full-width"
                    :modelObject="declaration"
                    fieldId="m1PermitNumber"
                    :fieldTitle="$t('permitNumber')"
                    :dense="true"
                />
            </div>
            <div class="col-12 col-sm-12 col-md-1 q-px-md">
                <field-display
                    class="full-width"
                    :modelObject="declaration"
                    fieldId="m1PermitIssueDateText"
                    :fieldTitle="$t('issueDate')"
                    :dense="true"
                />
            </div>
            <div class="col-12 col-sm-12 col-md-1 q-px-md">
                <field-display
                    class="full-width"
                    :modelObject="declaration"
                    fieldId="m1PermitEndDateText"
                    :fieldTitle="$t('endDate')"
                    :dense="true"
                />
            </div>
            <div class="col-12 col-sm-12 col-md-4 q-px-md">
                <field-display
                    class="full-width"
                    :modelObject="declaration"
                    fieldId="m1PermitAuthority"
                    :fieldTitle="$t('authority')"
                    :dense="true"
                />
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { Declaration } from '../../models/m1/Entities/Declaration';
import FieldDisplay from '@/common/components/form/FieldDisplay.vue';

export default defineComponent({
    name: 'declaration-personal-data',
    components: {
        FieldDisplay
    },
    props: {
        declaration: {
            type: Declaration,
            default: null
        }
    }
});
</script>

<style>
.view-declaration-data .field-group-header {
    padding: 6px 12px;
    background-color: var(--palette-primary-dark);
    color: white;
    border-radius: 6px;
}
.view-declaration-data .q-expansion-item .q-expansion-item__toggle-icon {
    color: white;
}
.view-declaration-data .declaration-link {
    color: var(--palette-primary-medium);
    font-weight: 600;
    cursor: pointer;
}
.view-declaration-data .success--text textarea {
    color: var(--palette-success) !important;
}
.view-declaration-data .warning--text textarea {
    color: var(--palette-warning) !important;
}
.view-declaration-data .error--text textarea {
    color: var(--palette-error) !important;
}
</style>
