import { IHttpClient } from '@/common/http-client/IHttpClient';
import { createHttpClient } from '@/common/http-client/HttpClientFactory';
import dayjs from '@/plugins/dayjs';

const httpClient: IHttpClient = createHttpClient();

export interface IAppService {
    getServerCurrentDateTime(): Promise<Date | null>;
}

export class AppService implements IAppService {
    public constructor(options?: any) {
        options = options || {};
    }

    public async getServerCurrentDateTime(): Promise<Date | null> {
        try {
            const url = 'info/ApplicationClock';
            const response: any = await httpClient.get<any>(url);
            let dt: Date | null = null;
            if (response?.today) {
                dt = dayjs(response.today).toDate();
            }
            return dt;
        } catch (ex: any) {
            console.error(ex);
            throw ex;
        }
    }
}
