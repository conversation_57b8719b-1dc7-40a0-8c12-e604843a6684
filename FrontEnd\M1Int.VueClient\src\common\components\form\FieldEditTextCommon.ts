import { defineComponent } from 'vue';
import Field<PERSON>ommon from './FieldCommon';
import Field<PERSON>abel from './FieldLabel.vue';

export default defineComponent({
    name: 'field-edit-text-common',
    mixins: [FieldCommon],
    components: {
        FieldLabel
    },
    props: {
        mask: {
            type: String,
            default: ''
        },
        fillMask: {
            type: Boolean,
            default: false
        },
        maxLength: {
            type: Number,
            default: undefined
        },
        clearable: {
            type: Boolean,
            default: false
        },
        autogrow: {
            type: Boolean,
            default: false
        },
        beforeIcon: {
            type: String,
            default: null
        },
        beforeIconTooltip: {
            type: String,
            default: null
        },
        afterIcon: {
            type: String,
            default: null
        },
        afterIconTooltip: {
            type: String,
            default: null
        },
        prependIcon: {
            type: String,
            default: null
        },
        prependIconTooltip: {
            type: String,
            default: null
        },
        appendIcon: {
            type: String,
            default: null
        },
        appendIconTooltip: {
            type: String,
            default: null
        }
    }
});
