import { SweetAlert } from '@/common/utilities/SweetAlert';

describe('SweetAlert', () => {
    let alert: SweetAlert;

    beforeEach(() => {
        alert = new SweetAlert();
    });

    it('should be valid', () => {
        expect(alert).toBeTruthy();
    });

    it('locale should be valid', () => {
        expect(alert?.locale).toBeTruthy();
    });

    it('locale should equal el', () => {
        expect(alert?.locale).toEqual('el');
    });
});
