<template>
    <q-toolbar
        flat
        :height="title ? 112 : 72"
        style="border: 0 !important"
        :class="listeHeaderClass"
        class="list-header"
    >
        <div class="column justify-center items-start" style="width: 100%">
            <div v-if="title" class="full-width row justify-center items-center list-header-title">
                <slot name="title">
                    <div style="padding-top: 12px; font-size: 1.5em; font-style: bold">{{ title }}</div>
                    <div v-show="searchInfo" class="q-ml-sm" style="font-size: 1em; font-style: bold">
                        {{ searchInfo }}
                    </div>
                </slot>
            </div>
            <slot name="content">
                <div class="full-width row justify-start items-center list-header-content">
                    <div class="col col-4 col-sm-3">
                        <slot name="actions:viewOption">
                            <div class="full-width row justify-start items-center content-section-left">
                                <div
                                    v-if="viewOptionsVisible"
                                    class="full-width row wrap justify-start items-center view-options"
                                >
                                    <q-btn
                                        class="q-mx-xs"
                                        :class="{ selected: !modelObject.viewOptionTable }"
                                        flat
                                        size="md"
                                        icon="grid_view"
                                        @click="setViewOptionTable(false)"
                                    >
                                        <q-tooltip anchor="top middle" self="center middle">{{
                                            $t(VIEW_OPTION_CARDS)
                                        }}</q-tooltip>
                                    </q-btn>
                                    <q-btn
                                        class="q-mx-xs"
                                        :class="{ selected: modelObject.viewOptionTable }"
                                        flat
                                        size="md"
                                        icon="view_headline"
                                        @click="setViewOptionTable(true)"
                                    >
                                        <q-tooltip anchor="top middle" self="center middle">{{
                                            $t(VIEW_OPTION_TABLE)
                                        }}</q-tooltip>
                                    </q-btn>
                                </div>
                            </div>
                        </slot>
                    </div>
                    <slot name="actions:filter">
                        <div class="col col-4 col-sm-6">
                            <div class="full-width row justify-center items-center content-section-center">
                                <slot name="actions:filterText">
                                    <div class="full-width row wrap justify-center items-center">
                                        <div
                                            v-if="searchTitle"
                                            class="q-mr-xs search-title"
                                            style="font-size: 1.3em; font-style: bold"
                                        >
                                            {{ searchTitle }}
                                        </div>
                                        <field-edit-text
                                            v-if="searchVisible"
                                            class="q-my-xs q-mx-lg search-input"
                                            style="width: 100%; max-width: 500px"
                                            :modelObject="modelObject"
                                            :elementSuffixId="id"
                                            fieldId="filterText"
                                            :fieldTitle="$t('search')"
                                            :hideLabel="true"
                                            prependIcon="search"
                                            :prependIconTooltip="$t('searchHint')"
                                            :clearable="true"
                                            :disable="!searchEnabled"
                                            :dense="true"
                                            @valueChanged="search(true)"
                                            @keypress.enter.prevent.stop="search(false)"
                                            @click:prepend="search(false)"
                                        />
                                    </div>
                                </slot>
                                <slot name="actions:filterExtra"></slot>
                            </div>
                        </div>
                    </slot>
                    <div class="col col-4 col-sm-3">
                        <div class="full-width row wrap justify-end items-center content-section-right">
                            <slot name="actions:refresh">
                                <q-btn
                                    v-if="refreshVisible"
                                    class="q-mx-sm"
                                    round
                                    dark
                                    color="primary"
                                    icon="refresh"
                                    :disabled="!refreshEnabled"
                                    :title="$t('refresh')"
                                    @click="refresh"
                                >
                                    <q-tooltip anchor="top middle" self="center middle">{{ $t('refresh') }}</q-tooltip>
                                </q-btn>
                            </slot>
                            <slot name="actions:add">
                                <q-btn
                                    v-if="addNewVisible"
                                    class="q-mx-sm"
                                    round
                                    dark
                                    color="primary"
                                    icon="add"
                                    :disabled="!addNewEnabled"
                                    @click="addNew"
                                >
                                    <q-tooltip anchor="top middle" self="center middle">{{ $t('add') }}</q-tooltip>
                                </q-btn>
                            </slot>
                            <slot name="actions:extra"></slot>
                        </div>
                    </div>
                </div>
            </slot>
        </div>
    </q-toolbar>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import FieldEditText from '@/common/components/form/FieldEditText.vue';
import { resumeVueRouterQuery } from '@/common/utilities';

export default defineComponent({
    name: 'list-header',
    components: {
        FieldEditText
    },
    props: {
        id: { type: String, default: null },
        color: { type: String, default: 'white' },
        listeHeaderClass: { type: String, default: null },
        title: { type: String, default: null },
        searchTitle: { type: String, default: null },
        searchInfo: { type: String, default: null },
        initialFilterText: { type: String, default: null },
        addNewTitle: { type: String, default: null },
        refreshVisible: { type: Boolean, default: true },
        refreshEnabled: { type: Boolean, default: true },
        addNewVisible: { type: Boolean, default: true },
        addNewEnabled: { type: Boolean, default: true },
        searchVisible: { type: Boolean, default: true },
        searchEnabled: { type: Boolean, default: true },
        viewOptionsVisible: { type: Boolean, default: true },
        viewOptionsEnabled: { type: Boolean, default: true }
    },
    emits: ['refresh', 'addNew', 'filterTextChanged', 'viewOptionChanged'],
    data() {
        const dataObj: {
            VIEW_OPTION_CARDS: string;
            VIEW_OPTION_TABLE: string;
            modelObject: any;
        } = {
            VIEW_OPTION_CARDS: 'cards',
            VIEW_OPTION_TABLE: 'table',
            modelObject: {
                filterText: '',
                viewOption: this.VIEW_OPTION_CARDS,
                viewOptionTable: false
            }
        };
        return dataObj;
    },
    computed: {
        viewOptions(): { value: string; text: string }[] {
            return [
                { value: this.VIEW_OPTION_CARDS, text: this.$t(this.VIEW_OPTION_CARDS).toString() },
                { value: this.VIEW_OPTION_TABLE, text: this.$t(this.VIEW_OPTION_TABLE).toString() }
            ];
        }
    },
    methods: {
        refresh() {
            this.$emit('refresh');
        },

        addNew() {
            this.$emit('addNew');
        },

        search(fromInput: boolean) {
            this.filterTextChanged();
            if (!fromInput) {
                this.refresh();
            }
        },

        resumeRouterQuery() {
            if (!this.$router || !this.$route) return;
            const query = Object.assign({}, this.$route.query || {});
            if (this.modelObject.filterText) {
                query['filterText'] = this.modelObject.filterText;
            } else {
                delete query['filterText'];
            }
            if (this.modelObject.viewOption) {
                query['viewOption'] = this.modelObject.viewOption;
            } else {
                delete query['viewOption'];
            }
            resumeVueRouterQuery(this.$router, this.$route, query);
        },

        filterTextChanged() {
            this.$nextTick(this.resumeRouterQuery);
            this.$emit('filterTextChanged', this.modelObject.filterText);
        },

        viewOptionChanged() {
            if (this.modelObject.viewOption === this.VIEW_OPTION_TABLE) {
                this.modelObject.viewOptionTable = true;
            } else {
                this.modelObject.viewOptionTable = false;
            }
            this.$nextTick(this.resumeRouterQuery);
            this.$emit('viewOptionChanged', this.modelObject.viewOption);
        },

        viewOptionTableChanged() {
            if (this.modelObject.viewOptionTable) {
                this.modelObject.viewOption = this.VIEW_OPTION_TABLE;
            } else {
                this.modelObject.viewOption = this.VIEW_OPTION_CARDS;
            }
            this.viewOptionChanged();
        },

        setViewOptionTable(value: boolean) {
            this.modelObject.viewOptionTable = value;
            this.viewOptionTableChanged();
        }
    },
    mounted() {
        if (this.$route && this.$route.query) {
            if (this.$route.query['filterText']) {
                this.modelObject.filterText = this.$route.query['filterText'];
                this.filterTextChanged();
            }
            if (this.$route.query['viewOption']) {
                this.modelObject.viewOption = this.$route.query['viewOption'];
                this.viewOptionChanged();
            }
        }
        if (this.initialFilterText) {
            this.modelObject.filterText = this.initialFilterText;
            this.$nextTick(this.resumeRouterQuery);
        }
        if (this.modelObject.filterText) {
            this.refresh();
        }
    }
});
</script>

<style scoped>
.list-header .list-header-content .view-options,
.list-header .list-header-content .content-section-center,
.list-header .list-header-content .content-section-left,
.list-header .list-header-content .content-section-right {
    align-items: center !important;
}

.list-header .list-header-content .q-btn {
    width: 35px;
    min-width: 35px;
    max-width: 35px;
    height: 35px;
    min-height: 35px;
    max-height: 35px;
}

.list-header .list-header-content .view-options .q-btn {
    color: #101113;
    cursor: pointer;
    opacity: 0.5;
}

.list-header .list-header-content .view-options .q-btn.selected {
    opacity: 1;
    color: var(--palette-primary);
}

.list-header .list-header-content .actions button:hover,
.list-header .list-header-content .actions .q-btn:hover,
.list-header .list-header-content .actions button:focus,
.list-header .list-header-content .actions .q-btn:focus {
    background-color: var(--palette-primary-dark) !important;
}
</style>
