<template>
    <div class="full-width declaration-tax-entity-data-view">
        <div
            v-if="declaration"
            class="full-width q-px-xs column justify-center items-center new-declaration-tax-entities"
        >
            <declaration-tax-entity-panel
                class="q-my-xs"
                expand-icon-toggle
                :declaration="declaration"
                :title= "$t(`submissionMode`) + ' ' + $t(`submission${declaration.m1SubmissionModeText}`)"  
                :isExpanded="false"
            />
        </div>
    </div>
</template>

<script lang="ts">
import { defineComponent, PropType } from 'vue';
import DeclarationHelper from './DeclarationHelper';
import { Declaration } from '@/m1Module/models/m1/Entities/Declaration';
import DeclarationTaxEntityPanel from './DeclarationTaxEntityPanel.vue';

export default defineComponent({
    name: 'declaration-tax-entity-data',
    mixins: [DeclarationHelper],
    components: {
        DeclarationTaxEntityPanel
    },
    props: {
        declaration: {
            type: Object as PropType<Declaration | null>,
            default: null
        }
    }
});
</script>
