<template>
    <div class="full-width row justify-center items-center tax-entity-data-card">
        <div v-if="declaration" class="full-width column justify-start items-start tax-entity-data-content">
            <div v-if="declaration" class="full-width row q-px-sm">
                <div class="col-12 col-md-3 q-px-md">
                    <field-display
                        :modelObject="declaration"
                        fieldId="m1Created"
                        :value="declaration.m1CreatedText"
                        :fieldTitle="$t('m1Created')"
                        :dense="true"
                    />
                </div>
                <div class="col-12 col-md-3 q-px-md">
                    <field-display
                        :modelObject="declaration"
                        fieldId="m1UpdatedEmpl"
                        :value="declaration.m1UpdatedEmplText"
                        :fieldTitle="$t('m1UpdatedEmpl')"
                        :dense="true"
                    />
                </div>
                <div class="col-12 col-md-3 q-px-md">
                    <field-display :modelObject="declaration" fieldId="m1Afm" :fieldTitle="$t('m1Afm')" :dense="true" />
                </div>
                <div class="col-12 col-md-3 q-px-md">
                    <field-display
                        class="full-width"
                        :modelObject="declaration"
                        fieldId="m1ServiceChannelType"
                        :value="$t(`serviceChannelType${declaration.m1ServiceChannelTypeText}`)"
                        :fieldTitle="$t('serviceChannelType')"
                        :dense="true"
                    />
                </div>
            </div>
            <!-- <div v-if="declaration.submittedByRepresentative" class="full-width row q-px-sm"> -->
            <div class="full-width row q-px-sm">
                <!-- SOS AGX -->
                <div class="col-12 col-md-3 q-px-md">
                    <field-display
                        :modelObject="declaration"
                        fieldId="m1RepresentativeAfm"
                        :fieldTitle="$t('representativeAfm')"
                        :dense="true"
                    />
                </div>
                <div class="col-12 col-md-3 q-px-md">
                    <field-display
                        :modelObject="declaration"
                        fieldId="m1RepresentativeName"
                        :fieldTitle="$t('representativeName')"
                        :dense="true"
                    />
                </div>
                <div class="col-12 col-md-2 q-px-md">
                    <field-display
                        :modelObject="declaration"
                        fieldId="m1RepresentativeTelephone"
                        :fieldTitle="$t('telephone')"
                        :dense="true"
                    />
                </div>
                <div class="col-12 col-md-1 q-px-md">
                    <field-display
                        :modelObject="declaration"
                        fieldId="m1RepresentativeEmail"
                        :value="declaration.m1RepresentativeSendEmail ? $t('yes') : $t('no')"
                        :fieldTitle="$t('representativeSendEmail')"
                        :dense="true"
                    />
                </div>
                <div class="col-12 col-md-3 q-px-md">
                    <field-display
                        :modelObject="declaration"
                        fieldId="m1RepresentativeEmail"
                        :fieldTitle="$t('email')"
                        :dense="true"
                    />
                </div>
                <!-- <div class="col-12 col-md-12 q-px-md">
                    <field-display
                        :modelObject="declaration"
                        fieldId="applicantAddressDescription"
                        :fieldTitle="$t('address')"
                        :dense="true"
                    />
                </div> -->
            </div>
            <!-- <div v-if="declaration.submittedByAuthorizedRepresentative" class="full-width row q-px-sm">  -->
            <div class="full-width row q-px-sm">
                <!-- SOS AGX -->
                <div class="col-12 col-md-4 q-px-md">
                    <field-display
                        :modelObject="declaration"
                        fieldId="m1RelStartDate8"
                        :value="declaration.m1RelStartDate8Text"
                        :fieldTitle="$t('relStartDate')"
                        :dense="true"
                    />
                </div>
                <div class="col-12 col-md-8 q-px-md">
                    <field-display
                        :modelObject="declaration"
                        fieldId="m1RelKind8Description"
                        :fieldTitle="$t('relKind')"
                        :dense="true"
                    />
                </div>
                <div class="col-12 col-md-4 q-px-md">
                    <field-display
                        :modelObject="declaration"
                        fieldId="m1RelProofDoc8Description"
                        :fieldTitle="$t('relProofDoc')"
                        :dense="true"
                    />
                </div>
                <div class="col-12 col-md-2 q-px-md">
                    <field-display
                        :modelObject="declaration"
                        fieldId="m1RelProofDocNo8"
                        :fieldTitle="$t('relProofDocNo')"
                        :dense="true"
                    />
                </div>
                <div class="col-12 col-md-2 q-px-md">
                    <field-display
                        :modelObject="declaration"
                        fieldId="m1RelProofDocDate8"
                        :value="declaration.m1RelProofDocDate8Text"
                        :fieldTitle="$t('relProofDocDate')"
                        :dense="true"
                    />
                </div>
                <div class="col-12 col-md-4 q-px-md">
                    <field-display
                        :modelObject="declaration"
                        fieldId="m1RelProofDocAuthor8"
                        :fieldTitle="$t('relProofDocAuthor')"
                        :dense="true"
                    />
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import type { PropType } from 'vue';
import { Declaration } from '@/m1Module/models/m1/Entities/Declaration';
import FieldDisplay from '@/common/components/form/FieldDisplay.vue';

export default defineComponent({
    name: 'declaration-tax-entity-data-card',
    components: {
        FieldDisplay
    },
    props: {
        declaration: {
            type: Object as PropType<Declaration | null>,
            default: null
        }
    }
});
</script>
