import { GetSubmittedDeclarationsLiteResponse } from '@/m1Module/models/m1/Services/GetSubmittedDeclarationsLite/GetSubmittedDeclarationsLiteResponse';

describe('GetSubmittedDeclarationsLiteResponse', () => {
    let response: GetSubmittedDeclarationsLiteResponse;

    beforeEach(() => {
        response = new GetSubmittedDeclarationsLiteResponse();
    });

    it('should be valid', () => {
        expect(response).toBeTruthy();
    });

    it('should have valid data', () => {
        expect(response.success).toEqual(false);
        expect(response.result).toBeFalsy();
        expect(response.message).toBeFalsy();
    });
});
