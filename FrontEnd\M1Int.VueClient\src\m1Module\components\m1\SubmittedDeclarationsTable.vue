<template>
    <div class="full-width q-ma-none q-px-xs submitted-declarations-table">
        <q-table
            :columns="tableColumns"
            :rows="filteredSubmittedDeclarations"
            row-key="id"
            v-model:pagination="pagingParams"
            @request="onRequest"
            server-side
            :pagination-label="
                (firstRowIndex, endRowIndex, totalRowsNumber) =>
                    `${firstRowIndex}-${endRowIndex} ${$t('of')} ${totalRowsNumber}`
            "
            :rows-per-page-label="$t('recordsPerPage:')"
            :rows-per-page-options="[25, 50, 75, 100]"
            :grid="$q?.screen?.width < 900"
            :dense="false"
            style="width: 100%; height: calc(100vh - 352px)"
            table-header-class="bg-primary text-white"
            class="submitted-declarations-list-table"
        >
            <template v-slot:body-cell-id="props">
                <q-td :props="props">
                    <div class="q-mx-sm row no-wrap justify-start items-center">
                        {{ getDeclarationDescriptionText(props.row) }}
                    </div>
                </q-td>
            </template>
            <template v-slot:body-cell-actions="props">
                <q-td :props="props">
                    <div class="row justify-start items-center">
                        <q-btn
                            round
                            size="sm"
                            class="q-mx-sm semi-faded"
                            color="info"
                            icon="login"
                            @click="displayDeclaration(props.row)"
                        >
                            <q-tooltip anchor="top left" self="center middle" class="pa-sm primary-dark">{{
                                $t('viewDeclaration')
                            }}</q-tooltip>
                        </q-btn>
                        <q-btn
                            round
                            size="sm"
                            class="q-mx-sm semi-faded"
                            color="info"
                            icon="article"
                            @click="printDeclaration(props.row)"
                        >
                            <q-tooltip anchor="top left" self="center middle" class="pa-sm primary-dark">{{
                                $t('printDeclaration')
                            }}</q-tooltip>
                        </q-btn>
                        <q-btn
                            v-if="props.row?.m1ReplacedBy"
                            round
                            size="sm"
                            class="q-mx-sm semi-faded"
                            color="info"
                            icon="update"
                            @click="displayDeclarationReplacedBy(props.row)"
                        >
                            <q-tooltip anchor="top left" self="center middle" class="pa-sm primary-dark">
                                {{ $t('declarationReplacedBy', [props.row?.m1ReplacedBy]) }}
                            </q-tooltip>
                        </q-btn>
                    </div>
                </q-td>
            </template>
            <template v-slot:item="props">
                <div
                    class="q-px-xs q-py-sm col-12 grid-style-transition"
                    style="min-width: 300px; display: flex; flex-grow: 1"
                >
                    <q-card bordered flat style="width: 100%; background-color: rgb(250, 250, 250)">
                        <div
                            v-for="col in props.cols.filter((col: any) => col.name !== 'actions')"
                            :key="col.name"
                            class="q-px-md"
                        >
                            <field-display
                                class="full-width"
                                :modelObject="props.row"
                                :fieldId="col.name"
                                :fieldTitle="col.label"
                                :value="col.value"
                            />
                        </div>
                        <div class="q-mb-md row justify-center items-center">
                            <q-btn
                                round
                                size="sm"
                                class="q-mx-sm semi-faded"
                                color="info"
                                icon="login"
                                @click="displayDeclaration(props.row)"
                            >
                                <q-tooltip anchor="top left" self="center middle" class="pa-sm primary-dark">{{
                                    $t('viewDeclaration')
                                }}</q-tooltip>
                            </q-btn>
                            <q-btn
                                round
                                size="sm"
                                class="q-mx-sm semi-faded"
                                color="info"
                                icon="article"
                                @click="printDeclaration(props.row)"
                            >
                                <q-tooltip anchor="top left" self="center middle" class="pa-sm primary-dark">{{
                                    $t('printDeclaration')
                                }}</q-tooltip>
                            </q-btn>
                        </div>
                    </q-card>
                </div>
            </template>
        </q-table>
    </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import DeclarationHelper from './DeclarationHelper';
import { DeclarationLite } from '@/m1Module/models/m1/Entities/DeclarationLite';
import FieldDisplay from '@/common/components/form/FieldDisplay.vue';
import { PagingParams } from '@/m1Module/models/m1/Entities/PagingParams';
import { QTableProps } from 'quasar';

export default defineComponent({
    name: 'submitted-declarations-table',
    mixins: [DeclarationHelper],
    components: {
        FieldDisplay
    },
    props: {
        filteredSubmittedDeclarations: {
            type: Array<DeclarationLite>,
            default: () => []
        }
    },
    emits: ['filterChanged'],
    computed: {
        pagingParams(): PagingParams {
            return this.store?.pagedDeclarationsFilter?.pagingParams || new PagingParams();
        },
        tableColumns(): any[] {
            return [
                {
                    name: 'id',
                    label: this.$t('declarationNumber').toString(),
                    field: 'id',
                    align: 'center',
                    sortable: true,
                    style: 'width: 120px;'
                },
                {
                    name: 'fullName',
                    label: this.$t('fullName').toString(),
                    field: 'applicantName',
                    align: 'left',
                    sortable: true,
                    style: 'white-space: break-spaces'
                },
                {
                    name: 'm1IdNumber',
                    label: this.$t('idDocument').toString(),
                    field: 'm1IdNumber',
                    align: 'left',
                    sortable: true,
                    style: 'white-space: break-spaces; width: 120px;'
                },
                {
                    name: 'm1Afm',
                    label: this.$t('afm').toString(),
                    field: 'm1Afm',
                    align: 'left',
                    sortable: true,
                    style: 'white-space: break-spaces; width: 120px;'
                },
                {
                    name: 'm1UpdatedEmpl',
                    label: this.$t('served').toString(),
                    field: 'm1UpdatedEmplText',
                    align: 'left',
                    sortable: false,
                    style: 'white-space: break-spaces'
                },
                {
                    name: 'm1SubmissionMode',
                    label: this.$t('submissionMode').toString(),
                    field: 'm1SubmissionModeText',
                    format: (val: any) => {
                        return this.$t(`submission${val}`);
                    },
                    align: 'left',
                    sortable: true,
                    style: 'white-space: break-spaces'
                },
                {
                    name: 'm1RepresentativeAfm',
                    label: this.$t('representativeAfm').toString(),
                    field: 'm1RepresentativeAfm',
                    align: 'center',
                    sortable: true,
                    style: 'white-space: break-spaces;width: 120px;'
                },
                {
                    name: 'm1Created',
                    label: this.$t('submission').toString(),
                    field: 'm1CreatedText',
                    align: 'left',
                    sortable: false,
                    style: 'white-space: break-spaces'
                },
                {
                    name: 'actions',
                    label: '',
                    align: 'center',
                    style: 'margin:0; padding: 0; width: 140px;'
                }
            ];
        }
    },
    methods: {
        displayDeclaration(declaration: DeclarationLite) {
            if (!declaration?.id) return;
            this.$router?.push(`/viewDeclaration/${declaration?.id}`);
        },
        displayDeclarationReplacedBy(declaration: DeclarationLite) {
            if (!declaration?.id || !declaration?.m1ReplacedBy) return;
            this.$router?.push(`/viewDeclaration/${declaration?.m1ReplacedBy}`);
        },
        onRequest({
            pagination
        }: {
            pagination: NonNullable<QTableProps['pagination']>;
            filter?: any;
            getCellValue: (col: any, row: any) => any;
        }) {
            this.$emit('filterChanged', new PagingParams(pagination));
        }
    }
});
</script>

<style>
.submitted-declarations-list-table thead tr th {
    position: sticky;
    z-index: 1;
}
.submitted-declarations-list-table thead tr:first-child th {
    top: 0;
}

.submitted-declarations-list-table .q-table__bottom {
    flex-wrap: nowrap !important;
    align-items: center;
    justify-content: space-between;
}

.submitted-declarations-list-table .q-table__control {
    margin-bottom: 0 !important;
}
</style>
