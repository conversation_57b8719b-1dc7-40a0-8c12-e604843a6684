import { shallowMount, VueWrapper } from '@vue/test-utils';
import HomeComponent from '@/main/components/Home.vue';
import { commonComponentMocks } from '@/mocks/commonComponentMocks';

describe('HomeComponent', () => {
    let wrapper: VueWrapper;
    const testMessage: string = 'test message';

    beforeEach(() => {
        wrapper = shallowMount(HomeComponent, {
            global: {
                mocks: commonComponentMocks
            },
            props: {
                message: testMessage
            }
        });
    });

    afterEach(() => {});

    it('should mount', () => {
        expect(wrapper).toBeTruthy();
        expect(wrapper.vm).toBeTruthy();
        expect(wrapper.vm.$el).toBeTruthy();
    });

    it('element should be valid', () => {
        expect(wrapper.vm.$el.classList).toBeTruthy();
        expect(Array.from(wrapper.vm.$el.classList)).toContain('home-component');
    });

    it('should have valid $t property', () => {
        expect(wrapper.vm.$t).toBeTruthy();
        expect(typeof wrapper.vm.$t).toEqual('function');
    });

    it('$t should return valid result', () => {
        expect(wrapper.vm.$t('HomeComponent')).toEqual('HomeComponent');
    });

    it('should have valid $alert property', () => {
        expect((wrapper.vm as any).$alert).toBeTruthy();
    });

    it('element should contain title', () => {
        expect(wrapper.text()).toContain('homeMessage');
    });

    it('should render message', () => {
        const message = wrapper.props().message;
        expect(message).toBeTruthy();
        expect(message).toEqual(testMessage);
        expect(wrapper.text()).toMatch(testMessage);
    });

    it('should render another message', async () => {
        const anotherMessage: string = 'test another message';
        await wrapper.setProps({ message: anotherMessage });
        const message = wrapper.props().message;
        expect(message).toBeTruthy();
        expect(message).toEqual(anotherMessage);
        expect(wrapper.text()).toMatch(anotherMessage);
    });
});
