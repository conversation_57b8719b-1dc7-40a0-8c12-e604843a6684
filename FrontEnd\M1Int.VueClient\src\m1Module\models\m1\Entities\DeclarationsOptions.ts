export const DEFAULT_GREECE_COUNTRY_CODE: string = 'GR';
export const DEFAULT_GREECE_COUNTRY_DESCRIPTION: string = 'ΕΛΛΑΔΑ';
export const DEFAULT_DECLARATION_MIN_YEAR: number = 2024;
export const DEFAULT_DECLARATION_ITEMS_APPROVAL_DAYS_OFFSET: number = 15;

export class AttachmentsOptions {
    public attachmentsAllowedExtensions: Array<string> = [];
    public attachmentsMaxFilesNumber: number = 0;
    public attachmentsMaxFileSize: number = 0;
    public attachmentsMaxTotalSize: number = 0;

    public constructor(options?: any) {
        options = options || {};
        this.attachmentsAllowedExtensions = options.attachmentsAllowedExtensions || options.allowedExtensions || [];
        this.attachmentsMaxFilesNumber = options.attachmentsMaxFilesNumber || options.maxFilesNumber || 0;
        this.attachmentsMaxFileSize = options.attachmentsMaxFileSize || options.maxFileSize || 0;
        this.attachmentsMaxTotalSize = options.attachmentsMaxTotalSize || options.maxTotalSize || 0;
    }
}

export class DeclarationsOptions {
    public attachments: AttachmentsOptions = new AttachmentsOptions();
    public zeroAfmAllowed: boolean = false;
    public declarationExpirationActive: boolean = true;
    public greeceCountryCode: string = DEFAULT_GREECE_COUNTRY_CODE;
    public greeceCountryDescription: string = DEFAULT_GREECE_COUNTRY_DESCRIPTION;
    public declarationMinYear: number = DEFAULT_DECLARATION_MIN_YEAR;
    public declarationMinProjectValue: number | null = null;
    public declarationProjectStartMinYear: number | null = null;
    public declarationRelatedDeclarationDescriptionRegEx: string | null = null;
    public declarationProtocolCodeActive: boolean = true;
    public declarationProtocolCodeRegEx: string | null = null;
    public declarationProtocolCodeMinYear: number | null = null;

    public constructor(options?: any) {
        options = options || {};
        if (options.attachments) this.attachments = new AttachmentsOptions(options.attachments);
        if (options.zeroAfmAllowed !== undefined && options.zeroAfmAllowed !== null)
            this.zeroAfmAllowed = options.zeroAfmAllowed;
        if (options.declarationExpirationActive !== undefined && options.declarationExpirationActive !== null)
            this.declarationExpirationActive = options.declarationExpirationActive;
        this.greeceCountryCode = options.greeceCountryCode || DEFAULT_GREECE_COUNTRY_CODE;
        this.greeceCountryDescription = options.greeceCountryDescription || DEFAULT_GREECE_COUNTRY_DESCRIPTION;
        this.declarationMinYear = options.declarationMinYear || options.minYear || DEFAULT_DECLARATION_MIN_YEAR;
        this.declarationMinProjectValue = options.declarationMinProjectValue || options.minProjectValue || null;
        this.declarationProjectStartMinYear =
            options.declarationProjectStartMinYear || options.minProjectStartYear || null;
        this.declarationRelatedDeclarationDescriptionRegEx =
            options.declarationRelatedDeclarationDescriptionRegEx || options.relatedDeclarationDescriptionRegEx || null;
        if (options.declarationProtocolCodeActive !== undefined && options.declarationProtocolCodeActive !== null)
            this.declarationProtocolCodeActive = options.declarationProtocolCodeActive;
        this.declarationProtocolCodeRegEx = options.declarationProtocolCodeRegEx || options.protocolCodeRegEx || null;
        this.declarationProtocolCodeMinYear =
            options.declarationProtocolCodeMinYear || options.protocolCodeMinYear || null;
    }
}
