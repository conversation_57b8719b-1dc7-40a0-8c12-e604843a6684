import { RequestBase } from '@/common/models/RequestBase';
import { PagingParams } from '../../Entities/PagingParams';
export class GetSubmittedDeclarationsRequest extends RequestBase {
    public id?: number | null = null;
    public m1Afm: string | null = null;
    public m1IdNumber: string | null = null;
    public m1SurNameA: string | null = null;
    public m1SurNameB: string | null = null;
    public m1Name: string | null = null;
    public m1RepresentativeAfm: string | null = null;

    public pagingParams: PagingParams | null = null;

    public constructor(options?: any) {
        super();
        options = options || {};
        this.id = options.id || null;
        this.m1Afm = options.m1Afm || null;
        this.m1IdNumber = options.m1IdNumber || null;
        this.m1SurNameA = options.m1SurNameA || null;
        this.m1SurNameB = options.m1SurNameB || null;
        this.m1Name = options.m1Name || null;
        this.m1RepresentativeAfm = options.m1RepresentativeAfm || null;

        this.pagingParams = options.pagingParams || null;
    }
}
