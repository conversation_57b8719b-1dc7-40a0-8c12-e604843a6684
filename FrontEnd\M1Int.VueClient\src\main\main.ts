import { createApp } from 'vue';
import '@/common/assets/main.css';
import Main from './Main.vue';
import { messageBus } from './messageBus';
import router from './router';
import i18n from '@/common/i18n';
import { SweetAlert } from '@/common/utilities/SweetAlert';
import { createPinia } from 'pinia';
import { Quasar } from 'quasar';
import qLangEn from 'quasar/lang/en-US';
import qLangEl from 'quasar/lang/el';
/*
import '@quasar/extras/material-icons/material-icons.css';
import '@quasar/extras/material-icons-outlined/material-icons-outlined.css';
*/
import 'quasar/src/css/index.sass';
import config from '@/config';

const pinia = createPinia();
const app = createApp(Main);

app.config.globalProperties.$messageBus = messageBus;

app.use(i18n)
    .use(SweetAlert)
    .use(pinia)
    .use(Quasar, {
        // lang: i18n.global.locale === 'el' ? qLangEl : qLangEn,
        lang: config.locale === 'el' ? qLangEl : qLangEn,
        plugins: {} // import Quasar plugins and add here
    })
    .use(router);

app.mount('#app');
