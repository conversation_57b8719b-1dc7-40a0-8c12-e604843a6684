import { Store, defineStore } from 'pinia';
import { State } from './State';
import { IAppService, AppService } from '@/common/services/AppService';
import { UserAuthData } from '@/auth/models/Auth';
import packageInfo from '../../../package.json';

type CommonStoreType = Store<'commonStore', State>;

export interface ICommonStore extends CommonStoreType {
    applicationName: string;
    applicationVersion: string;
    serverCurrentDateTime: Date | null;
    authData: UserAuthData | null;

    clearServerCurrentDateTime(): void;
    setServerCurrentDateTime(serverCurrentDateTime: Date | null): void;
    clearAuthData(): void;
    setAuthData(authData: UserAuthData | null): void;

    getServerCurrentDateTime(): Promise<Date | null>;
}

export function getCommonStore(service?: IAppService): ICommonStore {
    service = service || new AppService();
    const state = new State();
    const useStore = defineStore('commonStore', {
        state: () => ({ ...state }),
        getters: {
            applicationName(/* state: State */) {
                return packageInfo.name;
            },
            applicationVersion(/* state: State */) {
                return packageInfo.version;
            },
            serverCurrentDateTime(state: State) {
                return state._serverCurrentDateTime;
            },
            authData(state: State) {
                return state._authData;
            }
        },
        actions: {
            clearServerCurrentDateTime() {
                this.$patch({ _serverCurrentDateTime: null });
            },
            setServerCurrentDateTime(serverCurrentDateTime: Date | null) {
                if (serverCurrentDateTime) {
                    this.$patch({ _serverCurrentDateTime: serverCurrentDateTime });
                } else {
                    this.clearServerCurrentDateTime();
                }
            },
            clearAuthData() {
                this.$patch({ _authData: null });
            },
            setAuthData(authData: UserAuthData | null) {
                if (authData) {
                    this.$patch({ _authData: authData });
                } else {
                    this.$patch({ _authData: null });
                }
            },
            async getServerCurrentDateTime(): Promise<Date | null> {
                try {
                    if (!service) {
                        this.clearServerCurrentDateTime();
                        return this.serverCurrentDateTime;
                    }
                    const response: any = await service.getServerCurrentDateTime();
                    this.setServerCurrentDateTime(response);
                    return this.serverCurrentDateTime;
                } catch (ex: any) {
                    console.error(ex);
                    throw ex;
                }
            }
        }
    });
    const store = useStore();
    return store as ICommonStore;
}
