<template>
    <q-option-group
        type="radio"
        :id="elementId"
        :for="elementId"
        :name="elementName"
        v-model="modelObject[fieldId]"
        :error-message="validationMessage"
        :error="isDirty && isInvalid"
        :noErrorIcon="true"
        :class="classResolved"
        :label="label"
        :placeholder="placeholder"
        :hint="hint"
        :hideHint="hideHint"
        :outlined="outlined"
        :filled="filled"
        :borderless="borderless"
        :rounded="rounded"
        :dense="dense"
        :disable="disabled"
        :readonly="readonly"
        :inline="inline"
        :options="mappedOptions"
        :leftLabel="leftLabel"
        :color="color"
        :keepColor="!!color"
        @update:model-value="valueChanged"
    />
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import FieldCommon from './FieldCommon';

export default defineComponent({
    name: 'field-edit-radio',
    mixins: [FieldCommon],
    props: {
        options: {
            type: Array<any>,
            default: () => []
        },
        optionLabel: {
            type: String,
            default: 'label'
        },
        optionValue: {
            type: String,
            default: 'value'
        },
        optionColor: {
            type: String,
            default: null
        },
        returnObject: {
            type: Boolean,
            default: false
        },
        inline: {
            type: Boolean,
            default: true
        },
        color: {
            type: String,
            default: null
        },
        leftLabel: {
            type: Boolean,
            default: false
        }
    },
    computed: {
        mappedOptions(): Array<any> {
            if (!this.options?.length) return [];
            return this.options.map((x: any) => {
                const res: any = {
                    value: this.returnObject ? x : x[this.optionValue],
                    label: x[this.optionLabel]
                };
                if (this.optionColor && x[this.optionColor]) {
                    res.color = x[this.optionColor];
                } else if (this.color) {
                    res.color = this.color;
                }
                return res;
            });
        }
    }
});
</script>
