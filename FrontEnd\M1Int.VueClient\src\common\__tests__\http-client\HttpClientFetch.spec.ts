import { HttpClientFetch } from '@/common/http-client/HttpClientFetch';

describe('HttpClientFetch', () => {
    let client: HttpClientFetch;

    beforeEach(() => {
        client = new HttpClientFetch();
    });

    it('client is valid', () => {
        expect(client).toBeTruthy();
    });

    it('client has baseurl property', () => {
        expect(client.baseUrl).not.toBeUndefined();
        expect(client.baseUrl).not.toBeNull();
    });
});
