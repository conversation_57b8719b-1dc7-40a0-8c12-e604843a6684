import { GetDeclarationsOptionsResponse } from '@/m1Module/models/m1/Services/GetDeclarationsOptions/GetDeclarationsOptionsResponse';

describe('GetDeclarationsOptionsResponse', () => {
    let response: GetDeclarationsOptionsResponse;

    beforeEach(() => {
        response = new GetDeclarationsOptionsResponse();
    });

    it('should be valid', () => {
        expect(response).toBeTruthy();
    });

    it('should have valid data', () => {
        expect(response.success).toEqual(false);
        expect(response.result).toBeFalsy();
        expect(response.message).toBeFalsy();
    });
});
