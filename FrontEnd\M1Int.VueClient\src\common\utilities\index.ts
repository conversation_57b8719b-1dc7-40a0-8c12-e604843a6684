import { v4 } from 'uuid';
import dayjs from '@/plugins/dayjs';
import { Router } from 'vue-router';
import { Dayjs } from 'dayjs';
import config from '@/config';

export function getNewUID(): string {
    return v4();
}

export function roundNumber(value: number | null, decimals: number = 2): number {
    if (!value) return 0;
    const factor: number = Math.pow(10, decimals);
    return Math.round(value * factor) / factor;
}

export function getLocaleDecimalSeparator(locale: string = config.locale): string {
    const numberFormat: Intl.NumberFormat = new Intl.NumberFormat(locale);
    const parts: Array<any> = numberFormat.formatToParts(123456.789);
    const sep: string = parts.find((d) => d.type === 'decimal').value;
    return sep;
}

export function getLocaleGroupSeparator(locale: string = config.locale): string {
    const numberFormat: Intl.NumberFormat = new Intl.NumberFormat(locale);
    const parts: Array<any> = numberFormat.formatToParts(123456.789);
    const sep: string = parts.find((d) => d.type === 'group').value;
    return sep;
}

export function getLocaleNumberText(numberValue: Number, decimals: number = 2, locale: string = config.locale) {
    const res: string = numberValue.toLocaleString(locale, {
        minimumFractionDigits: decimals,
        maximumFractionDigits: decimals
    });
    return res;
}

export function getRandomInt(max: number) {
    return Math.floor(Math.random() * max);
}

export function getRandomCollectionItem(collection: Array<any>): any {
    if (!collection?.length) return null;
    return collection[getRandomInt(collection.length)];
}

export function getMaskedText(text: string, fillChar: string = '*'): string {
    if (!text) return '';
    const value: string = text.trim();
    if (value.length < 3) return value;
    const items: Array<string> = value.split(' ');
    const maskedItems: Array<string> = items.map((x: string) => {
        if (x.length < 3) return x;
        const m: string = `${x[0]}${''.padEnd(x.length - 2, fillChar)}${x[x.length - 1]}`;
        return m;
    });
    const res: string = maskedItems.join(' ');
    return res;
}

export function sleep(millis: number): Promise<any> {
    const resPromise = new Promise((resolve /*, reject */) => {
        setTimeout(() => resolve(millis), millis);
    });
    return resPromise;
}

export function isNetworkOnLine(): boolean {
    return window?.navigator?.onLine;
}

export function isNetworkOffLine(): boolean {
    return !window?.navigator?.onLine;
}

export function getRootUrl(): string {
    if (!location) return '';
    return `${location.protocol}//${location.host}`;
}

export function goToUrl(options?: any): Promise<boolean> {
    options = options || {};
    options.url = options.url || '/';
    options.timeout = options.timeout || 0;
    options.reload = options.reload || false;
    if (
        environVars?.applicationRoot &&
        options.url.startsWith('/') &&
        !options.url.startsWith(environVars?.applicationRoot)
    ) {
        if (options.url.startsWith('/')) options.url = options.url.substring(1);
        options.url = `${environVars?.applicationRoot}/${options.url}`;
    }
    if (window && window.location.href != options.url) {
        return new Promise((resolve /*, reject */) => {
            setTimeout(() => {
                window.location.href = options.url;
                return resolve(true);
            }, options.timeout);
        });
    } else {
        if (options.reload) {
            return new Promise((resolve /*, reject */) => {
                setTimeout(() => {
                    window.location.reload();
                    return resolve(true);
                }, options.timeout);
            });
        } else {
            return Promise.resolve(true);
        }
    }
}

export function openNewUrl(options?: any): Promise<boolean> {
    options = options || {};
    options.url = options.url || '/';
    options.timeout = options.timeout || 0;
    if (
        environVars?.applicationRoot &&
        options.url.startsWith('/') &&
        !options.url.startsWith(environVars?.applicationRoot)
    ) {
        if (options.url.startsWith('/')) options.url = options.url.substring(1);
        options.url = `${environVars?.applicationRoot}/${options.url}`;
    }
    return new Promise((resolve /*, reject */) => {
        setTimeout(() => {
            window.open(options.url, '_blank');
            return resolve(true);
        }, options.timeout);
    });
}

export function resumeVueRouterQuery(router: Router, route: any, query: any) {
    if (!router || !route || !route.query) return;
    const curQueryJson = JSON.stringify(route.query)?.toLowerCase() || '';
    const newQueryJson = JSON.stringify(query)?.toLowerCase() || '';
    if (newQueryJson !== curQueryJson) {
        router.replace({ query }).catch((ex) => ex);
    }
}

export function getNoIntonation(value: string | undefined | null): string {
    if (!value) return '';
    const newValue: string = value
        .replace(/Ά/g, 'Α')
        .replace(/Έ/g, 'Ε')
        .replace(/Ή/g, 'Η')
        .replace(/Ί/g, 'Ι')
        .replace(/Ϊ/g, 'Ι')
        .replace(/΅Ι/g, 'Ι')
        .replace(/Ό/g, 'Ο')
        .replace(/Ύ/g, 'Υ')
        .replace(/Ϋ/g, 'Υ')
        .replace(/΅Υ/g, 'Υ')
        .replace(/Ώ/g, 'Ω')
        .replace(/ά/g, 'α')
        .replace(/έ/g, 'ε')
        .replace(/ή/g, 'η')
        .replace(/ί/g, 'ι')
        .replace(/ϊ/g, 'ι')
        .replace(/ΐ/g, 'ι')
        .replace(/ό/g, 'ο')
        .replace(/ύ/g, 'υ')
        .replace(/ϋ/g, 'υ')
        .replace(/ΰ/g, 'υ')
        .replace(/ώ/g, 'ω');
    return newValue;
}

export function validNumber(value: any) {
    if (value === undefined || value === null) return true;
    try {
        const numValue = Number(value);
        return !isNaN(numValue);
    } catch {
        return false;
    }
}

export function validDateTime(format: string) {
    return function validDate(value: any) {
        if (!value) return true;
        if (value instanceof Date) {
            value = dayjs(value).format(format);
        }
        const dt = dayjs(value, format, true);
        if (!dt.isValid()) return false;
        return true;
    };
}

export function validTime(value: any) {
    if (!value) return true;
    const tParts: string[] = value.split(':');
    if (tParts.length < 2 || tParts.length > 3) return false;
    if (tParts.some((x) => x.length < 2 || x.length > 2)) {
        return false;
    }
    const h: number = parseInt(tParts[0].trim());
    if (isNaN(h) || h < 0 || h >= 24) return false;
    const m: number = parseInt(tParts[1].trim());
    if (isNaN(m) || m < 0 || m >= 60) return false;
    if (tParts.length > 2) {
        const s: number = parseInt(tParts[2].trim());
        if (isNaN(s) || s < 0 || s >= 60) return false;
    }
    return true;
}

export function dateTimeInRange(from: any, upto: any) {
    return function dtInRange(value: any) {
        if (!value) return true;
        const dt: Dayjs = dayjs(value);
        if (from) {
            const fromDt: Dayjs = dayjs(from);
            if (dt.isBefore(fromDt)) return false;
        }
        if (upto) {
            const uptoDt: Dayjs = dayjs(upto);
            if (dt.isAfter(uptoDt)) return false;
        }
        return true;
    };
}

export function validAFM(value: string): boolean {
    if (value) value = value.trim();
    if (!value) return true;
    if (value.length !== 9) return false;
    const checkDigit: number = value.charCodeAt(8) - 48;
    let sum = 0;
    for (let i = 7; i >= 0; i--) {
        sum += (value.charCodeAt(i) - 48) << (8 - i);
    }
    let mod: number = sum % 11;
    if (mod === 10) mod = 0;
    return mod === checkDigit;
}

export function saveFile(url: string, filename: string) {
    if (!filename) filename = `file-${dayjs().format('YYYY-MM-DD_HH-mm-ss')}`;
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
}
