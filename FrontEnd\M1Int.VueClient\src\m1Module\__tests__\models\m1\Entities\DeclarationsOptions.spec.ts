import { DeclarationsOptions, DEFAULT_DECLARATION_MIN_YEAR } from '@/m1Module/models/m1/Entities/DeclarationsOptions';

describe('DeclarationsOptions', () => {
    let entity: DeclarationsOptions;

    beforeEach(() => {
        entity = new DeclarationsOptions();
    });

    it('should be valid', () => {
        expect(entity).toBeTruthy();
    });

    it('should have valid declarationMinYear', () => {
        expect(entity?.declarationMinYear).toEqual(DEFAULT_DECLARATION_MIN_YEAR);
    });
});
