import jsLint from '@eslint/js';
import vueLint from 'eslint-plugin-vue';
import {
    defineConfigWithVueTs,
    vueTsConfigs,
    configureVueProject
} from '@vue/eslint-config-typescript';

configureVueProject({
    tsSyntaxInTemplates: true,
    scriptLangs: ['ts', 'tsx']
});

export default defineConfigWithVueTs(
    jsLint.configs.recommended,
    vueLint.configs['flat/essential'],
    vueTsConfigs.recommended,
    {
        ignores: [
            '.prettierrc.js',
            '**/node_modules/*',
            '**/dist/*',
            '**/playwright-report/*',
            '**/test-results/*',
            '**/coverage/*'
        ]
    },
    {
        rules: {
            indent: ['warn', 4, { SwitchCase: 1, ObjectExpression: 'off' }],
            'vue/script-indent': ['warn', 4, { baseIndent: 0 }],
            'comma-dangle': ['warn', 'never'],
            quotes: ['warn', 'single', { allowTemplateLiterals: true }],
            semi: ['error', 'always'],
            'no-console': 'off',
            'no-debugger': 'warn',
            'no-prototype-builtins': 'off',
            'no-useless-escape': 'off',
            '@typescript-eslint/no-empty-function': 'off',
            '@typescript-eslint/no-explicit-any': 'off',
            '@typescript-eslint/no-non-null-asserted-optional-chain': 'off',
            '@typescript-eslint/ban-types': 'off',
            '@typescript-eslint/no-inferrable-types': 'off',
            '@typescript-eslint/no-var-requires': 'off',
            '@typescript-eslint/no-empty-interface': 'off',
            '@typescript-eslint/no-unsafe-function-type': 'off',
            '@typescript-eslint/no-unused-vars': 'off',
            '@typescript-eslint/no-empty-object-type': 'off',
            // '@typescript-eslint/no-unused-expressions': 'off',
            '@typescript-eslint/no-wrapper-object-types': 'off',
            'vue/no-mutating-props': 'off'
        }
    }
);
