import { IHttpClient } from '@/common/http-client/IHttpClient';
import { createHttpClient } from '@/common/http-client/HttpClientFactory';
import { LoginModel, UserAuthData } from '../models/Auth';
import authStore from './AuthStore';
import config from '@/config';

const httpClient: IHttpClient = createHttpClient();

export class AuthService {
    private static _instance: AuthService | null = null;
    private _authData: UserAuthData | null = null;

    /*
    constructor() {
        this.loadAuthData();
    }
    */

    public static get instance(): AuthService {
        if (!AuthService._instance) {
            AuthService._instance = new AuthService();
        }
        return AuthService._instance;
    }

    public async login(userName: string, password: string): Promise<UserAuthData> {
        try {
            const isExternalProvider: boolean = config.auth?.externalProvider?.enabled == true;
            if (!isExternalProvider && (!userName || !password)) {
                throw new Error('Invalid user credentials!');
            }
            this._authData = null;
            const url = config.USE_SERVER_SESSION ? 'session/CreateToken' : 'auth/CreateToken';
            const userCredentials: LoginModel = new LoginModel({ userName, password, isExternalProvider });
            const response: any = await httpClient.post<any, LoginModel>(url, userCredentials);
            if (!response?.success || !response?.result) throw new Error('User authentication failed');
            this._authData = new UserAuthData(response.result);
            if (!config.USE_SERVER_SESSION) {
                await this.storeAuthData();
                const authData = await authStore.loadAuthData();
                if (authData) {
                    this._authData = authData;
                }
            }
            return this._authData;
        } catch (ex: any) {
            console.error(ex);
            this._authData = null;
            throw ex;
        }
    }

    public async logout(): Promise<string | null> {
        this._authData = await authStore.loadAuthData();
        await this.clearAuthData();
        if (config.auth?.externalProvider?.enabled == true) {
            if (config.auth?.externalProvider?.logoutUrl) return config.auth?.externalProvider?.logoutUrl;
        }
        return null;
    }

    public async checkLoadAuthData() {
        await authServiceDataLoaded;
        if (this.authData?.userName) return;
        await this.loadAuthData(false);
    }

    public async loadAuthData(checkDoLogin: boolean = true): Promise<UserAuthData | null> {
        if (checkDoLogin) {
            try {
                const isExternalProvider: boolean = config.auth?.externalProvider?.enabled == true;
                if (
                    isExternalProvider &&
                    config.auth?.externalProvider?.simulateExternalProviderUserIdHeader !== true
                ) {
                    await this.login('', '');
                }
            } catch (ex) {
                console.error(ex);
            }
        }
        this._authData = await authStore.loadAuthData();
        if (this._authData?.hasExpired()) await this.clearAuthData();
        return this._authData;
    }

    private async storeAuthData() {
        try {
            await authStore.storeAuthData(this._authData);
        } catch (ex: any) {
            await this.clearAuthData();
        }
    }

    public async clearAuthData() {
        this._authData = null;
        await authStore.clearAuthData();
    }

    public get authData(): UserAuthData | null {
        return this._authData;
    }

    public get userName(): string {
        return this.authData?.userName || '';
    }

    public get userEmail(): string {
        return this.authData?.userEmail || '';
    }

    public get userAuthenticated(): boolean {
        const authData = this.authData;
        if (authData && authData?.userName) {
            return true;
        } else {
            return false;
        }
    }

    public get authHasExpired(): boolean {
        const authData = this.authData;
        if (authData) {
            return authData.hasExpired();
        } else {
            return false;
        }
    }

    public async checkFixAuthExpired(): Promise<boolean> {
        if (!this._authData || !this.authHasExpired) return false;
        await this.logout();
        return true;
    }
}

const authService: AuthService = AuthService.instance;
export const authServiceDataLoaded: Promise<UserAuthData | null> = authService.loadAuthData();
export default authService;
