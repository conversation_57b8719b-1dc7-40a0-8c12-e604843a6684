import { shallowMount, VueWrapper } from '@vue/test-utils';
import { setActivePinia, createPinia } from 'pinia';
import SubmittedDeclarationsView from '@/m1Module/views/m1/SubmittedDeclarations.vue';
import { commonComponentMocks, commonComponentPlugins } from '@/mocks/commonComponentMocks';
import { getM1Store, IM1Store } from '@/m1Module/stores/M1Store';
import { MockM1Service } from '@/mocks/MockM1Service';
import { ICommonStore, getCommonStore } from '@/common/stores/Store';
import { MockAppService } from '@/mocks/MockAppService';

describe('SubmittedDeclarationsView', () => {
    let wrapper: VueWrapper;
    let commonStore: ICommonStore | null = null;
    let m1Store: IM1Store | null = null;

    beforeEach(() => {
        setActivePinia(createPinia());
        commonStore = getCommonStore(new MockAppService());
        m1Store = getM1Store(new MockM1Service());
        wrapper = shallowMount(SubmittedDeclarationsView, {
            global: {
                mocks: commonComponentMocks,
                plugins: commonComponentPlugins
            },
            props: {
                commonStore: commonStore,
                store: m1Store
            }
        });
    });

    afterEach(() => {});

    it('should mount', () => {
        expect(wrapper).toBeTruthy();
        expect(wrapper.vm).toBeTruthy();
        expect(wrapper.vm.$el).toBeTruthy();
    });

    it('element should be valid', () => {
        expect(wrapper.vm.$el.classList).toBeTruthy();
        expect(Array.from(wrapper.vm.$el.classList)).toContain('submitted-declarations');
    });

    it('should have valid $t property', () => {
        expect(wrapper.vm.$t).toBeTruthy();
        expect(typeof wrapper.vm.$t).toEqual('function');
    });

    it('$t should return valid result', () => {
        expect(wrapper.vm.$t('SubmittedDeclarationsView')).toEqual('SubmittedDeclarationsView');
    });

    it('should have valid $alert property', () => {
        expect((wrapper.vm as any).$alert).toBeTruthy();
    });
});
