import { shallowMount, VueWrapper } from '@vue/test-utils';
import mockAlert from '@/mocks/MockAlert';
import FieldEditDateTime from '@/common/components/form/FieldEditDateTime.vue';
import { Component } from 'vue';

describe('FieldEditDateTime', () => {
    let wrapper: VueWrapper;
    let component: Component;

    const model: any = {
        id: '001',
        eventAt: new Date()
    };

    beforeEach(() => {
        wrapper = shallowMount(FieldEditDateTime, {
            global: {
                mocks: {
                    $t: (key: string) => {
                        return key;
                    },
                    $alert: mockAlert
                }
            },
            props: {
                modelObject: model,
                fieldId: 'eventAt',
                fieldTitle: 'Event date'
            }
        });
        component = wrapper.vm as Component;
    });

    afterEach(() => {});

    it('should mount', () => {
        expect(wrapper).toBeTruthy();
        expect(wrapper.vm).toBeTruthy();
        expect(wrapper.vm.$el).toBeTruthy();
    });

    it('component should be valid', () => {
        expect(component).toBeTruthy();
    });

    it('should have valid $t property', () => {
        expect(wrapper.vm.$t).toBeTruthy();
        expect(typeof wrapper.vm.$t).toEqual('function');
    });

    it('should have valid $alert property', () => {
        expect((wrapper.vm as any).$alert).toBeTruthy();
    });

    it('should contain field date label', async () => {
        expect(wrapper.attributes('label')).toContain((component as any).fieldTitle);
    });

    it('should have valid field date value', async () => {
        expect((component as any).getValue()).toEqual(model.eventAt);
    });

    it('should update field date value', async () => {
        const newDate = new Date();
        expect((component as any).getValue()).toEqual(model.eventAt);
        model.eventAt = newDate;
        expect((component as any).getValue()).toEqual(newDate);
    });
});
