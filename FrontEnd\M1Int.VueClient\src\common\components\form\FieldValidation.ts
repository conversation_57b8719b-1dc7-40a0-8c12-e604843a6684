import { defineComponent } from 'vue';
import type { PropType } from 'vue';
import { Validation } from '@vuelidate/core';

export default defineComponent({
    name: 'field-validation',
    props: {
        fieldId: {
            type: String,
            default: ''
        },
        fieldTitle: {
            type: String,
            default: ''
        },
        validationObject: {
            type: Object as PropType<Validation>,
            default: null
        },
        customValidators: {
            type: Array<string>,
            default: []
        }
    },
    computed: {
        targetValidationObject(): any {
            if (this.fieldId) return this.validationObject[this.fieldId];
            else return this.validationObject;
        },
        isDirty(): boolean {
            if (!this.validationObject) return false;
            return this.targetValidationObject?.$dirty;
        },
        isValid(): boolean {
            if (!this.validationObject) return true;
            if (this.targetValidationObject?.$error !== undefined) return !this.targetValidationObject?.$error;
            else if (this.targetValidationObject?.$invalid !== undefined) return !this.targetValidationObject?.$invalid;
            return true;
        },
        isInvalid(): boolean {
            if (!this.validationObject) return false;
            if (this.targetValidationObject?.$error !== undefined) return this.targetValidationObject?.$error;
            else if (this.targetValidationObject?.$invalid !== undefined) return this.targetValidationObject?.$invalid;
            return false;
        },
        validationMessage(): string {
            if (!this.validationObject || !this.fieldId) return '';
            const validationField: any = this.validationObject[this.fieldId];
            if (!validationField) return '';
            if (this.isDirtyWithValidationError('required')) {
                return this.$t('fieldIsRequired', [this.fieldTitle]);
            }
            if (this.isDirtyWithValidationError('minLength')) {
                return this.$t('fieldMinLength', [this.fieldTitle, validationField.minLength.$params.min]);
            }
            if (this.isDirtyWithValidationError('maxLength')) {
                return this.$t('fieldMaxLength', [this.fieldTitle, validationField.maxLength.$params.max]);
            }
            if (this.isDirtyWithValidationError('minValue')) {
                return this.$t('fieldMinValue', [this.fieldTitle, validationField.minValue.$params.min]);
            }
            if (this.isDirtyWithValidationError('maxValue')) {
                return this.$t('fieldMaxValue', [this.fieldTitle, validationField.maxValue.$params.max]);
            }
            if (this.customValidators.length) {
                for (let i = 0; i < this.customValidators.length; i++) {
                    const customValidatorId = this.customValidators[i];
                    if (this.isDirtyWithValidationError(customValidatorId)) {
                        const customValidator: any = validationField[customValidatorId];
                        if (customValidator.$message) return customValidator.$message;
                        else return this.$t('fieldIsInvalid', [this.fieldTitle]);
                    }
                }
            }
            if (this.isDirtyWithValidationError()) {
                return this.$t('fieldIsInvalid', [this.fieldTitle]);
            }
            return '';
        },
        validationClass(): string {
            let res: string = '';
            if (this.isDirty) res += ' dirty';
            if (this.isInvalid) res += ' invalid';
            if (this.isValid) res += ' valid';
            return res.trim();
        }
    },
    methods: {
        hasValidationError(validator: any = null, isDirty: boolean = false): boolean {
            if (!this.validationObject) return false;
            if (isDirty) {
                if (!this.isDirty) return false;
            }
            if (this.isValid) return false;
            if (!validator) return true;
            if (this.fieldId) {
                let validationField: any = null;
                if (this.validationObject.$errors)
                    validationField = (this.validationObject.$errors as any)[this.fieldId];
                if (validationField) {
                    if (validationField.length <= 0) return false;
                    const index = validationField.findIndex((x: any) => x.$validator === validator);
                    return index >= 0;
                } else {
                    validationField = this.validationObject[this.fieldId];
                    if (!validationField) return false;
                    if (!validationField[validator]) return false;
                    return validationField[validator].$invalid;
                }
            } else {
                if (!this.validationObject[validator]) return false;
                return this.validationObject[validator].$invalid;
            }
        },
        isDirtyWithValidationError(validator: any = null): boolean {
            return this.hasValidationError(validator, true);
        },
        onFieldValidationChanged(): void {
            if (!this.validationObject || this.validationObject.$pending) return;
            if (!this.targetValidationObject.$pending && !this.targetValidationObject.$dirty) {
                if (this.targetValidationObject.$touch) this.targetValidationObject.$touch();
                else this.targetValidationObject.$dirty = true;
            }
        }
    }
});
