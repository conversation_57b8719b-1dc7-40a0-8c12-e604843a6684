import { ExportSubmittedDeclarationsToCsvResponse } from '@/m1Module/models/m1/Services/ExportSubmittedDeclarationsToCsv/ExportSubmittedDeclarationsToCsvResponse';

describe('ExportSubmittedDeclarationsToCsvResponse', () => {
    let response: ExportSubmittedDeclarationsToCsvResponse;

    beforeEach(() => {
        response = new ExportSubmittedDeclarationsToCsvResponse();
    });

    it('should be valid', () => {
        expect(response).toBeTruthy();
    });

    it('should have valid data', () => {
        expect(response.success).toEqual(false);
        expect(response.result).toBeFalsy();
        expect(response.message).toBeFalsy();
    });
});
