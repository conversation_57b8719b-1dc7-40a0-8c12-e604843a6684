<template>
    <q-input
        :id="elementId"
        :for="elementId"
        :name="elementName"
        v-model="inputValue"
        :error-message="validationMessage"
        :error="isDirty && isInvalid"
        :noErrorIcon="true"
        :type="typeResolved"
        :class="classResolved"
        :label="label"
        :placeholder="placeholder"
        :hint="hint"
        :hideHint="hideHint"
        :mask="mask"
        :fillMask="fillMask"
        :maxlength="maxLength"
        :autogrow="autogrow"
        :outlined="outlined"
        :filled="filled"
        :borderless="borderless"
        :rounded="rounded"
        :bottomSlots="bottomSlots"
        :hideBottomSpace="!bottomSlots"
        :dense="dense"
        :clearable="clearable && !disabled && !readonly"
        clear-icon="close"
        :disable="disabled"
        :readonly="readonly"
        :autofocus="autofocus && !disabled && !readonly"
        @keypress="keyPressed"
        @update:model-value="valueUpdated"
        @clear="checkValueChanged"
        @blur="checkValueChanged"
    >
        <template v-slot:label>
            <field-label :label="label" :hint="labelInfo" :isTooltip="isLabelInfoTooltip" />
        </template>
        <template v-slot:before v-if="beforeIcon">
            <q-icon :name="beforeIcon" @click="emitEvent('click:before')">
                <q-tooltip v-if="beforeIconTooltip" anchor="top middle" self="center middle">
                    {{ beforeIconTooltip }}
                </q-tooltip>
            </q-icon>
        </template>
        <template v-slot:after v-if="afterIcon">
            <q-icon :name="afterIcon" @click="emitEvent('click:after')">
                <q-tooltip v-if="afterIconTooltip" anchor="top middle" self="center middle">
                    {{ afterIconTooltip }}
                </q-tooltip>
            </q-icon>
        </template>
        <template v-slot:prepend v-if="prependIcon">
            <q-icon :name="prependIcon" @click="emitEvent('click:prepend')">
                <q-tooltip v-if="prependIconTooltip" anchor="top middle" self="center middle">
                    {{ prependIconTooltip }}
                </q-tooltip>
            </q-icon>
        </template>
        <template v-slot:append v-if="appendIcon">
            <q-icon :name="appendIcon" @click="emitEvent('click:append')">
                <q-tooltip v-if="appendIconTooltip" anchor="top middle" self="center middle">
                    {{ appendIconTooltip }}
                </q-tooltip>
            </q-icon>
        </template>
    </q-input>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import FieldEditTextCommon from './FieldEditTextCommon';
import { getLocaleDecimalSeparator, getLocaleGroupSeparator, validNumber } from '@/common/utilities';

const enterKeyCode: string = 'Enter';
const numpadDecimalCode: string = 'NumpadDecimal';
const digitsKeyCodes: Array<string> = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
const minusKeyCodes: Array<string> = ['-'];
const plusKeyCodes: Array<string> = ['+'];
const signsKeyCodes: Array<string> = [...minusKeyCodes, ...plusKeyCodes];
const separatorsKeyCodes: Array<string> = ['.', ','];

export default defineComponent({
    name: 'field-edit-number',
    mixins: [FieldEditTextCommon],
    props: {
        decimals: {
            type: Number,
            default: 0
        }
    },
    data() {
        const dataObj: {
            groupSeparator: string;
            decimalSeparator: string;
            inputValue: string | null;
        } = {
            groupSeparator: getLocaleGroupSeparator(this.locale),
            decimalSeparator: getLocaleDecimalSeparator(this.locale),
            inputValue: null
        };
        return dataObj;
    },
    methods: {
        resumeSeparators() {
            this.groupSeparator = getLocaleGroupSeparator(this.locale);
            this.decimalSeparator = getLocaleDecimalSeparator(this.locale);
        },
        resumeInputValue() {
            this.inputValue = null;
            const fieldValue: any = this.getValue();
            if (fieldValue === undefined || fieldValue === null) return;
            const numberValue = Number(fieldValue);
            if (isNaN(numberValue)) return;
            this.inputValue = numberValue.toLocaleString(this.locale, {
                minimumFractionDigits: this.decimals,
                maximumFractionDigits: this.decimals
            });
        },
        setFieldValue(value: number | null, resumeInputValue: boolean = true) {
            if (this.modelObject[this.fieldId] !== value) {
                this.modelObject[this.fieldId] = value;
            }
            if (resumeInputValue) this.resumeInputValue();
        },
        getInputNumberValue(): number | null {
            if (this.inputValue == undefined || this.inputValue === null || this.inputValue.length <= 0) {
                return null;
            }
            const groupReplaceStr = this.groupSeparator === '.' ? `\\${this.groupSeparator}` : this.groupSeparator;
            const decimalReplaceStr =
                this.decimalSeparator === '.' ? `\\${this.decimalSeparator}` : this.decimalSeparator;
            const value: string = this.inputValue
                .replace(new RegExp(groupReplaceStr, 'g'), '')
                .replace(new RegExp(decimalReplaceStr, 'g'), '.');
            if (value === undefined || value === null || value?.toString().length <= 0) {
                return null;
            }
            let numValue: number | null = null;
            if (validNumber(value)) {
                numValue = Number(Number(value).toFixed(this.decimals));
                if (isNaN(numValue)) numValue = null;
            }
            return numValue;
        },
        keyPressed(event: KeyboardEvent) {
            if (event.altKey || event.ctrlKey || event.metaKey) {
                event.preventDefault();
                return false;
            }

            // sign ('-', '+')
            if (signsKeyCodes.includes(event.key)) {
                if (!this.inputValue?.length) return true;
            }

            if (event.key === enterKeyCode) {
                this.processFieldValue();
                this.valueChanged();
                return true;
            }

            // digits ('0'..'9')
            if (digitsKeyCodes.includes(event.key)) {
                return true;
            }

            // groups and decimals separators ('.' , ',')
            if (separatorsKeyCodes.includes(event.key)) {
                if (event.code === numpadDecimalCode || event.key === this.decimalSeparator) {
                    if (
                        this.decimals > 0 &&
                        this.inputValue?.length &&
                        !this.inputValue.includes(this.decimalSeparator)
                    ) {
                        this.inputValue += this.decimalSeparator;
                    }
                } else if (event.key === this.groupSeparator) {
                    if (
                        this.inputValue &&
                        !this.inputValue.endsWith(this.groupSeparator) &&
                        !this.inputValue.includes(this.decimalSeparator)
                    ) {
                        return true;
                    }
                }
                event.preventDefault();
                return false;
            }

            event.preventDefault();
            return false;
        },
        valueUpdated() {
            if (this.inputValue === undefined || this.inputValue === null || !this.inputValue?.length) {
                return this.valueChanged();
            }
            this.$nextTick(this.fixTabItems);
        },
        checkValueChanged(): boolean {
            const curValue = this.getValue();
            const newValue: number | null = this.getInputNumberValue();
            if (newValue !== curValue) {
                this.processFieldValue();
                this.valueChanged();
                return true;
            }
            return false;
        },
        processFieldValue() {
            const numValue: number | null = this.getInputNumberValue();
            if (numValue === null) {
                this.setFieldValue(null, false);
            } else {
                this.setFieldValue(numValue);
            }
        }
    },
    created() {
        this.resumeInputValue();
    },
    beforeMount() {
        this.resumeSeparators();
    }
});
</script>
